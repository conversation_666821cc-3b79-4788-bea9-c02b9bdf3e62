#!/bin/bash

# 触发GitHub Actions部署的测试脚本

set -e

echo "🚀 触发GitHub Actions自动部署测试"
echo "================================"

# 检查当前状态
echo ""
echo "📋 当前状态:"
echo "   分支: $(git branch --show-current)"
echo "   最新提交: $(git log --oneline -1)"
echo "   远程状态: $(git status -uno | grep -E "up to date|ahead|behind" || echo "需要检查")"

# 检查是否有未提交的更改
if ! git diff-index --quiet HEAD --; then
    echo ""
    echo "⚠️ 检测到未提交的更改:"
    git status --porcelain
    echo ""
    read -p "是否要提交这些更改? (y/N): " commit_changes
    
    if [[ $commit_changes =~ ^[Yy]$ ]]; then
        echo "📝 提交更改..."
        git add .
        git commit -m "chore: commit changes before deployment test"
    else
        echo "⏭️ 跳过未提交的更改"
    fi
fi

# 选择触发方式
echo ""
echo "选择部署触发方式:"
echo "1) 创建空提交 (推荐)"
echo "2) 修改README文件"
echo "3) 添加时间戳文件"
echo "4) 取消操作"

read -p "请选择 (1-4): " choice

case $choice in
    1)
        echo ""
        echo "🔄 创建空提交触发部署..."
        TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
        git commit --allow-empty -m "test: trigger GitHub Actions deployment - $TIMESTAMP"
        ;;
    2)
        echo ""
        echo "📝 修改README文件..."
        TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
        echo "" >> README.md
        echo "<!-- Last deployment test: $TIMESTAMP -->" >> README.md
        git add README.md
        git commit -m "docs: update README to trigger deployment - $TIMESTAMP"
        ;;
    3)
        echo ""
        echo "📄 创建时间戳文件..."
        TIMESTAMP=$(date '+%Y-%m-%d_%H-%M-%S')
        echo "Deployment test triggered at: $(date)" > "deployment-test-$TIMESTAMP.txt"
        git add "deployment-test-$TIMESTAMP.txt"
        git commit -m "test: add timestamp file to trigger deployment - $TIMESTAMP"
        ;;
    4)
        echo "❌ 操作已取消"
        exit 0
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

# 推送到远程仓库
echo ""
echo "📤 推送到远程仓库..."
git push origin master

echo ""
echo "✅ 推送完成!"
echo ""
echo "🔍 监控部署状态:"
echo "   GitHub Actions: https://github.com/justpm2099/dev-daily-v2/actions"
echo "   Cloudflare Pages: https://dash.cloudflare.com/pages"
echo ""
echo "⏱️ 预计部署时间: 2-3分钟"
echo ""
echo "🔄 检查部署状态:"
echo "   运行: ./scripts/monitor-deployment.sh"
echo "   或访问: https://dev-daily-v2.pages.dev"

# 可选：等待并检查部署状态
echo ""
read -p "是否要等待并检查部署状态? (y/N): " wait_for_deployment

if [[ $wait_for_deployment =~ ^[Yy]$ ]]; then
    echo ""
    echo "⏳ 等待部署完成..."
    echo "   (按 Ctrl+C 可随时退出监控)"
    
    # 等待30秒让GitHub Actions开始
    echo "   等待GitHub Actions启动..."
    sleep 30
    
    # 检查部署状态
    for i in {1..10}; do
        echo "   检查第 $i 次..."
        
        # 检查最新部署
        LATEST_DEPLOYMENT=$(wrangler pages deployment list --project-name=dev-daily-v2 | head -5 | grep "$(git log --format="%h" -1)" || echo "")
        
        if [ -n "$LATEST_DEPLOYMENT" ]; then
            echo ""
            echo "🎉 部署成功!"
            echo "   最新部署包含当前提交"
            echo "   访问: https://dev-daily-v2.pages.dev"
            break
        fi
        
        if [ $i -eq 10 ]; then
            echo ""
            echo "⏰ 监控超时"
            echo "   请手动检查GitHub Actions状态"
            echo "   或运行: ./scripts/monitor-deployment.sh"
        else
            sleep 30
        fi
    done
fi

echo ""
echo "🎯 下次部署:"
echo "   只需正常推送代码，GitHub Actions会自动部署"
echo "   git add . && git commit -m 'your message' && git push"
