#!/bin/bash

# 🔍 验证部署配置脚本

echo "🔍 验证 GitHub Actions 部署配置"
echo "================================"
echo ""

# 检查 GitHub CLI
if ! command -v gh &> /dev/null; then
    echo "❌ GitHub CLI 未安装"
    exit 1
fi

echo "✅ GitHub CLI 已就绪"
echo ""

# 显示仓库信息
REPO_INFO=$(gh repo view --json owner,name -q '.owner.login + "/" + .name')
echo "📁 仓库: $REPO_INFO"
echo ""

# 检查 Secrets
echo "🔐 检查 GitHub Secrets:"
gh secret list
echo ""

# 检查必要的 Secrets
SECRETS=$(gh secret list --json name -q '.[].name')
HAS_ACCOUNT_ID=$(echo "$SECRETS" | grep -q "CLOUDFLARE_ACCOUNT_ID" && echo "✅" || echo "❌")
HAS_API_TOKEN=$(echo "$SECRETS" | grep -q "CLOUDFLARE_API_TOKEN" && echo "✅" || echo "❌")

echo "必要的 Secrets 状态:"
echo "  CLOUDFLARE_ACCOUNT_ID: $HAS_ACCOUNT_ID"
echo "  CLOUDFLARE_API_TOKEN:  $HAS_API_TOKEN"
echo ""

if [[ "$HAS_ACCOUNT_ID" == "❌" ]] || [[ "$HAS_API_TOKEN" == "❌" ]]; then
    echo "⚠️  缺少必要的 Secrets，请先设置:"
    
    if [[ "$HAS_ACCOUNT_ID" == "❌" ]]; then
        echo "   echo '74031127ffb3b4595fbdaf41971011c2' | gh secret set CLOUDFLARE_ACCOUNT_ID"
    fi
    
    if [[ "$HAS_API_TOKEN" == "❌" ]]; then
        echo "   gh secret set CLOUDFLARE_API_TOKEN"
    fi
    
    echo ""
    exit 1
fi

echo "✅ 所有必要的 Secrets 已设置"
echo ""

# 检查 GitHub Actions 工作流文件
if [[ -f ".github/workflows/ci-cd.yml" ]]; then
    echo "✅ GitHub Actions 工作流文件存在"
else
    echo "❌ GitHub Actions 工作流文件不存在"
    exit 1
fi
echo ""

# 检查最近的 Actions 运行
echo "📊 最近的 GitHub Actions 运行:"
gh run list --limit 3
echo ""

echo "🚀 部署配置验证完成！"
echo ""
echo "📋 下一步操作:"
echo "1. 推送代码触发部署: git push origin master"
echo "2. 查看 Actions 状态: https://github.com/$REPO_INFO/actions"
echo "3. 访问部署应用: https://dev-daily-v2.pages.dev"
echo ""
echo "🎯 如果要立即触发部署，运行:"
echo "   git commit --allow-empty -m 'trigger: GitHub Actions deployment test'"
echo "   git push origin master"
