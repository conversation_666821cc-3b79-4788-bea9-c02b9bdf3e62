# 🔑 创建 Cloudflare API Token 指南

## 快速设置步骤

### 1. 创建 API Token

1. **访问 Cloudflare API Tokens 页面**
   ```
   https://dash.cloudflare.com/profile/api-tokens
   ```

2. **点击 "Create Token" 按钮**

3. **选择 "Custom token" 模板**

4. **配置 Token 权限**：
   - **Token name**: `dev-daily-v2-github-actions`
   - **Permissions**:
     - `Account` → `Cloudflare Pages:Edit`
     - `Zone` → `Zone:Read` (可选，用于自定义域名)
   - **Account Resources**:
     - `Include` → `<EMAIL>'s Account`
   - **Zone Resources**:
     - `Include` → `All zones` (如果需要自定义域名)

5. **点击 "Continue to summary"**

6. **点击 "Create Token"**

7. **复制生成的 Token** (格式类似: `1234567890abcdef1234567890abcdef12345678`)

### 2. 使用 GitHub CLI 设置 Secret

复制 Token 后，在终端运行：

```bash
# 方法 1: 直接输入 Token
echo "YOUR_CLOUDFLARE_API_TOKEN" | gh secret set CLOUDFLARE_API_TOKEN

# 方法 2: 交互式输入 (更安全)
gh secret set CLOUDFLARE_API_TOKEN
# 然后粘贴您的 Token 并按回车
```

### 3. 验证设置

```bash
# 查看已设置的 Secrets
gh secret list

# 应该看到:
# CLOUDFLARE_ACCOUNT_ID  Updated 2025-XX-XX
# CLOUDFLARE_API_TOKEN   Updated 2025-XX-XX
```

## 🚀 自动化脚本

如果您想要一键设置，可以运行：

```bash
./scripts/setup-secrets.sh
```

这个脚本会：
1. 检查必要的 CLI 工具
2. 验证登录状态  
3. 引导您创建 API Token
4. 自动设置 GitHub Secrets

## ✅ 验证部署

设置完成后：

1. **推送代码触发部署**:
   ```bash
   git add .
   git commit -m "test: trigger GitHub Actions deployment"
   git push origin master
   ```

2. **查看 GitHub Actions 状态**:
   ```
   https://github.com/justpm2099/dev-daily-v2/actions
   ```

3. **访问部署的应用**:
   ```
   https://dev-daily-v2.pages.dev
   ```

## 🔐 安全提示

- ✅ API Token 只存储在 GitHub Secrets 中，不会暴露在代码中
- ✅ Token 具有最小必要权限 (只能编辑 Cloudflare Pages)
- ✅ 可以随时在 Cloudflare Dashboard 中撤销 Token
- ✅ GitHub Secrets 只有仓库管理员可以查看和修改

## 🛠️ 故障排除

### Token 权限不足
如果部署失败并提示权限错误：
1. 检查 Token 是否包含 `Cloudflare Pages:Edit` 权限
2. 确认 Account Resources 包含正确的账号

### Token 无效
如果 Token 验证失败：
1. 重新创建 Token
2. 确保复制完整的 Token 字符串
3. 检查 Token 是否已过期

### GitHub Actions 失败
如果 GitHub Actions 运行失败：
1. 检查 Secrets 是否正确设置
2. 查看 Actions 日志中的具体错误信息
3. 确认 Cloudflare Pages 项目名称正确
