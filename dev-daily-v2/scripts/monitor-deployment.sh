#!/bin/bash

# dev-daily v2 部署监控脚本
# 监控GitHub Actions和Cloudflare Pages的部署状态

set -e

echo "📊 dev-daily v2 部署状态监控"
echo "================================"

# 检查GitHub Actions状态
echo ""
echo "🔄 GitHub Actions 状态:"
echo "   仓库: https://github.com/justpm2099/dev-daily-v2"
echo "   Actions: https://github.com/justpm2099/dev-daily-v2/actions"

# 检查最新提交
echo ""
echo "📝 最新提交信息:"
git log --oneline -1

# 检查当前分支
echo ""
echo "🌿 当前分支:"
git branch --show-current

# 检查远程状态
echo ""
echo "🔗 远程同步状态:"
git status -uno

# 检查Cloudflare Pages状态
echo ""
echo "🌐 Cloudflare Pages 状态:"

# 生产环境检查
echo "📍 生产环境检查:"
PROD_URL="https://dev-daily-v2.pages.dev"
PROD_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $PROD_URL || echo "000")

if [ "$PROD_STATUS" = "200" ]; then
    echo "✅ 生产环境正常: $PROD_URL"
    
    # 检查页面内容
    if curl -s $PROD_URL | grep -q "dev-daily v2"; then
        echo "✅ 页面内容正常"
    else
        echo "⚠️ 页面内容可能有问题"
    fi
    
    # 检查最后修改时间
    LAST_MODIFIED=$(curl -s -I $PROD_URL | grep -i "last-modified" | cut -d' ' -f2- || echo "未知")
    echo "📅 最后修改: $LAST_MODIFIED"
    
else
    echo "❌ 生产环境异常 (HTTP $PROD_STATUS): $PROD_URL"
fi

# 预览环境检查
echo ""
echo "📍 预览环境检查:"
PREVIEW_URL="https://preview.dev-daily-v2.pages.dev"
PREVIEW_STATUS=$(curl -s -o /dev/null -w "%{http_code}" $PREVIEW_URL || echo "000")

if [ "$PREVIEW_STATUS" = "200" ]; then
    echo "✅ 预览环境正常: $PREVIEW_URL"
else
    echo "⚠️ 预览环境状态 (HTTP $PREVIEW_STATUS): $PREVIEW_URL"
fi

# 检查构建文件
echo ""
echo "📦 本地构建状态:"
if [ -d "dist" ]; then
    echo "✅ dist 目录存在"
    echo "📊 文件数量: $(find dist -type f | wc -l)"
    echo "📈 总大小: $(du -sh dist | cut -f1)"
    
    # 检查关键文件
    if [ -f "dist/index.html" ]; then
        echo "✅ index.html 存在"
    else
        echo "❌ index.html 缺失"
    fi
    
    if [ -d "dist/assets" ]; then
        echo "✅ assets 目录存在"
        echo "📊 资源文件: $(find dist/assets -type f | wc -l) 个"
    else
        echo "❌ assets 目录缺失"
    fi
else
    echo "❌ dist 目录不存在，需要重新构建"
fi

# 检查部署配置
echo ""
echo "⚙️ 部署配置检查:"

# wrangler.toml
if [ -f "wrangler.toml" ]; then
    echo "✅ wrangler.toml 配置正常"
    PROJECT_NAME=$(grep "^name" wrangler.toml | head -1 | cut -d'"' -f2)
    echo "📋 项目名称: $PROJECT_NAME"
else
    echo "❌ wrangler.toml 缺失"
fi

# GitHub Actions
if [ -f ".github/workflows/deploy.yml" ]; then
    echo "✅ GitHub Actions 配置正常"
    NODE_VERSION=$(grep "node-version:" .github/workflows/deploy.yml | cut -d"'" -f2)
    echo "📋 Node.js 版本: $NODE_VERSION"
else
    echo "❌ GitHub Actions 配置缺失"
fi

# 性能检查
echo ""
echo "⚡ 性能检查:"

# 检查页面加载时间
if [ "$PROD_STATUS" = "200" ]; then
    LOAD_TIME=$(curl -s -o /dev/null -w "%{time_total}" $PROD_URL)
    echo "⏱️ 页面加载时间: ${LOAD_TIME}s"
    
    if (( $(echo "$LOAD_TIME < 2.0" | bc -l) )); then
        echo "✅ 加载速度良好"
    elif (( $(echo "$LOAD_TIME < 5.0" | bc -l) )); then
        echo "⚠️ 加载速度一般"
    else
        echo "❌ 加载速度较慢"
    fi
fi

# 检查文档同步功能
echo ""
echo "📚 功能检查:"
if curl -s $PROD_URL | grep -q "文档同步"; then
    echo "✅ 文档同步功能已部署"
else
    echo "⚠️ 文档同步功能可能未正确部署"
fi

# 总结和建议
echo ""
echo "🎯 部署状态总结:"
echo "================================"

if [ "$PROD_STATUS" = "200" ] && [ -d "dist" ]; then
    echo "✅ 部署状态: 正常"
    echo "🎉 所有系统运行正常！"
else
    echo "⚠️ 部署状态: 需要关注"
    echo "🔧 建议检查以下项目:"
    
    if [ "$PROD_STATUS" != "200" ]; then
        echo "   - 检查 Cloudflare Pages 部署状态"
        echo "   - 查看 GitHub Actions 构建日志"
    fi
    
    if [ ! -d "dist" ]; then
        echo "   - 运行 npm run build 重新构建"
    fi
fi

echo ""
echo "📚 有用链接:"
echo "   🌐 生产环境: $PROD_URL"
echo "   🔍 预览环境: $PREVIEW_URL"
echo "   🔄 GitHub Actions: https://github.com/justpm2099/dev-daily-v2/actions"
echo "   ☁️ Cloudflare Pages: https://dash.cloudflare.com/pages"
echo "   📊 项目仓库: https://github.com/justpm2099/dev-daily-v2"

echo ""
echo "🔄 要持续监控，请运行: watch -n 30 ./scripts/monitor-deployment.sh"
