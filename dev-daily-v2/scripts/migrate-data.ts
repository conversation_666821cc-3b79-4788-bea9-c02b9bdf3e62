#!/usr/bin/env tsx

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { DevDailyAdapter } from '../src/services/DevDailyAdapter.js';
import { Document, ProjectTreeNode } from '../src/types/index.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 数据迁移服务
class DataMigrationService {
  private sourceDir: string;
  private targetDir: string;
  private backupDir: string;

  constructor() {
    this.sourceDir = path.join(__dirname, '../../dev-daily-package/dev-daily');
    this.targetDir = path.join(__dirname, '../data');
    this.backupDir = path.join(__dirname, '../data/backups/migration');
  }

  async migrate(): Promise<void> {
    console.log('🚀 开始dev-daily v1 -> v2 数据迁移...');
    
    try {
      // 1. 创建目录结构
      await this.createDirectoryStructure();
      
      // 2. 备份现有数据
      await this.createBackup();
      
      // 3. 扫描和转换文档
      const documents = await this.scanAndConvertDocuments();
      
      // 4. 生成项目树
      const projectTree = await this.generateProjectTree(documents);
      
      // 5. 保存转换后的数据
      await this.saveConvertedData(documents, projectTree);
      
      // 6. 验证迁移结果
      await this.validateMigration();
      
      console.log('✅ 数据迁移完成！');
      console.log(`📊 迁移统计:`);
      console.log(`   - 文档数量: ${documents.length}`);
      console.log(`   - 项目节点: ${this.countNodes(projectTree)}`);
      
    } catch (error) {
      console.error('❌ 迁移失败:', error);
      throw error;
    }
  }

  private async createDirectoryStructure(): Promise<void> {
    console.log('📁 创建目录结构...');
    
    const directories = [
      'documents',
      'project-tree',
      'ai-data',
      'backups',
      'backups/migration',
      'backups/auto',
      'backups/manual'
    ];

    for (const dir of directories) {
      const fullPath = path.join(this.targetDir, dir);
      await fs.mkdir(fullPath, { recursive: true });
    }
  }

  private async createBackup(): Promise<void> {
    console.log('💾 创建备份...');
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(this.backupDir, `migration-backup-${timestamp}`);
    
    try {
      // 检查源目录是否存在
      await fs.access(this.sourceDir);
      
      // 复制整个源目录
      await this.copyDirectory(this.sourceDir, backupPath);
      
      console.log(`✅ 备份创建成功: ${backupPath}`);
    } catch (error) {
      console.warn('⚠️ 源目录不存在，跳过备份');
    }
  }

  private async copyDirectory(src: string, dest: string): Promise<void> {
    await fs.mkdir(dest, { recursive: true });
    
    const entries = await fs.readdir(src, { withFileTypes: true });
    
    for (const entry of entries) {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);
      
      if (entry.isDirectory()) {
        await this.copyDirectory(srcPath, destPath);
      } else {
        await fs.copyFile(srcPath, destPath);
      }
    }
  }

  private async scanAndConvertDocuments(): Promise<Document[]> {
    console.log('📄 扫描和转换文档...');
    
    const documents: Document[] = [];
    
    try {
      // 检查源目录是否存在
      await fs.access(this.sourceDir);
      
      const files = await this.scanMarkdownFiles(this.sourceDir);
      
      for (const file of files) {
        try {
          const content = await fs.readFile(file.path, 'utf-8');
          const stats = await fs.stat(file.path);
          
          const document = await DevDailyAdapter.convertMarkdownToDocument(
            file.name,
            content,
            {
              createdAt: stats.birthtime.toISOString(),
              updatedAt: stats.mtime.toISOString(),
              size: stats.size
            }
          );
          
          documents.push(document);
          console.log(`   ✓ 转换文档: ${file.name}`);
        } catch (error) {
          console.warn(`   ⚠️ 跳过文档 ${file.name}: ${error}`);
        }
      }
    } catch (error) {
      console.warn('⚠️ 源目录不存在，创建示例文档');
      documents.push(...this.createSampleDocuments());
    }
    
    return documents;
  }

  private async scanMarkdownFiles(dir: string): Promise<Array<{ name: string; path: string }>> {
    const files: Array<{ name: string; path: string }> = [];
    
    const entries = await fs.readdir(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      
      if (entry.isDirectory()) {
        const subFiles = await this.scanMarkdownFiles(fullPath);
        files.push(...subFiles);
      } else if (entry.name.endsWith('.md')) {
        files.push({
          name: entry.name,
          path: fullPath
        });
      }
    }
    
    return files;
  }

  private createSampleDocuments(): Document[] {
    const now = new Date().toISOString();
    
    return [
      {
        id: 'sample-project-overview',
        filename: 'project-overview.md',
        title: '项目概览',
        content: `# dev-daily项目概览

## 项目简介
dev-daily是一个AI辅助的开发协作系统，旨在解决AI记忆限制问题，提升开发效率。

## 主要功能
- 结构化的开发日志记录
- AI上下文管理
- 项目进度跟踪
- 文档模板化

## 技术栈
- 前端：React + TypeScript
- 样式：Tailwind CSS
- 构建：Vite
`,
        type: 'guideline',
        status: 'published',
        metadata: {
          createdAt: now,
          updatedAt: now,
          author: 'dev-daily-user',
          tags: ['overview', 'project', 'guideline'],
          wordCount: 150
        }
      },
      {
        id: 'sample-daily-log',
        filename: `${new Date().toISOString().split('T')[0]}-migration-start.md`,
        title: '开始v2迁移',
        content: `# ${new Date().toISOString().split('T')[0]} - 开始v2迁移

## 今日目标
- 完成dev-daily v1到v2的数据迁移
- 验证Project Tree功能
- 测试AI助手集成

## 进展情况
- ✅ 创建新的项目结构
- ✅ 实现数据迁移脚本
- 🔄 正在测试功能完整性

## 遇到的问题
暂无

## 明日计划
- 完善用户界面
- 添加更多AI功能
- 优化性能表现
`,
        type: 'daily-log',
        status: 'published',
        metadata: {
          createdAt: now,
          updatedAt: now,
          author: 'dev-daily-user',
          tags: ['migration', 'v2', 'daily'],
          wordCount: 120
        }
      }
    ];
  }

  private async generateProjectTree(documents: Document[]): Promise<ProjectTreeNode> {
    console.log('🌳 生成项目树...');
    
    const projectTree = await DevDailyAdapter.generateProjectTreeFromDocuments(documents);
    
    console.log(`   ✓ 生成项目树，包含 ${this.countNodes(projectTree)} 个节点`);
    
    return projectTree;
  }

  private async saveConvertedData(documents: Document[], projectTree: ProjectTreeNode): Promise<void> {
    console.log('💾 保存转换后的数据...');
    
    // 保存文档数据
    const documentsPath = path.join(this.targetDir, 'documents', 'documents.json');
    await fs.writeFile(documentsPath, JSON.stringify(documents, null, 2));
    
    // 保存项目树数据
    const projectTreePath = path.join(this.targetDir, 'project-tree', 'tree-structure.json');
    await fs.writeFile(projectTreePath, JSON.stringify(projectTree, null, 2));
    
    // 保存迁移元数据
    const migrationMeta = {
      version: '2.0.0',
      migratedAt: new Date().toISOString(),
      sourceVersion: '1.0.0',
      documentCount: documents.length,
      nodeCount: this.countNodes(projectTree),
      migrationId: `migration-${Date.now()}`
    };
    
    const metaPath = path.join(this.targetDir, 'migration-meta.json');
    await fs.writeFile(metaPath, JSON.stringify(migrationMeta, null, 2));
    
    console.log('   ✓ 数据保存完成');
  }

  private async validateMigration(): Promise<void> {
    console.log('🔍 验证迁移结果...');
    
    // 验证文件是否存在
    const requiredFiles = [
      'documents/documents.json',
      'project-tree/tree-structure.json',
      'migration-meta.json'
    ];
    
    for (const file of requiredFiles) {
      const filePath = path.join(this.targetDir, file);
      try {
        await fs.access(filePath);
        console.log(`   ✓ 文件存在: ${file}`);
      } catch (error) {
        throw new Error(`验证失败: 文件不存在 ${file}`);
      }
    }
    
    // 验证数据完整性
    try {
      const documentsPath = path.join(this.targetDir, 'documents', 'documents.json');
      const documentsData = await fs.readFile(documentsPath, 'utf-8');
      const documents = JSON.parse(documentsData);
      
      if (!Array.isArray(documents)) {
        throw new Error('文档数据格式错误');
      }
      
      const projectTreePath = path.join(this.targetDir, 'project-tree', 'tree-structure.json');
      const treeData = await fs.readFile(projectTreePath, 'utf-8');
      const projectTree = JSON.parse(treeData);
      
      if (!projectTree.id || !projectTree.name) {
        throw new Error('项目树数据格式错误');
      }
      
      console.log('   ✓ 数据完整性验证通过');
    } catch (error) {
      throw new Error(`数据验证失败: ${error}`);
    }
  }

  private countNodes(node: ProjectTreeNode): number {
    let count = 1;
    if (node.children) {
      count += node.children.reduce((sum, child) => sum + this.countNodes(child), 0);
    }
    return count;
  }
}

// 主函数
async function main() {
  try {
    const migrationService = new DataMigrationService();
    await migrationService.migrate();
    process.exit(0);
  } catch (error) {
    console.error('迁移失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { DataMigrationService };
