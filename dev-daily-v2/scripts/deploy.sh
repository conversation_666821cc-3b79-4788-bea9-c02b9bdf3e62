#!/bin/bash

# dev-daily v2 部署脚本
# 用于部署到Cloudflare Pages

set -e

echo "🚀 开始部署 dev-daily v2 到 Cloudflare Pages..."

# 检查是否安装了必要的工具
if ! command -v npm &> /dev/null; then
    echo "❌ 错误: npm 未安装"
    exit 1
fi

if ! command -v wrangler &> /dev/null; then
    echo "📦 安装 wrangler..."
    npm install -g wrangler
fi

# 检查是否已登录 Cloudflare
echo "🔐 检查 Cloudflare 登录状态..."
if ! wrangler whoami &> /dev/null; then
    echo "请先登录 Cloudflare:"
    wrangler login
fi

# 安装依赖
echo "📦 安装依赖..."
npm ci

# 类型检查
echo "🔍 执行类型检查..."
npm run type-check

# 构建项目
echo "🏗️ 构建项目..."
npm run build

# 检查构建输出
if [ ! -d "dist" ]; then
    echo "❌ 错误: 构建失败，dist 目录不存在"
    exit 1
fi

echo "✅ 构建完成，dist 目录大小:"
du -sh dist

# 部署选择
echo ""
echo "选择部署类型:"
echo "1) 生产环境 (main)"
echo "2) 预览环境 (preview)"
read -p "请选择 (1-2): " choice

case $choice in
    1)
        echo "🚀 部署到生产环境..."
        wrangler pages deploy dist --project-name=dev-daily-v2 --branch=main
        ;;
    2)
        echo "🔍 部署到预览环境..."
        wrangler pages deploy dist --project-name=dev-daily-v2 --branch=preview
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "✅ 部署完成!"
echo "🌐 访问地址:"
echo "   生产环境: https://dev-daily-v2.pages.dev"
echo "   预览环境: https://preview.dev-daily-v2.pages.dev"
echo ""
echo "📊 Cloudflare Pages 控制台:"
echo "   https://dash.cloudflare.com/pages"
