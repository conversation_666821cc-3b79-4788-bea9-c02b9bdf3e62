#!/bin/bash

# dev-daily v2 部署状态检查脚本

set -e

echo "🔍 检查 dev-daily v2 部署状态..."

# 检查本地构建
echo ""
echo "📦 检查本地构建状态:"
if [ -d "dist" ]; then
    echo "✅ dist 目录存在"
    echo "📊 构建文件大小:"
    ls -la dist/
    echo ""
    echo "📈 总大小: $(du -sh dist | cut -f1)"
else
    echo "❌ dist 目录不存在，请先运行 npm run build"
fi

# 检查 wrangler 配置
echo ""
echo "⚙️ 检查 wrangler 配置:"
if [ -f "wrangler.toml" ]; then
    echo "✅ wrangler.toml 存在"
    echo "📋 项目配置:"
    grep -E "^name|^compatibility_date|^pages_build_output_dir" wrangler.toml || true
else
    echo "❌ wrangler.toml 不存在"
fi

# 检查 GitHub Actions
echo ""
echo "🔄 检查 GitHub Actions 配置:"
if [ -f ".github/workflows/deploy.yml" ]; then
    echo "✅ GitHub Actions 配置存在"
    echo "📋 工作流配置:"
    grep -E "^name:|node-version:|projectName:" .github/workflows/deploy.yml || true
else
    echo "❌ GitHub Actions 配置不存在"
fi

# 检查环境变量
echo ""
echo "🔐 检查环境变量配置:"
if [ -f ".env.local" ]; then
    echo "✅ .env.local 存在"
    echo "📋 配置项数量: $(grep -c "^[^#]" .env.local || echo "0")"
else
    echo "⚠️ .env.local 不存在，使用 .env.example 作为模板"
fi

# 检查 Cloudflare 登录状态
echo ""
echo "🔐 检查 Cloudflare 登录状态:"
if command -v wrangler &> /dev/null; then
    if wrangler whoami &> /dev/null; then
        echo "✅ 已登录 Cloudflare"
        wrangler whoami
    else
        echo "❌ 未登录 Cloudflare，请运行: wrangler login"
    fi
else
    echo "❌ wrangler 未安装，请运行: npm install -g wrangler"
fi

# 检查在线部署状态
echo ""
echo "🌐 检查在线部署状态:"

# 生产环境
echo "📍 生产环境:"
if curl -s -o /dev/null -w "%{http_code}" https://dev-daily-v2.pages.dev | grep -q "200"; then
    echo "✅ 生产环境可访问: https://dev-daily-v2.pages.dev"
else
    echo "❌ 生产环境不可访问或未部署"
fi

# 预览环境
echo "📍 预览环境:"
if curl -s -o /dev/null -w "%{http_code}" https://preview.dev-daily-v2.pages.dev | grep -q "200"; then
    echo "✅ 预览环境可访问: https://preview.dev-daily-v2.pages.dev"
else
    echo "⚠️ 预览环境不可访问或未部署"
fi

echo ""
echo "🎯 部署建议:"
echo "1. 确保所有检查项都通过"
echo "2. 如果首次部署，请先运行: wrangler login"
echo "3. 设置 GitHub Secrets:"
echo "   - CLOUDFLARE_API_TOKEN"
echo "   - CLOUDFLARE_ACCOUNT_ID"
echo "4. 推送代码到 main 分支触发自动部署"
echo "5. 或手动部署: ./scripts/deploy.sh"

echo ""
echo "📚 相关链接:"
echo "   Cloudflare Pages: https://dash.cloudflare.com/pages"
echo "   GitHub Actions: https://github.com/justpm2099/dev-daily-v2/actions"
echo "   项目文档: ./docs/"
