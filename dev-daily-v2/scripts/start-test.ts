#!/usr/bin/env tsx

import { spawn } from 'child_process';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 测试启动服务
class TestStartupService {
  private projectRoot: string;

  constructor() {
    this.projectRoot = path.join(__dirname, '..');
  }

  async startTest(): Promise<void> {
    console.log('🚀 启动dev-daily v2测试环境...\n');

    try {
      // 1. 检查环境
      await this.checkEnvironment();
      
      // 2. 安装依赖
      await this.installDependencies();
      
      // 3. 创建测试数据
      await this.createTestData();
      
      // 4. 启动开发服务器
      await this.startDevServer();
      
    } catch (error) {
      console.error('❌ 测试启动失败:', error);
      process.exit(1);
    }
  }

  private async checkEnvironment(): Promise<void> {
    console.log('🔍 检查环境...');
    
    // 检查Node.js版本
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion < 18) {
      throw new Error(`需要Node.js 18+，当前版本: ${nodeVersion}`);
    }
    
    console.log(`   ✅ Node.js版本: ${nodeVersion}`);
    
    // 检查package.json
    const packagePath = path.join(this.projectRoot, 'package.json');
    try {
      await fs.access(packagePath);
      console.log('   ✅ package.json存在');
    } catch {
      throw new Error('package.json不存在');
    }
  }

  private async installDependencies(): Promise<void> {
    console.log('📦 检查依赖...');
    
    const nodeModulesPath = path.join(this.projectRoot, 'node_modules');
    
    try {
      await fs.access(nodeModulesPath);
      console.log('   ✅ 依赖已安装');
    } catch {
      console.log('   📥 安装依赖...');
      await this.runCommand('npm', ['install'], this.projectRoot);
      console.log('   ✅ 依赖安装完成');
    }
  }

  private async createTestData(): Promise<void> {
    console.log('📄 创建测试数据...');
    
    const dataDir = path.join(this.projectRoot, 'data');
    
    // 创建数据目录
    await fs.mkdir(dataDir, { recursive: true });
    await fs.mkdir(path.join(dataDir, 'documents'), { recursive: true });
    await fs.mkdir(path.join(dataDir, 'project-tree'), { recursive: true });
    await fs.mkdir(path.join(dataDir, 'backups'), { recursive: true });
    
    // 创建测试配置文件
    const testConfig = {
      version: '2.0.0-test',
      environment: 'test',
      features: {
        projectTree: true,
        aiAssistant: true,
        documentManagement: true,
        dataSync: true
      },
      testData: {
        enabled: true,
        documents: 5,
        projectNodes: 12
      },
      createdAt: new Date().toISOString()
    };
    
    await fs.writeFile(
      path.join(dataDir, 'test-config.json'),
      JSON.stringify(testConfig, null, 2)
    );
    
    console.log('   ✅ 测试数据创建完成');
  }

  private async startDevServer(): Promise<void> {
    console.log('🌐 启动开发服务器...\n');
    
    console.log('=' .repeat(60));
    console.log('🎉 dev-daily v2 测试环境启动中...');
    console.log('=' .repeat(60));
    console.log('');
    console.log('📋 测试功能清单:');
    console.log('   ✅ Project Tree管理 - 可视化项目结构');
    console.log('   ✅ 文档管理系统 - 搜索、过滤、分类');
    console.log('   ✅ AI智能分析 - 自动生成项目树');
    console.log('   ✅ 主仪表板 - 项目概览和统计');
    console.log('   ✅ 响应式设计 - 适配不同设备');
    console.log('');
    console.log('🔗 访问地址: http://localhost:5173');
    console.log('📱 移动端测试: 调整浏览器窗口大小');
    console.log('🎯 重点测试: 拖拽重组项目树结构');
    console.log('');
    console.log('=' .repeat(60));
    console.log('');

    // 启动Vite开发服务器
    const devProcess = spawn('npm', ['run', 'dev'], {
      cwd: this.projectRoot,
      stdio: 'inherit',
      shell: true
    });

    // 处理进程退出
    devProcess.on('close', (code) => {
      if (code !== 0) {
        console.error(`\n❌ 开发服务器退出，代码: ${code}`);
      } else {
        console.log('\n✅ 开发服务器正常退出');
      }
    });

    // 处理中断信号
    process.on('SIGINT', () => {
      console.log('\n🛑 正在关闭开发服务器...');
      devProcess.kill('SIGINT');
      process.exit(0);
    });
  }

  private async runCommand(command: string, args: string[], cwd: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const process = spawn(command, args, {
        cwd,
        stdio: 'pipe',
        shell: true
      });

      let output = '';
      let errorOutput = '';

      process.stdout?.on('data', (data) => {
        output += data.toString();
      });

      process.stderr?.on('data', (data) => {
        errorOutput += data.toString();
      });

      process.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`命令失败: ${command} ${args.join(' ')}\n${errorOutput}`));
        }
      });
    });
  }
}

// 主函数
async function main() {
  try {
    const testService = new TestStartupService();
    await testService.startTest();
  } catch (error) {
    console.error('启动失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { TestStartupService };
