#!/bin/bash

# 🔐 GitHub Secrets 自动化设置脚本
# 使用 GitHub CLI 和 Cloudflare CLI 自动配置部署所需的 Secrets

set -e

echo "🚀 开始设置 GitHub Secrets for dev-daily-v2 部署..."
echo ""

# 检查必要的 CLI 工具
echo "📋 检查必要工具..."

if ! command -v gh &> /dev/null; then
    echo "❌ GitHub CLI 未安装。请先安装: brew install gh"
    exit 1
fi

if ! command -v wrangler &> /dev/null; then
    echo "❌ Wrangler CLI 未安装。请先安装: npm install -g wrangler"
    exit 1
fi

echo "✅ GitHub CLI 已安装"
echo "✅ Wrangler CLI 已安装"
echo ""

# 检查 GitHub CLI 登录状态
echo "🔍 检查 GitHub CLI 登录状态..."
if ! gh auth status &> /dev/null; then
    echo "❌ GitHub CLI 未登录。请先运行: gh auth login"
    exit 1
fi

echo "✅ GitHub CLI 已登录"
echo ""

# 检查 Wrangler 登录状态
echo "🔍 检查 Wrangler 登录状态..."
if ! wrangler whoami &> /dev/null; then
    echo "❌ Wrangler 未登录。请先运行: wrangler login"
    exit 1
fi

echo "✅ Wrangler 已登录"
echo ""

# 获取 Cloudflare 账号信息
echo "📊 获取 Cloudflare 账号信息..."
ACCOUNT_INFO=$(wrangler whoami 2>/dev/null)
ACCOUNT_ID=$(echo "$ACCOUNT_INFO" | grep -A 1 "Account ID" | tail -1 | awk '{print $NF}')

if [ -z "$ACCOUNT_ID" ]; then
    echo "❌ 无法获取 Cloudflare 账号 ID"
    exit 1
fi

echo "✅ Cloudflare 账号 ID: $ACCOUNT_ID"
echo ""

# 检查是否已有 API Token
echo "🔑 检查 Cloudflare API Token..."
echo ""
echo "⚠️  需要创建 Cloudflare API Token 用于 GitHub Actions 部署"
echo ""
echo "📝 请按以下步骤创建 API Token："
echo "1. 访问: https://dash.cloudflare.com/profile/api-tokens"
echo "2. 点击 'Create Token'"
echo "3. 选择 'Custom token'"
echo "4. 配置权限:"
echo "   - Account: Cloudflare Pages:Edit"
echo "   - Zone: Zone:Read (可选，用于自定义域名)"
echo "5. 复制生成的 Token"
echo ""

# 提示用户输入 API Token
read -p "🔐 请输入您的 Cloudflare API Token: " -s CLOUDFLARE_API_TOKEN
echo ""

if [ -z "$CLOUDFLARE_API_TOKEN" ]; then
    echo "❌ API Token 不能为空"
    exit 1
fi

# 验证 API Token
echo "🔍 验证 API Token..."
if ! curl -s -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
     "https://api.cloudflare.com/client/v4/user/tokens/verify" | \
     grep -q '"success":true'; then
    echo "❌ API Token 验证失败，请检查 Token 是否正确"
    exit 1
fi

echo "✅ API Token 验证成功"
echo ""

# 设置 GitHub Secrets
echo "🔧 设置 GitHub Secrets..."

# 设置 CLOUDFLARE_API_TOKEN
echo "📝 设置 CLOUDFLARE_API_TOKEN..."
if echo "$CLOUDFLARE_API_TOKEN" | gh secret set CLOUDFLARE_API_TOKEN; then
    echo "✅ CLOUDFLARE_API_TOKEN 设置成功"
else
    echo "❌ CLOUDFLARE_API_TOKEN 设置失败"
    exit 1
fi

# 设置 CLOUDFLARE_ACCOUNT_ID
echo "📝 设置 CLOUDFLARE_ACCOUNT_ID..."
if echo "$ACCOUNT_ID" | gh secret set CLOUDFLARE_ACCOUNT_ID; then
    echo "✅ CLOUDFLARE_ACCOUNT_ID 设置成功"
else
    echo "❌ CLOUDFLARE_ACCOUNT_ID 设置失败"
    exit 1
fi

echo ""
echo "🎉 所有 GitHub Secrets 设置完成！"
echo ""
echo "📋 已设置的 Secrets:"
echo "   - CLOUDFLARE_API_TOKEN: ✅"
echo "   - CLOUDFLARE_ACCOUNT_ID: ✅ ($ACCOUNT_ID)"
echo ""
echo "🚀 现在您可以："
echo "   1. 推送代码到 master 分支触发自动部署"
echo "   2. 创建 Pull Request 获得预览部署"
echo "   3. 查看 GitHub Actions 运行状态"
echo ""
echo "🌐 部署地址:"
echo "   - 生产环境: https://dev-daily-v2.pages.dev"
echo "   - GitHub Actions: https://github.com/$(gh repo view --json owner,name -q '.owner.login + "/" + .name')/actions"
echo ""
echo "✨ 部署配置完成！"
