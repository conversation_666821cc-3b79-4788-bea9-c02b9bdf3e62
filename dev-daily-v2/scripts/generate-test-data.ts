/**
 * 测试数据生成脚本
 * 
 * 用于生成备份和项目日历的测试数据
 * 运行方式: npm run tsx scripts/generate-test-data.ts
 */

import { format, subDays, addDays } from 'date-fns';
import { ProjectLogService } from '../src/services/ProjectLogService';
import { BackupService } from '../src/services/BackupService';
import { TestDataService } from '../src/services/TestDataService';

// 生成过去30天的项目日志数据
const generateProjectLogs = () => {
  console.log('正在生成项目日志数据...');
  
  const today = new Date();
  const projectTree = TestDataService.generateTestProjectTree();
  const projectId = projectTree.id;
  const projectName = projectTree.name;
  
  // 模拟节点完成记录
  const completedNodes = [
    { id: 'A1', name: 'A1: Node.js 环境', day: -28, hours: 1 },
    { id: 'A2', name: 'A2: TypeScript 配置', day: -27, hours: 2 },
    { id: 'A3', name: 'A3: React 生态系统', day: -25, hours: 3 },
    { id: 'A4', name: 'A4: Vite 构建工具', day: -24, hours: 2 },
    { id: 'B1', name: 'B1: Project Tree管理', day: -20, hours: 8 },
    { id: 'C1', name: 'C1: 主仪表板', day: -15, hours: 6 },
    { id: 'B4', name: 'B4: 思维模型系统', day: -10, hours: 7 },
    { id: 'C3', name: 'C3: 思维模型界面', day: -8, hours: 5 },
    { id: 'D1', name: 'D1: 迁移脚本', day: -5, hours: 4 },
    { id: 'D2', name: 'D2: 数据验证', day: -3, hours: 3 },
    { id: 'B2', name: 'B2: 文档管理系统', day: -1, hours: 6 },
  ];
  
  // 模拟问题记录
  const issues = [
    { description: 'TypeScript类型定义不完整', day: -26 },
    { description: 'React组件渲染性能问题', day: -22 },
    { description: '拖拽功能在移动端不稳定', day: -18 },
    { description: '思维模型切换时出现闪烁', day: -9 },
    { description: '数据迁移时部分文档丢失', day: -4 },
    { description: '备份恢复后UI状态不一致', day: -2 },
  ];
  
  // 模拟里程碑记录
  const milestones = [
    { name: 'A模块: 环境与依赖 完成', day: -23 },
    { name: '项目树基础功能完成', day: -19 },
    { name: '思维模型功能完成', day: -7 },
    { name: '数据迁移功能完成', day: -2 },
  ];
  
  // 模拟每日笔记
  const dailyNotes = [
    { note: '今天开始环境配置，Node.js安装顺利', day: -29 },
    { note: 'TypeScript配置遇到一些问题，需要调整tsconfig.json', day: -27 },
    { note: '完成了React组件的基础结构设计', day: -24 },
    { note: '项目树拖拽功能实现，但还需优化', day: -21 },
    { note: '思维模型的六顶思考帽实现完成', day: -12 },
    { note: '数据迁移功能测试中发现几个边缘情况', day: -6 },
    { note: '准备开始实现备份功能', day: -3 },
    { note: '今天完成了所有计划任务，进度超出预期', day: -1 },
  ];
  
  // 记录节点完成
  for (const node of completedNodes) {
    const date = subDays(today, Math.abs(node.day));
    ProjectLogService.logTaskCompleted(
      projectId,
      projectName,
      node.id,
      node.name,
      node.hours
    );
    
    // 修改日期（模拟历史数据）
    const logs = ProjectLogService.getAllLogs();
    const lastLog = logs[0];
    lastLog.date = format(date, 'yyyy-MM-dd');
    lastLog.timestamp = date.toISOString();
    localStorage.setItem('dev-daily-v2-project-logs', JSON.stringify(logs));
  }
  
  // 记录问题
  for (const issue of issues) {
    const date = subDays(today, Math.abs(issue.day));
    ProjectLogService.logIssueReported(
      projectId,
      projectName,
      issue.description
    );
    
    // 修改日期
    const logs = ProjectLogService.getAllLogs();
    const lastLog = logs[0];
    lastLog.date = format(date, 'yyyy-MM-dd');
    lastLog.timestamp = date.toISOString();
    localStorage.setItem('dev-daily-v2-project-logs', JSON.stringify(logs));
  }
  
  // 记录里程碑
  for (const milestone of milestones) {
    const date = subDays(today, Math.abs(milestone.day));
    ProjectLogService.logMilestoneReached(
      projectId,
      projectName,
      milestone.name
    );
    
    // 修改日期
    const logs = ProjectLogService.getAllLogs();
    const lastLog = logs[0];
    lastLog.date = format(date, 'yyyy-MM-dd');
    lastLog.timestamp = date.toISOString();
    localStorage.setItem('dev-daily-v2-project-logs', JSON.stringify(logs));
  }
  
  // 记录笔记
  for (const note of dailyNotes) {
    const date = subDays(today, Math.abs(note.day));
    ProjectLogService.addProjectNote(
      projectId,
      projectName,
      note.note
    );
    
    // 修改日期
    const logs = ProjectLogService.getAllLogs();
    const lastLog = logs[0];
    lastLog.date = format(date, 'yyyy-MM-dd');
    lastLog.timestamp = date.toISOString();
    localStorage.setItem('dev-daily-v2-project-logs', JSON.stringify(logs));
  }
  
  console.log(`已生成 ${completedNodes.length + issues.length + milestones.length + dailyNotes.length} 条项目日志数据`);
};

// 生成备份数据
const generateBackups = async () => {
  console.log('正在生成备份数据...');
  
  const projectTree = TestDataService.generateTestProjectTree();
  const documents = TestDataService.generateTestDocuments();
  const userSettings = {
    theme: 'light',
    language: 'zh-CN',
    aiApiKey: 'YOUR_API_KEY_HERE', // 请在实际使用时替换为您的API密钥
    backupEnabled: true,
    autoSave: true
  };
  
  // 模拟备份记录
  const backupPoints = [
    { description: '初始项目设置备份', day: -30, type: 'manual' },
    { description: '环境配置完成备份', day: -25, type: 'milestone' },
    { description: '项目树功能完成备份', day: -20, type: 'milestone' },
    { description: '每周例行备份', day: -14, type: 'manual' },
    { description: '思维模型功能完成备份', day: -10, type: 'milestone' },
    { description: '数据迁移功能完成备份', day: -5, type: 'milestone' },
    { description: '每周例行备份', day: -7, type: 'manual' },
    { description: '文档管理功能完成备份', day: -1, type: 'milestone' },
    { description: '当前状态备份', day: 0, type: 'manual' },
  ];
  
  for (const point of backupPoints) {
    const date = subDays(new Date(), Math.abs(point.day));
    
    const backup = await BackupService.createBackup(
      projectTree,
      documents,
      userSettings,
      {
        description: point.description,
        triggerType: point.type as any
      }
    );
    
    // 修改时间戳
    const backups = await BackupService.getAllBackups();
    const lastBackup = backups[0];
    lastBackup.timestamp = date.toISOString();
    localStorage.setItem('dev-daily-v2-backups', JSON.stringify(backups));
  }
  
  console.log(`已生成 ${backupPoints.length} 条备份数据`);
};

// 主函数
const main = async () => {
  try {
    console.log('开始生成测试数据...');
    
    // 清除现有数据
    localStorage.removeItem('dev-daily-v2-project-logs');
    localStorage.removeItem('dev-daily-v2-daily-progress');
    localStorage.removeItem('dev-daily-v2-backups');
    
    // 生成数据
    generateProjectLogs();
    await generateBackups();
    
    console.log('测试数据生成完成！');
  } catch (error) {
    console.error('生成测试数据时出错:', error);
  }
};

// 执行主函数
main();
