#!/bin/bash

# 🚀 快速设置 GitHub Secrets
# 使用 GitHub CLI 快速配置 Cloudflare API Token

set -e

echo "🔐 GitHub Secrets 快速设置"
echo "=========================="
echo ""

# 检查 GitHub CLI
if ! command -v gh &> /dev/null; then
    echo "❌ GitHub CLI 未安装。请先安装: brew install gh"
    exit 1
fi

# 检查登录状态
if ! gh auth status &> /dev/null; then
    echo "❌ GitHub CLI 未登录。请先运行: gh auth login"
    exit 1
fi

echo "✅ GitHub CLI 已就绪"
echo ""

# 显示当前仓库信息
REPO_INFO=$(gh repo view --json owner,name -q '.owner.login + "/" + .name')
echo "📁 当前仓库: $REPO_INFO"
echo ""

# 检查已有的 Secrets
echo "🔍 检查现有 Secrets..."
EXISTING_SECRETS=$(gh secret list --json name -q '.[].name' | tr '\n' ' ')
echo "已有 Secrets: $EXISTING_SECRETS"
echo ""

# 设置 CLOUDFLARE_ACCOUNT_ID (已知)
echo "📝 设置 CLOUDFLARE_ACCOUNT_ID..."
if echo "74031127ffb3b4595fbdaf41971011c2" | gh secret set CLOUDFLARE_ACCOUNT_ID; then
    echo "✅ CLOUDFLARE_ACCOUNT_ID 已设置"
else
    echo "❌ CLOUDFLARE_ACCOUNT_ID 设置失败"
    exit 1
fi
echo ""

# 设置 CLOUDFLARE_API_TOKEN
echo "🔑 设置 CLOUDFLARE_API_TOKEN"
echo ""
echo "📋 请按以下步骤创建 API Token:"
echo "1. 浏览器已打开 Cloudflare API Tokens 页面"
echo "2. 点击 'Create Token'"
echo "3. 选择 'Custom token'"
echo "4. 配置:"
echo "   - Token name: dev-daily-v2-github-actions"
echo "   - Permissions: Account → Cloudflare Pages:Edit"
echo "   - Account Resources: Include → <EMAIL>'s Account"
echo "5. 创建并复制 Token"
echo ""

# 交互式输入 API Token
echo "🔐 请输入您的 Cloudflare API Token:"
if gh secret set CLOUDFLARE_API_TOKEN; then
    echo "✅ CLOUDFLARE_API_TOKEN 已设置"
else
    echo "❌ CLOUDFLARE_API_TOKEN 设置失败"
    exit 1
fi
echo ""

# 验证设置
echo "🔍 验证 Secrets 设置..."
gh secret list
echo ""

echo "🎉 GitHub Secrets 设置完成！"
echo ""
echo "🚀 下一步:"
echo "1. 推送代码到 master 分支: git push origin master"
echo "2. 查看 GitHub Actions: https://github.com/$REPO_INFO/actions"
echo "3. 访问部署的应用: https://dev-daily-v2.pages.dev"
echo ""
echo "✨ 自动化部署已配置完成！"
