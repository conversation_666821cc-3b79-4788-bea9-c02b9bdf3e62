#!/bin/bash

# GitHub Actions 测试和诊断脚本

set -e

echo "🔍 GitHub Actions 配置诊断"
echo "================================"

# 检查仓库信息
echo ""
echo "📋 仓库信息:"
echo "   远程URL: $(git remote get-url origin)"
echo "   当前分支: $(git branch --show-current)"
echo "   最新提交: $(git log --oneline -1)"

# 检查GitHub Actions配置文件
echo ""
echo "⚙️ GitHub Actions 配置检查:"
if [ -f ".github/workflows/deploy.yml" ]; then
    echo "✅ deploy.yml 存在"
    
    # 检查触发条件
    echo "📋 触发分支:"
    grep -A 2 "branches:" .github/workflows/deploy.yml | grep -E "master|main" || echo "❌ 未找到master/main分支配置"
    
    # 检查必要的secrets
    echo ""
    echo "🔐 需要的Secrets:"
    echo "   - CLOUDFLARE_API_TOKEN"
    echo "   - CLOUDFLARE_ACCOUNT_ID"
    echo "   - GITHUB_TOKEN (自动提供)"
    
    # 检查项目名称
    PROJECT_NAME=$(grep "projectName:" .github/workflows/deploy.yml | cut -d':' -f2 | tr -d ' ')
    echo ""
    echo "📋 项目名称: $PROJECT_NAME"
    
else
    echo "❌ .github/workflows/deploy.yml 不存在"
fi

# 检查wrangler配置
echo ""
echo "🔧 Wrangler 配置检查:"
if [ -f "wrangler.toml" ]; then
    echo "✅ wrangler.toml 存在"
    WRANGLER_PROJECT=$(grep "^name" wrangler.toml | head -1 | cut -d'"' -f2)
    echo "📋 Wrangler项目名: $WRANGLER_PROJECT"
    
    if [ "$PROJECT_NAME" = "$WRANGLER_PROJECT" ]; then
        echo "✅ 项目名称匹配"
    else
        echo "⚠️ 项目名称不匹配: GitHub Actions($PROJECT_NAME) vs Wrangler($WRANGLER_PROJECT)"
    fi
else
    echo "❌ wrangler.toml 不存在"
fi

# 检查构建配置
echo ""
echo "🏗️ 构建配置检查:"
if [ -f "package.json" ]; then
    echo "✅ package.json 存在"
    
    # 检查必要的脚本
    if grep -q '"type-check"' package.json; then
        echo "✅ type-check 脚本存在"
    else
        echo "❌ type-check 脚本缺失"
    fi
    
    if grep -q '"build"' package.json; then
        echo "✅ build 脚本存在"
    else
        echo "❌ build 脚本缺失"
    fi
    
    # 检查Node.js版本要求
    NODE_VERSION=$(grep "node-version:" .github/workflows/deploy.yml | cut -d"'" -f2)
    echo "📋 GitHub Actions Node.js版本: $NODE_VERSION"
    
else
    echo "❌ package.json 不存在"
fi

# 测试本地构建
echo ""
echo "🧪 本地构建测试:"
echo "正在测试构建流程..."

# 类型检查
if npm run type-check > /dev/null 2>&1; then
    echo "✅ 类型检查通过"
else
    echo "❌ 类型检查失败"
fi

# 构建测试
if npm run build > /dev/null 2>&1; then
    echo "✅ 构建成功"
    if [ -d "dist" ]; then
        echo "✅ dist 目录生成"
        echo "📊 构建文件: $(find dist -type f | wc -l) 个"
    else
        echo "❌ dist 目录未生成"
    fi
else
    echo "❌ 构建失败"
fi

# 检查Cloudflare连接
echo ""
echo "☁️ Cloudflare 连接检查:"
if command -v wrangler &> /dev/null; then
    if wrangler whoami > /dev/null 2>&1; then
        echo "✅ Cloudflare 已登录"
        ACCOUNT_ID=$(wrangler whoami | grep "Account ID" | awk '{print $NF}')
        echo "📋 Account ID: $ACCOUNT_ID"
    else
        echo "❌ Cloudflare 未登录"
    fi
else
    echo "❌ wrangler 未安装"
fi

# 检查最近的GitHub Actions运行
echo ""
echo "🔄 GitHub Actions 状态建议:"
echo "1. 检查GitHub仓库的Actions页面:"
echo "   https://github.com/justpm2099/dev-daily-v2/actions"
echo ""
echo "2. 确保以下Secrets已配置:"
echo "   - 访问: https://github.com/justpm2099/dev-daily-v2/settings/secrets/actions"
echo "   - 添加 CLOUDFLARE_API_TOKEN"
echo "   - 添加 CLOUDFLARE_ACCOUNT_ID"
echo ""
echo "3. 如果Secrets未配置，请按以下步骤操作:"
echo "   a) 获取Cloudflare API Token:"
echo "      - 访问: https://dash.cloudflare.com/profile/api-tokens"
echo "      - 创建Token，权限: Account:Cloudflare Pages:Edit"
echo "   b) 获取Account ID:"
echo "      - 在Cloudflare Dashboard右侧栏查看"
echo "      - 或运行: wrangler whoami"
echo ""
echo "4. 手动触发GitHub Actions:"
echo "   - 推送新的commit到master分支"
echo "   - 或在Actions页面手动运行workflow"

# 生成修复建议
echo ""
echo "🔧 修复建议:"
if [ ! -f ".github/workflows/deploy.yml" ]; then
    echo "❌ 需要创建GitHub Actions配置文件"
elif ! grep -q "CLOUDFLARE_API_TOKEN" .github/workflows/deploy.yml; then
    echo "❌ GitHub Actions配置中缺少Cloudflare配置"
else
    echo "✅ 配置文件看起来正常"
    echo "🎯 主要问题可能是GitHub Secrets未正确配置"
    echo ""
    echo "📝 下一步操作:"
    echo "1. 访问 GitHub 仓库设置页面"
    echo "2. 配置必要的 Secrets"
    echo "3. 推送新的 commit 测试自动部署"
    echo "4. 或使用手动部署: ./scripts/deploy.sh"
fi

echo ""
echo "🎯 快速修复命令:"
echo "   手动部署: wrangler pages deploy dist --project-name=dev-daily-v2"
echo "   检查部署: wrangler pages deployment list --project-name=dev-daily-v2"
echo "   强制推送: git commit --allow-empty -m 'trigger deployment' && git push"
