/**
 * Service Worker - 离线支持和缓存管理
 */

const CACHE_NAME = 'dev-daily-v2-cache-v1';
const RUNTIME_CACHE = 'dev-daily-v2-runtime-v1';

// 需要缓存的静态资源
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.ico'
];

// 需要缓存的 API 路径模式
const API_CACHE_PATTERNS = [
  /^\/api\//,
  /^\/data\//
];

// 安装事件 - 缓存静态资源
self.addEventListener('install', (event) => {
  console.log('Service Worker 安装中...');
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('缓存静态资源...');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('Service Worker 安装完成');
        return self.skipWaiting();
      })
  );
});

// 激活事件 - 清理旧缓存
self.addEventListener('activate', (event) => {
  console.log('Service Worker 激活中...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME && cacheName !== RUNTIME_CACHE) {
              console.log('删除旧缓存:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker 激活完成');
        return self.clients.claim();
      })
  );
});

// 拦截网络请求
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // 跳过非 HTTP(S) 请求
  if (!request.url.startsWith('http')) {
    return;
  }

  // 处理导航请求
  if (request.mode === 'navigate') {
    event.respondWith(handleNavigationRequest(request));
    return;
  }

  // 处理静态资源
  if (isStaticAsset(request)) {
    event.respondWith(handleStaticAsset(request));
    return;
  }

  // 处理 API 请求
  if (isApiRequest(request)) {
    event.respondWith(handleApiRequest(request));
    return;
  }

  // 处理其他资源
  event.respondWith(handleOtherRequest(request));
});

// 处理导航请求 (HTML 页面)
async function handleNavigationRequest(request) {
  try {
    // 尝试网络请求
    const response = await fetch(request);
    
    // 缓存成功的响应
    if (response.ok) {
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, response.clone());
    }
    
    return response;
  } catch (error) {
    console.log('网络请求失败，使用缓存:', error);
    
    // 网络失败时返回缓存的 index.html
    const cache = await caches.open(CACHE_NAME);
    const cachedResponse = await cache.match('/index.html');
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // 如果没有缓存，返回离线页面
    return new Response(getOfflinePage(), {
      headers: { 'Content-Type': 'text/html' }
    });
  }
}

// 处理静态资源 (CSS, JS, 图片等)
async function handleStaticAsset(request) {
  try {
    // 只处理 GET 请求
    if (request.method !== 'GET') {
      return fetch(request);
    }

    // 缓存优先策略
    const cache = await caches.open(CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      // 后台更新缓存
      fetch(request).then((response) => {
        if (response.ok && request.method === 'GET') {
          try {
            cache.put(request, response.clone());
          } catch (error) {
            console.log('后台缓存更新失败:', error);
          }
        }
      }).catch(() => {
        // 忽略后台更新失败
      });

      return cachedResponse;
    }
    
    // 缓存中没有，尝试网络请求
    const response = await fetch(request);

    // 只缓存 GET 请求的成功响应
    if (response.ok && request.method === 'GET') {
      try {
        cache.put(request, response.clone());
      } catch (error) {
        console.log('缓存失败:', error);
      }
    }

    return response;
  } catch (error) {
    console.log('静态资源请求失败:', error);
    
    // 返回缓存的响应或默认响应
    const cache = await caches.open(CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    return cachedResponse || new Response('资源不可用', { status: 404 });
  }
}

// 处理 API 请求
async function handleApiRequest(request) {
  try {
    // 网络优先策略
    const response = await fetch(request);
    
    if (response.ok) {
      // 缓存 GET 请求的响应
      if (request.method === 'GET') {
        const cache = await caches.open(RUNTIME_CACHE);
        cache.put(request, response.clone());
      }
    }
    
    return response;
  } catch (error) {
    console.log('API 请求失败，尝试缓存:', error);
    
    // 只有 GET 请求才尝试缓存
    if (request.method === 'GET') {
      const cache = await caches.open(RUNTIME_CACHE);
      const cachedResponse = await cache.match(request);
      
      if (cachedResponse) {
        // 添加离线标识
        const headers = new Headers(cachedResponse.headers);
        headers.set('X-Served-From', 'cache');
        
        return new Response(cachedResponse.body, {
          status: cachedResponse.status,
          statusText: cachedResponse.statusText,
          headers: headers
        });
      }
    }
    
    // 返回离线响应
    return new Response(JSON.stringify({
      error: '网络不可用',
      message: '当前处于离线状态，请检查网络连接',
      offline: true
    }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// 处理其他请求
async function handleOtherRequest(request) {
  try {
    return await fetch(request);
  } catch (error) {
    console.log('其他请求失败:', error);
    return new Response('网络不可用', { status: 503 });
  }
}

// 判断是否为静态资源
function isStaticAsset(request) {
  const url = new URL(request.url);
  const pathname = url.pathname;
  
  return (
    pathname.endsWith('.js') ||
    pathname.endsWith('.css') ||
    pathname.endsWith('.png') ||
    pathname.endsWith('.jpg') ||
    pathname.endsWith('.jpeg') ||
    pathname.endsWith('.gif') ||
    pathname.endsWith('.svg') ||
    pathname.endsWith('.ico') ||
    pathname.endsWith('.woff') ||
    pathname.endsWith('.woff2') ||
    pathname.endsWith('.ttf')
  );
}

// 判断是否为 API 请求
function isApiRequest(request) {
  const url = new URL(request.url);
  return API_CACHE_PATTERNS.some(pattern => pattern.test(url.pathname));
}

// 获取离线页面 HTML
function getOfflinePage() {
  return `
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>离线模式 - dev-daily v2</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          margin: 0;
          padding: 0;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .container {
          text-align: center;
          color: white;
          max-width: 500px;
          padding: 2rem;
        }
        .icon {
          font-size: 4rem;
          margin-bottom: 1rem;
        }
        h1 {
          font-size: 2rem;
          margin-bottom: 1rem;
          font-weight: 300;
        }
        p {
          font-size: 1.1rem;
          line-height: 1.6;
          margin-bottom: 2rem;
          opacity: 0.9;
        }
        .retry-btn {
          background: rgba(255, 255, 255, 0.2);
          border: 2px solid rgba(255, 255, 255, 0.3);
          color: white;
          padding: 0.75rem 2rem;
          border-radius: 50px;
          font-size: 1rem;
          cursor: pointer;
          transition: all 0.3s ease;
        }
        .retry-btn:hover {
          background: rgba(255, 255, 255, 0.3);
          border-color: rgba(255, 255, 255, 0.5);
        }
        .features {
          margin-top: 2rem;
          text-align: left;
        }
        .feature {
          margin: 0.5rem 0;
          opacity: 0.8;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="icon">📱</div>
        <h1>离线模式</h1>
        <p>您当前处于离线状态，但 dev-daily v2 仍然可以正常工作！</p>
        
        <div class="features">
          <div class="feature">✓ 查看已缓存的项目数据</div>
          <div class="feature">✓ 编辑项目树和文档</div>
          <div class="feature">✓ 使用 AI 助手功能</div>
          <div class="feature">✓ 数据将在联网后自动同步</div>
        </div>
        
        <button class="retry-btn" onclick="window.location.reload()">
          重新连接
        </button>
      </div>
    </body>
    </html>
  `;
}

// 监听消息事件
self.addEventListener('message', (event) => {
  const { type, data } = event.data;
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;
      
    case 'GET_VERSION':
      event.ports[0].postMessage({ version: CACHE_NAME });
      break;
      
    case 'CLEAR_CACHE':
      clearAllCaches().then(() => {
        event.ports[0].postMessage({ success: true });
      });
      break;
      
    default:
      console.log('未知消息类型:', type);
  }
});

// 清理所有缓存
async function clearAllCaches() {
  const cacheNames = await caches.keys();
  await Promise.all(
    cacheNames.map(cacheName => caches.delete(cacheName))
  );
  console.log('所有缓存已清理');
}

// 定期清理过期缓存
setInterval(() => {
  cleanupExpiredCache();
}, 24 * 60 * 60 * 1000); // 每24小时清理一次

async function cleanupExpiredCache() {
  try {
    const cache = await caches.open(RUNTIME_CACHE);
    const requests = await cache.keys();
    
    for (const request of requests) {
      const response = await cache.match(request);
      if (response) {
        const dateHeader = response.headers.get('date');
        if (dateHeader) {
          const responseDate = new Date(dateHeader);
          const now = new Date();
          const daysDiff = (now.getTime() - responseDate.getTime()) / (1000 * 60 * 60 * 24);
          
          // 删除超过7天的缓存
          if (daysDiff > 7) {
            await cache.delete(request);
            console.log('删除过期缓存:', request.url);
          }
        }
      }
    }
  } catch (error) {
    console.error('清理过期缓存失败:', error);
  }
}
