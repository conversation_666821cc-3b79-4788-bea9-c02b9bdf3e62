<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试手动添加项目功能</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #2563eb;
        }
        .button.green {
            background: #10b981;
        }
        .button.green:hover {
            background: #059669;
        }
        .project-list {
            margin-top: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }
        .project-item {
            padding: 15px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .project-item:last-child {
            border-bottom: none;
        }
        .project-info h3 {
            margin: 0 0 5px 0;
            color: #1f2937;
        }
        .project-info p {
            margin: 0;
            color: #6b7280;
            font-size: 14px;
        }
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .status.linked {
            background: #d1fae5;
            color: #065f46;
        }
        .status.unlinked {
            background: #fef3c7;
            color: #92400e;
        }
        .log {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 dev-daily v2 多项目管理测试</h1>
        <p>这个页面用于测试本地项目与在线项目的关联功能</p>
        
        <div>
            <button class="button green" onclick="addCloudflareProject()">
                ➕ 添加 Cloudflare 分析项目
            </button>
            <button class="button" onclick="loadProjects()">
                🔄 刷新项目列表
            </button>
            <button class="button" onclick="clearProjects()">
                🗑️ 清空项目列表
            </button>
        </div>

        <div id="projectList" class="project-list">
            <!-- 项目列表将在这里显示 -->
        </div>

        <div id="log" class="log">
            <strong>操作日志:</strong><br>
            页面已加载，等待操作...<br>
        </div>
    </div>

    <script>
        // 模拟 MultiProjectLocalScanner 的核心功能
        const LOCAL_PROJECTS_KEY = 'dev-daily-v2-local-projects';
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function getLocalProjects() {
            const stored = localStorage.getItem(LOCAL_PROJECTS_KEY);
            return stored ? JSON.parse(stored) : [];
        }

        function saveLocalProjects(projects) {
            localStorage.setItem(LOCAL_PROJECTS_KEY, JSON.stringify(projects));
            log(`已保存 ${projects.length} 个项目到本地存储`);
        }

        function addCloudflareProject() {
            const projects = getLocalProjects();
            
            // 检查是否已存在
            const existing = projects.find(p => p.name === 'cloudflare-user-analytics');
            if (existing) {
                log('❌ Cloudflare 分析项目已存在');
                return;
            }

            const newProject = {
                path: 'C:\\Users\\<USER>\\Desktop\\VScode\\cloudflare-user-analytics',
                name: 'cloudflare-user-analytics',
                type: 'web',
                framework: ['Cloudflare Workers', 'TypeScript', 'Hono'],
                packageManager: 'npm',
                gitRepository: true,
                lastModified: new Date().toISOString(),
                size: 5000000,
                fileCount: 25,
                structure: {
                    src: ['workers', 'database', 'dashboard'],
                    components: ['analytics-collector', 'dashboard', 'api'],
                    services: ['MultiProjectLocalScanner', 'AnalyticsUtils'],
                    pages: ['dashboard'],
                    tests: ['tests'],
                    docs: ['docs'],
                    config: ['wrangler.toml', 'package.json'],
                    assets: ['public']
                },
                dependencies: ['hono', '@cloudflare/workers-types', 'typescript'],
                scripts: {
                    'dev': 'wrangler dev',
                    'deploy': 'wrangler deploy',
                    'build': 'npm run build'
                },
                linkedProjectId: '', // 未关联
                healthScore: 85
            };

            projects.push(newProject);
            saveLocalProjects(projects);
            log('✅ 成功添加 Cloudflare 用户行为分析项目');
            loadProjects();
        }

        function loadProjects() {
            const projects = getLocalProjects();
            const listDiv = document.getElementById('projectList');
            
            if (projects.length === 0) {
                listDiv.innerHTML = '<div style="padding: 20px; text-align: center; color: #6b7280;">暂无项目，点击上方按钮添加项目</div>';
                log('📋 项目列表为空');
                return;
            }

            listDiv.innerHTML = projects.map(project => `
                <div class="project-item">
                    <div class="project-info">
                        <h3>📁 ${project.name}</h3>
                        <p>📍 ${project.path}</p>
                        <p>🛠️ ${project.framework.join(', ')}</p>
                        <p>📊 健康度: ${project.healthScore}% | 📁 ${project.fileCount} 个文件</p>
                    </div>
                    <div>
                        <div class="status ${project.linkedProjectId ? 'linked' : 'unlinked'}">
                            ${project.linkedProjectId ? '🔗 已关联' : '⚠️ 未关联'}
                        </div>
                    </div>
                </div>
            `).join('');
            
            log(`📋 已加载 ${projects.length} 个项目`);
        }

        function clearProjects() {
            if (confirm('确定要清空所有项目吗？')) {
                localStorage.removeItem(LOCAL_PROJECTS_KEY);
                log('🗑️ 已清空所有项目');
                loadProjects();
            }
        }

        // 页面加载时自动加载项目
        window.onload = function() {
            log('🚀 测试页面已加载');
            loadProjects();
        };
    </script>
</body>
</html>
