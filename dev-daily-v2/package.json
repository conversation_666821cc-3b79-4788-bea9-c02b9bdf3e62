{"name": "dev-daily-v2", "version": "2.0.0", "description": "Enhanced dev-daily with Project Tree management and AI assistance", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build-check": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "migrate": "tsx scripts/migrate-data.ts", "backup": "tsx scripts/create-backup.ts", "restore": "tsx scripts/restore-backup.ts", "test": "tsx scripts/start-test.ts", "start-test": "tsx scripts/start-test.ts", "generate-test-data": "tsx scripts/generate-test-data.ts", "deploy": "npm run build && wrangler pages deploy dist --project-name=dev-daily-v2", "deploy-preview": "npm run build && wrangler pages deploy dist --project-name=dev-daily-v2 --branch=preview"}, "dependencies": {"clsx": "^2.0.0", "date-fns": "^2.30.0", "fuse.js": "^7.0.0", "highlight.js": "^11.9.0", "lucide-react": "^0.294.0", "marked": "^11.1.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hotkeys-hook": "^4.4.1", "react-markdown": "^9.0.1", "react-router-dom": "^6.20.1", "rehype-highlight": "^7.0.0", "remark-gfm": "^4.0.0", "zustand": "^4.4.7"}, "devDependencies": {"@types/marked": "^6.0.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "tsx": "^4.6.2", "typescript": "^5.2.2", "vite": "^6.3.5", "wrangler": "^3.0.0"}, "keywords": ["dev-daily", "project-management", "ai-assistant", "project-tree", "development-tools"], "author": "dev-daily team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/dev-daily-v2.git"}}