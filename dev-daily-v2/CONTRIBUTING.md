# 贡献指南

感谢您对 dev-daily v2 项目的关注！我们欢迎所有形式的贡献。

## 🚀 快速开始

### 开发环境设置

1. **Fork 项目**
   ```bash
   # 在 GitHub 上 fork 项目，然后克隆到本地
   git clone https://github.com/your-username/dev-daily-v2.git
   cd dev-daily-v2/dev-daily-v2
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动开发服务器**
   ```bash
   npm run dev
   ```

4. **运行类型检查**
   ```bash
   npm run type-check
   ```

## 📝 开发规范

### 代码风格

- 使用 TypeScript 进行开发
- 遵循 ESLint 配置
- 使用 Prettier 格式化代码
- 组件使用 PascalCase 命名
- 文件名使用 kebab-case 或 PascalCase

### 提交信息规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型说明：**
- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例：**
```
feat(project-tree): add drag and drop functionality

Add support for dragging nodes to reorganize project structure.
Includes visual feedback and validation.

Closes #123
```

## 🐛 报告问题

### 问题报告模板

**环境信息：**
- 操作系统: [e.g. macOS 14.0]
- 浏览器: [e.g. Chrome 120.0]
- Node.js 版本: [e.g. 18.19.0]
- 项目版本: [e.g. v2.0.0]

**问题描述：**
[清晰描述遇到的问题]

**重现步骤：**
1. 打开 [页面/功能]
2. 点击 [按钮/元素]
3. 观察到 [错误现象]

**期望行为：**
[描述期望的正确行为]

**截图/录屏：**
[如果适用，提供视觉证据]

## 💡 功能建议

### 功能建议模板

**功能概述：**
[简要描述建议的功能]

**使用场景：**
[描述具体的使用场景和用户需求]

**设计思路：**
[如果有，提供设计思路或原型]

**技术考虑：**
[技术实现的考虑因素]

## 🔧 开发流程

### 分支管理

- `main`: 主分支，包含稳定的生产代码
- `develop`: 开发分支，包含最新的开发代码
- `feature/*`: 功能分支，用于开发新功能
- `fix/*`: 修复分支，用于修复 bug
- `docs/*`: 文档分支，用于文档更新

### Pull Request 流程

1. **创建分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **开发功能**
   - 编写代码
   - 添加测试
   - 更新文档

3. **提交代码**
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

4. **推送分支**
   ```bash
   git push origin feature/your-feature-name
   ```

5. **创建 Pull Request**
   - 在 GitHub 上创建 PR
   - 填写 PR 模板
   - 等待代码审查

### Pull Request 模板

**变更类型：**
- [ ] 新功能
- [ ] Bug 修复
- [ ] 文档更新
- [ ] 代码重构
- [ ] 性能优化

**变更描述：**
[描述你的变更内容]

**测试：**
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 手动测试通过

**检查清单：**
- [ ] 代码遵循项目规范
- [ ] 添加了必要的测试
- [ ] 更新了相关文档
- [ ] 没有引入新的警告

## 🧪 测试

### 运行测试

```bash
# 启动测试环境
npm run start-test

# 类型检查
npm run type-check

# 构建测试
npm run build
```

### 测试覆盖

- 新功能必须包含相应的测试
- 修复 bug 时应添加回归测试
- 保持测试的简洁和可读性

## 📚 文档

### 文档更新

- 新功能需要更新相关文档
- API 变更需要更新接口文档
- 重要变更需要更新 README

### 文档规范

- 使用清晰的标题和结构
- 提供代码示例
- 包含必要的截图或图表
- 保持文档的时效性

## 🎯 发布流程

### 版本管理

使用 [Semantic Versioning](https://semver.org/) 规范：

- `MAJOR`: 不兼容的 API 变更
- `MINOR`: 向后兼容的功能新增
- `PATCH`: 向后兼容的问题修复

### 发布检查清单

- [ ] 所有测试通过
- [ ] 文档已更新
- [ ] 变更日志已更新
- [ ] 版本号已更新
- [ ] 部署测试通过

## 📞 联系我们

如果您有任何问题或建议，请通过以下方式联系我们：

- 📧 邮箱: <EMAIL>
- 🐛 GitHub Issues: [项目 Issues](https://github.com/your-org/dev-daily-v2/issues)
- 💬 GitHub Discussions: [项目讨论](https://github.com/your-org/dev-daily-v2/discussions)

感谢您的贡献！🎉
