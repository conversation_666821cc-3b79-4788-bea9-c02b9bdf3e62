name = "dev-daily-v2"
compatibility_date = "2024-07-10"
pages_build_output_dir = "dist"

[env.production]
name = "dev-daily-v2"

[env.preview]
name = "dev-daily-v2-preview"

# 环境变量配置
[env.production.vars]
ENVIRONMENT = "production"
NODE_ENV = "production"

[env.preview.vars]
ENVIRONMENT = "preview"
NODE_ENV = "development"

# 文档同步服务配置 (待配置)
# [[env.production.d1_databases]]
# binding = "DB"
# database_name = "dev-daily-sync"
# database_id = "your-database-id"

# [[env.production.r2_buckets]]
# binding = "DOCUMENTS_BUCKET"
# bucket_name = "dev-daily-documents"

# [[env.production.r2_buckets]]
# binding = "BACKUPS_BUCKET"
# bucket_name = "dev-daily-backups"

# [[env.production.r2_buckets]]
# binding = "ASSETS_BUCKET"
# bucket_name = "dev-daily-assets"
