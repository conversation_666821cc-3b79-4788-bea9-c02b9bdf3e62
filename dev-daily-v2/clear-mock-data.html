<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧹 清理模拟数据</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .status-card {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
        .status-card.has-data {
            border-color: #f59e0b;
            background: #fffbeb;
        }
        .status-card.clean {
            border-color: #10b981;
            background: #ecfdf5;
        }
        .button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s;
        }
        .button:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }
        .button.danger {
            background: #ef4444;
        }
        .button.danger:hover {
            background: #dc2626;
        }
        .button.success {
            background: #10b981;
        }
        .button.success:hover {
            background: #059669;
        }
        .log {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .data-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .data-item:last-child {
            border-bottom: none;
        }
        .count {
            background: #dbeafe;
            color: #1e40af;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .warning {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            color: #92400e;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧹 dev-daily v2 数据清理工具</h1>
            <p>清理模拟数据，为真实项目数据让路</p>
        </div>

        <!-- 当前数据状态 -->
        <div class="status-card" id="statusCard">
            <h2>📊 当前数据状态</h2>
            <div id="dataStatus">
                <p>正在检查数据状态...</p>
            </div>
        </div>

        <!-- 警告信息 -->
        <div class="warning">
            <strong>⚠️ 注意：</strong> 清理操作将删除所有本地存储的项目数据，包括：
            <ul>
                <li>本地项目列表</li>
                <li>工作空间配置</li>
                <li>项目关联信息</li>
                <li>扫描历史记录</li>
            </ul>
            请确保您已经备份了重要数据！
        </div>

        <!-- 操作按钮 -->
        <div style="text-align: center; margin: 30px 0;">
            <button class="button" onclick="checkDataStatus()">
                🔍 检查数据状态
            </button>
            <button class="button danger" onclick="clearAllData()">
                🗑️ 清理所有数据
            </button>
            <button class="button success" onclick="resetSystem()">
                🔄 重置系统
            </button>
        </div>

        <!-- 详细操作 -->
        <div class="status-card">
            <h3>🎯 精确清理</h3>
            <div class="data-item">
                <span>本地项目数据</span>
                <button class="button" onclick="clearLocalProjects()">清理</button>
            </div>
            <div class="data-item">
                <span>多项目数据</span>
                <button class="button" onclick="clearMultiProjectData()">清理</button>
            </div>
            <div class="data-item">
                <span>缓存数据</span>
                <button class="button" onclick="clearCacheData()">清理</button>
            </div>
        </div>

        <!-- 操作日志 -->
        <div class="log" id="log">
            [系统] 数据清理工具已就绪<br>
            [提示] 点击"检查数据状态"查看当前数据情况<br>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '[错误]' : type === 'success' ? '[成功]' : type === 'warning' ? '[警告]' : '[信息]';
            const color = type === 'error' ? '#ef4444' : type === 'success' ? '#10b981' : type === 'warning' ? '#f59e0b' : '#3b82f6';
            logDiv.innerHTML += `<span style="color: ${color}">${prefix}</span> [${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function getItemCount(key) {
            try {
                const data = localStorage.getItem(key);
                if (!data) return 0;
                const parsed = JSON.parse(data);
                return Array.isArray(parsed) ? parsed.length : Object.keys(parsed).length;
            } catch {
                return 0;
            }
        }

        function checkDataStatus() {
            log('🔍 检查数据状态...');
            
            const localProjectsCount = getItemCount('dev-daily-v2-local-projects');
            const workspacesCount = getItemCount('dev-daily-v2-workspaces');
            const projectsCount = getItemCount('dev-daily-v2-projects');
            const scanHistoryCount = getItemCount('dev-daily-v2-scan-history');
            
            const totalItems = localProjectsCount + workspacesCount + projectsCount + scanHistoryCount;
            
            const statusCard = document.getElementById('statusCard');
            const dataStatus = document.getElementById('dataStatus');
            
            if (totalItems === 0) {
                statusCard.className = 'status-card clean';
                dataStatus.innerHTML = `
                    <h3 style="color: #10b981;">✅ 系统干净</h3>
                    <p>没有发现任何模拟数据，系统处于干净状态。</p>
                `;
                log('✅ 系统干净，没有发现模拟数据', 'success');
            } else {
                statusCard.className = 'status-card has-data';
                dataStatus.innerHTML = `
                    <h3 style="color: #f59e0b;">📊 发现数据</h3>
                    <div class="data-item">
                        <span>本地项目</span>
                        <span class="count">${localProjectsCount} 项</span>
                    </div>
                    <div class="data-item">
                        <span>工作空间</span>
                        <span class="count">${workspacesCount} 项</span>
                    </div>
                    <div class="data-item">
                        <span>项目数据</span>
                        <span class="count">${projectsCount} 项</span>
                    </div>
                    <div class="data-item">
                        <span>扫描历史</span>
                        <span class="count">${scanHistoryCount} 项</span>
                    </div>
                    <div class="data-item">
                        <strong>总计</strong>
                        <span class="count">${totalItems} 项</span>
                    </div>
                `;
                log(`📊 发现 ${totalItems} 项数据`, 'warning');
            }
        }

        function clearLocalProjects() {
            const keys = [
                'dev-daily-v2-local-projects',
                'dev-daily-v2-scan-history',
                'dev-daily-v2-project-links'
            ];
            
            keys.forEach(key => {
                localStorage.removeItem(key);
                log(`🗑️ 已清理: ${key}`);
            });
            
            log('✅ 本地项目数据清理完成', 'success');
            checkDataStatus();
        }

        function clearMultiProjectData() {
            const keys = [
                'dev-daily-v2-workspaces',
                'dev-daily-v2-current-workspace',
                'dev-daily-v2-projects'
            ];
            
            keys.forEach(key => {
                localStorage.removeItem(key);
                log(`🗑️ 已清理: ${key}`);
            });
            
            log('✅ 多项目数据清理完成', 'success');
            checkDataStatus();
        }

        function clearCacheData() {
            const keys = [
                'dev-daily-v2-settings',
                'dev-daily-v2-user-preferences',
                'dev-daily-v2-temp-data'
            ];
            
            keys.forEach(key => {
                localStorage.removeItem(key);
                log(`🗑️ 已清理: ${key}`);
            });
            
            log('✅ 缓存数据清理完成', 'success');
            checkDataStatus();
        }

        function clearAllData() {
            if (!confirm('确定要清理所有数据吗？此操作不可撤销！')) {
                log('❌ 用户取消了清理操作', 'warning');
                return;
            }
            
            log('🧹 开始清理所有数据...');
            
            clearLocalProjects();
            clearMultiProjectData();
            clearCacheData();
            
            log('✅ 所有数据清理完成！', 'success');
        }

        function resetSystem() {
            if (!confirm('确定要重置系统吗？这将清理所有数据并重新初始化！')) {
                log('❌ 用户取消了重置操作', 'warning');
                return;
            }
            
            log('🔄 开始重置系统...');
            
            // 清理所有数据
            clearAllData();
            
            // 重新初始化（这里可以添加重新初始化的逻辑）
            log('🔄 重新初始化基础数据结构...');
            
            // 刷新页面状态
            setTimeout(() => {
                checkDataStatus();
                log('✅ 系统重置完成！', 'success');
            }, 1000);
        }

        // 页面加载时自动检查状态
        window.onload = function() {
            log('🚀 数据清理工具已启动');
            checkDataStatus();
        };
    </script>
</body>
</html>
