# 🚀 dev-daily v2 - 智能项目管理系统

<div align="center">

[![Version](https://img.shields.io/badge/version-2.0.0-blue.svg)](https://github.com/your-org/dev-daily-v2)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.2+-blue.svg)](https://www.typescriptlang.org/)
[![React](https://img.shields.io/badge/React-18.2+-61DAFB.svg)](https://reactjs.org/)
[![Vite](https://img.shields.io/badge/Vite-6.3+-646CFF.svg)](https://vitejs.dev/)
[![Cloudflare](https://img.shields.io/badge/Cloudflare-Pages-F38020.svg)](https://pages.cloudflare.com/)

**基于 Project Tree 概念的现代化智能开发日志管理系统**

[🌐 在线演示](https://dev-daily-v2.pages.dev) • [📖 文档](https://dev-daily-v2.pages.dev/docs) • [🐛 问题反馈](https://github.com/your-org/dev-daily-v2/issues)

## 🚀 部署状态

- **生产环境**: [![Deploy Status](https://github.com/your-org/dev-daily-v2/workflows/CI%2FCD%20Pipeline/badge.svg?branch=master)](https://github.com/your-org/dev-daily-v2/actions)
- **预览环境**: [![Deploy Status](https://github.com/your-org/dev-daily-v2/workflows/CI%2FCD%20Pipeline/badge.svg?branch=develop)](https://github.com/your-org/dev-daily-v2/actions)

### 自动化部署流程
- 🔄 **GitHub Actions**: 自动化 CI/CD 流程
- 📦 **Cloudflare Pages**: 全球 CDN 加速部署
- 🌍 **多环境支持**: 生产/预览/PR 预览环境
- ✅ **质量检查**: TypeScript 类型检查 + ESLint

</div>

---

## ✨ 核心特性

### 🌳 智能项目树管理
- **可视化结构**: 直观的树形项目结构展示
- **拖拽重组**: 支持节点拖拽调整层级关系
- **实时同步**: 变更历史记录和实时状态更新
- **多层级支持**: 项目 → 模块 → 任务 → 文档的完整层级

### 🤖 AI 智能助手
- **文档分析**: 自动提取关键信息和主题
- **项目生成**: 基于文档内容智能生成项目结构
- **智能建议**: 提供项目优化和改进建议
- **思维模型**: 集成多种思维框架辅助决策

### 📊 可视化仪表板
- **进度追踪**: 实时项目进度和完成度统计
- **数据洞察**: 多维度数据分析和可视化图表
- **快速操作**: 常用功能的快捷入口
- **状态概览**: 项目健康度和关键指标监控

### 📝 文档管理系统
- **智能分类**: 自动文档类型识别和分类
- **全文搜索**: 强大的搜索和过滤功能
- **Markdown 支持**: 完整的 Markdown 编辑和预览
- **关联管理**: 文档与项目节点的智能关联

## 🚀 快速开始

### 📋 环境要求

| 工具 | 版本要求 | 说明 |
|------|----------|------|
| Node.js | 18.0+ | 推荐使用 LTS 版本 |
| npm | 9.0+ | 包管理器 |
| 浏览器 | 现代浏览器 | Chrome 90+, Firefox 88+, Safari 14+ |

### ⚡ 一键启动

```bash
# 1. 克隆项目
git clone https://github.com/your-org/dev-daily-v2.git
cd dev-daily-v2/dev-daily-v2

# 2. 安装依赖
npm install

# 3. 启动开发服务器
npm run dev

# 4. 打开浏览器访问
# http://localhost:5173
```

### 🧪 测试环境

```bash
# 启动测试环境 (包含测试数据)
npm run start-test

# 类型检查
npm run type-check

# 构建项目
npm run build
```

### 🌐 部署到 Cloudflare Pages

```bash
# 部署到生产环境
npm run deploy

# 部署到预览环境
npm run deploy-preview
```

## 🏗️ 技术架构

### 🛠️ 技术栈

| 分类 | 技术 | 版本 | 说明 |
|------|------|------|------|
| **前端框架** | React | 18.3+ | 现代化 UI 框架 |
| **开发语言** | TypeScript | 5.2+ | 类型安全的 JavaScript |
| **构建工具** | Vite | 6.3+ | 快速的前端构建工具 |
| **样式框架** | Tailwind CSS | 3.4+ | 实用优先的 CSS 框架 |
| **状态管理** | Zustand | 4.5+ | 轻量级状态管理 |
| **路由** | React Router | 6.30+ | 客户端路由 |
| **图标** | Lucide React | 0.294+ | 现代化图标库 |
| **Markdown** | React Markdown | 9.1+ | Markdown 渲染 |
| **部署平台** | Cloudflare Pages | - | 全球 CDN 部署 |

### 📁 项目结构

```
dev-daily-v2/
├── src/
│   ├── components/          # React 组件
│   │   ├── Dashboard/       # 仪表板组件
│   │   ├── ProjectTree/     # 项目树组件
│   │   ├── Documents/       # 文档管理组件
│   │   ├── AI/             # AI 助手组件
│   │   ├── Calendar/       # 日历组件
│   │   ├── Roadmap/        # 路线图组件
│   │   └── Settings/       # 设置组件
│   ├── services/           # 业务逻辑服务
│   ├── types/              # TypeScript 类型定义
│   ├── hooks/              # 自定义 React Hooks
│   └── utils/              # 工具函数
├── public/                 # 静态资源
├── dist/                   # 构建输出
└── docs/                   # 项目文档
```

### 🔧 核心架构特性

- **🎯 组件化设计**: 模块化的 React 组件架构
- **📦 服务层抽象**: 清晰的业务逻辑分离
- **🔒 类型安全**: 100% TypeScript 覆盖
- **⚡ 性能优化**: Vite 构建 + 代码分割
- **🌐 全球部署**: Cloudflare Pages 边缘网络
- **📱 响应式**: 移动端友好的自适应设计

## 🎮 功能特性详解

### 🌳 Project Tree 管理

**核心功能**
- **可视化结构**: 树形展示项目层级关系
- **拖拽重组**: 支持节点间的拖拽移动
- **状态管理**: 多种状态标识（计划中、进行中、已完成、阻塞、暂停）
- **进度追踪**: 实时进度百分比和完成度统计
- **历史记录**: 完整的变更历史和操作日志

**节点类型**
- 🏗️ **项目**: 顶级项目容器
- 📦 **模块**: 功能模块划分
- ✅ **任务**: 具体执行任务
- 📄 **文档**: 相关文档资料
- 🎯 **特性**: 功能特性描述

### 📊 智能仪表板

**数据概览**
- 📈 **进度统计**: 项目完成度和趋势分析
- 📋 **任务分布**: 不同状态任务的数量统计
- 📅 **时间线**: 项目时间轴和里程碑
- 🔥 **活跃度**: 最近活动和更新频率

**快速操作**
- ➕ **快速创建**: 一键创建项目、任务、文档
- 🔍 **智能搜索**: 全局搜索和快速定位
- 📌 **收藏夹**: 常用项目和文档收藏
- 🔔 **通知中心**: 重要更新和提醒

### 🤖 AI 智能助手

**文档分析**
- 🧠 **内容理解**: 自动提取文档关键信息
- 🏷️ **智能标签**: 自动生成相关标签
- 📊 **情感分析**: 识别文档情感倾向
- 🔗 **关联推荐**: 推荐相关文档和项目

**项目生成**
- 🏗️ **结构生成**: 基于文档自动生成项目树
- 📋 **任务分解**: 智能拆分复杂任务
- ⏱️ **时间估算**: 基于历史数据估算工期
- 🎯 **优先级建议**: 智能优先级排序

### 📝 文档管理系统

**文档类型**
- 📋 **日志**: 开发日志和工作记录
- 🐛 **问题**: 问题报告和解决方案
- 🚀 **部署**: 部署指南和配置文档
- 📊 **总结**: 项目总结和复盘
- 📖 **指南**: 操作指南和最佳实践

**高级功能**
- 🔍 **全文搜索**: 支持内容、标题、标签搜索
- 🏷️ **智能分类**: 自动文档分类和归档
- 🔗 **关联管理**: 文档与项目节点的双向关联
- 📤 **导入导出**: 支持多种格式的导入导出

## 🛠️ 开发指南

### 📦 可用脚本

```bash
# 开发相关
npm run dev              # 启动开发服务器
npm run build            # 构建生产版本
npm run preview          # 预览构建结果
npm run type-check       # TypeScript 类型检查

# 测试相关
npm run test             # 启动测试环境
npm run start-test       # 启动测试环境（别名）
npm run generate-test-data # 生成测试数据

# 部署相关
npm run deploy           # 部署到生产环境
npm run deploy-preview   # 部署到预览环境

# 代码质量
npm run lint             # ESLint 代码检查
```

### 🔧 开发环境配置

**1. 克隆项目**
```bash
git clone https://github.com/your-org/dev-daily-v2.git
cd dev-daily-v2/dev-daily-v2
```

**2. 安装依赖**
```bash
npm install
```

**3. 启动开发服务器**
```bash
npm run dev
```

**4. 访问应用**
- 开发环境: http://localhost:5173
- 网络访问: http://[your-ip]:5173

### 🏗️ 构建和部署

**本地构建**
```bash
# 构建项目
npm run build

# 预览构建结果
npm run preview
```

**Cloudflare Pages 部署**
```bash
# 部署到生产环境
npm run deploy

# 部署到预览环境
npm run deploy-preview
```

### 🧪 测试和调试

**启动测试环境**
```bash
npm run start-test
```

测试环境包含：
- 📄 5个预置测试文档
- 🌳 12个项目树节点
- 🤖 AI分析示例数据
- 📊 完整的统计数据

## 🤝 贡献指南

我们欢迎所有形式的贡献！无论是报告 bug、提出新功能建议，还是提交代码改进。

### 🐛 报告问题

**在提交问题前，请确保：**
1. 搜索现有 issues，避免重复报告
2. 使用最新版本进行测试
3. 提供详细的重现步骤

**问题报告应包含：**
- 🖥️ **环境信息**: 操作系统、浏览器版本、Node.js 版本
- 📝 **问题描述**: 清晰描述遇到的问题
- 🔄 **重现步骤**: 详细的操作步骤
- 📸 **截图/录屏**: 如果适用，提供视觉证据
- 💡 **期望行为**: 描述期望的正确行为

### 💡 功能建议

**提交功能建议时，请包含：**
- 🎯 **使用场景**: 描述具体的使用场景
- 💼 **业务价值**: 说明功能的价值和必要性
- 🎨 **设计思路**: 如果有，提供设计思路或原型
- 🔧 **技术考虑**: 技术实现的考虑因素

### 🔧 代码贡献

**开发流程：**
1. **Fork** 项目到你的 GitHub 账户
2. **创建分支**: `git checkout -b feature/your-feature-name`
3. **开发功能**: 遵循项目的代码规范
4. **测试**: 确保所有测试通过
5. **提交**: 使用清晰的提交信息
6. **推送**: `git push origin feature/your-feature-name`
7. **PR**: 创建 Pull Request

**代码规范：**
- 📝 使用 TypeScript 进行类型安全开发
- 🎨 遵循 ESLint 和 Prettier 配置
- 📖 为新功能添加适当的注释
- 🧪 为新功能编写测试用例
- 📚 更新相关文档

### 🏷️ 提交信息规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型说明：**
- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

## 📊 性能指标

### 🎯 目标性能
- **首屏加载**: < 2秒
- **界面响应**: < 500ms
- **拖拽操作**: < 100ms
- **搜索响应**: < 200ms

### ✅ 功能完整性
- **核心功能**: 100% 完成
- **用户界面**: 100% 完成
- **类型安全**: 100% TypeScript 覆盖
- **响应式设计**: 支持所有主流设备

## 📝 更新日志

### v2.0.0 (2025-07-18) - 🎉 正式发布
- ✅ 完成 Project Tree 核心功能
- ✅ 实现 AI 智能分析功能
- ✅ 开发现代化 Web 界面
- ✅ 创建完整的测试环境
- ✅ 修复所有 TypeScript 类型错误
- ✅ 升级到 Vite 6.x，修复安全漏洞
- ✅ 移除已弃用的依赖包
- ✅ 完善 Cloudflare Pages 部署配置

### 🔮 下一步计划

**v2.1.0 - AI 功能增强**
- 🤖 集成真实 AI API (OpenAI/Claude)
- 🎭 实现思维模型辅助决策
- ⚙️ 高级设置和配置界面
- 💬 真实 AI 对话功能

**v2.2.0 - 数据持久化**
- 💾 本地存储实现
- ☁️ 云端同步功能
- 📤 数据导入导出
- 🔄 备份恢复机制

## 📄 许可证

本项目采用 [MIT License](LICENSE) 开源协议。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

特别感谢：
- [React](https://reactjs.org/) - 强大的 UI 框架
- [Vite](https://vitejs.dev/) - 快速的构建工具
- [Tailwind CSS](https://tailwindcss.com/) - 优秀的 CSS 框架
- [Cloudflare Pages](https://pages.cloudflare.com/) - 可靠的部署平台

## 📞 联系我们

- 🌐 **项目主页**: https://dev-daily-v2.pages.dev
- 📧 **邮箱**: <EMAIL>
- 🐛 **问题反馈**: [GitHub Issues](https://github.com/your-org/dev-daily-v2/issues)
- 💬 **讨论**: [GitHub Discussions](https://github.com/your-org/dev-daily-v2/discussions)

---

<div align="center">

**⭐ 如果这个项目对你有帮助，请给我们一个 Star！**

Made with ❤️ by dev-daily team

</div>
