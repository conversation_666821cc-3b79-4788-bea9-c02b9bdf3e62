# dev-daily v2 环境变量配置示例
# 复制此文件为 .env.local 并填入实际值

# 应用环境
NODE_ENV=development
ENVIRONMENT=development

# Cloudflare 配置 (用于文档同步功能)
# 获取方式: https://dash.cloudflare.com/profile/api-tokens
CLOUDFLARE_ACCOUNT_ID=your_account_id_here
CLOUDFLARE_API_TOKEN=your_api_token_here

# Cloudflare R2 存储配置
R2_ENDPOINT=https://your_account_id.r2.cloudflarestorage.com
R2_ACCESS_KEY_ID=your_r2_access_key_id
R2_SECRET_ACCESS_KEY=your_r2_secret_access_key
R2_BUCKET_DOCUMENTS=dev-daily-documents
R2_BUCKET_BACKUPS=dev-daily-backups
R2_BUCKET_ASSETS=dev-daily-assets

# Cloudflare D1 数据库配置
D1_DATABASE_ID=your_d1_database_id

# GitHub 集成配置
# 获取方式: https://github.com/settings/tokens
GITHUB_TOKEN=ghp_your_github_token_here

# API 配置
API_BASE_URL=https://api.dev-daily.com
API_VERSION=v1

# 功能开关
ENABLE_DOCUMENT_SYNC=true
ENABLE_AI_ASSISTANT=true
ENABLE_GITHUB_INTEGRATION=true
ENABLE_ANALYTICS=false

# 调试配置
DEBUG=false
LOG_LEVEL=info
