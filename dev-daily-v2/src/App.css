/* 基础应用样式 */
.App {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 自定义工具类 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* 拖拽效果 */
.dragging {
  opacity: 0.5;
  transform: rotate(2deg);
  transition: all 0.2s ease;
}

.drag-over {
  background-color: #dbeafe;
  border-color: #3b82f6;
  transition: all 0.2s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }

  .App {
    height: 100vh;
    height: 100dvh; /* 动态视口高度 */
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
}
