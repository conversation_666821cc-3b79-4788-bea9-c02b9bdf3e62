import React, { useState, useEffect } from 'react';
import {
  LayoutDashboard,
  GitBranch,
  FileText,
  Brain,
  Settings,
  Archive,
  Shield,
  Menu,
  X,
  Plus,
  Lightbulb,
  BookOpen,
  Calendar,
  Target,
  RefreshCw as Sync,
  Folder,
  Code,
  RefreshCw,
  Github,
  Activity,
  Database
} from 'lucide-react';
import { ProjectTreeContainer } from '../../components/ProjectTree/ProjectTreeContainer';
import { DocumentManager } from '../../components/Documents/DocumentManager';
import { AIAssistant } from '../../components/AI/AIAssistant';
import { ThinkingModelsPage } from '../../components/ThinkingModels/ThinkingModelsPage';
import { SettingsPage } from '../../components/Settings/SettingsPage';
import { UserGuide } from '../../components/Guide/UserGuide';
import { DocumentSyncManager } from '../../components/DocumentSync/DocumentSyncManager';
import { BackupManager } from '../../components/Backup/BackupManager';
import { ProjectCalendar } from '../../components/Calendar/ProjectCalendar';
import { ProjectRoadmap } from '../../components/Roadmap/ProjectRoadmap';
import { DevSyncDashboard } from '../../components/DevSync/DevSyncDashboard';
import { ProjectSelector } from '../../components/MultiProject/ProjectSelector';
import { LocalProjectManager } from '../../components/LocalDirectory/LocalProjectManager';
import { DevEnvironmentIntegration } from '../../components/LocalDirectory/DevEnvironmentIntegration';
import { MultiProjectSyncManager } from '../../components/LocalDirectory/MultiProjectSyncManager';
import { MultiProjectService } from '../../services/MultiProjectService';
import { Document, ProjectTreeNode, UserSettings } from '../../types/index';
import { DevDailyAdapter } from '../../services/DevDailyAdapter';
import { TestDataService } from '../../services/TestDataService';
import { useProjectContext } from '../../hooks/useProjectContext';
import { WorkflowManager } from '../../components/Workflow/WorkflowManager';
import { GitHubIntegrationPage } from '../../components/GitHub/GitHubIntegrationPage';
import { PerformanceMonitor } from '../../components/Performance/PerformanceMonitor';
import { OfflineIndicator } from '../../components/Offline/OfflineIndicator';
import { OnlinePlatformManager } from '../../components/Integration/OnlinePlatformManager';
import { format } from 'date-fns';
import clsx from 'clsx';

type ViewType = 'dashboard' | 'project-tree' | 'documents' | 'ai-assistant' | 'thinking-models' | 'settings' | 'legacy' | 'backup' | 'guide' | 'calendar' | 'roadmap' | 'dev-sync' | 'local-projects' | 'dev-environment' | 'sync-manager' | 'workflow' | 'github' | 'performance' | 'integrations' | 'document-sync';

interface MainDashboardProps {
  className?: string;
}

// 侧边栏导航组件
const Sidebar: React.FC<{
  activeView: ViewType;
  onViewChange: (view: ViewType) => void;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
}> = ({ activeView, onViewChange, isCollapsed, onToggleCollapse }) => {
  const navigationItems = [
    { id: 'dashboard' as ViewType, name: '仪表板', icon: LayoutDashboard },
    { id: 'project-tree' as ViewType, name: '项目树', icon: GitBranch },
    { id: 'roadmap' as ViewType, name: '路线图', icon: Target },
    { id: 'documents' as ViewType, name: '文档管理', icon: FileText },
    { id: 'calendar' as ViewType, name: '项目日历', icon: Calendar },
    { id: 'workflow' as ViewType, name: '工作流', icon: RefreshCw },
    { id: 'ai-assistant' as ViewType, name: 'AI助手', icon: Brain },
    { id: 'thinking-models' as ViewType, name: '思维模型', icon: Lightbulb },
    { id: 'guide' as ViewType, name: '使用指南', icon: BookOpen },
    { id: 'settings' as ViewType, name: '设置', icon: Settings },
  ];

  const secondaryItems = [
    { id: 'document-sync' as ViewType, name: '文档同步', icon: GitBranch },
    { id: 'github' as ViewType, name: 'GitHub 集成', icon: Github },
    { id: 'integrations' as ViewType, name: '在线平台', icon: Database },
    { id: 'performance' as ViewType, name: '性能监控', icon: Activity },
    { id: 'dev-sync' as ViewType, name: '开发同步', icon: Sync },
    { id: 'local-projects' as ViewType, name: '本地项目', icon: Folder },
    { id: 'dev-environment' as ViewType, name: '开发环境', icon: Code },
    { id: 'sync-manager' as ViewType, name: '数据同步', icon: RefreshCw },
    { id: 'backup' as ViewType, name: '备份管理', icon: Shield },
    { id: 'legacy' as ViewType, name: '经典界面', icon: Archive },
  ];

  return (
    <div className={clsx(
      'bg-gray-900 text-white transition-all duration-300 flex flex-col h-full',
      isCollapsed ? 'w-16' : 'w-64'
    )}>
      {/* 头部 */}
      <div className="p-4 border-b border-gray-700 flex-shrink-0">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <h1 className="text-xl font-bold">dev-daily v2</h1>
          )}
          <button
            onClick={onToggleCollapse}
            className="p-2 hover:bg-gray-700 rounded-md"
          >
            {isCollapsed ? <Menu size={20} /> : <X size={20} />}
          </button>
        </div>
      </div>

      {/* 主导航 */}
      <nav className="flex-1 p-4 overflow-y-auto">
        <div className="space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <button
                key={item.id}
                onClick={() => onViewChange(item.id)}
                className={clsx(
                  'w-full flex items-center gap-3 px-3 py-2 rounded-md transition-colors',
                  activeView === item.id
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                )}
                title={isCollapsed ? item.name : undefined}
              >
                <Icon size={20} />
                {!isCollapsed && <span>{item.name}</span>}
              </button>
            );
          })}
        </div>

        {/* 分隔线 */}
        <div className="my-6 border-t border-gray-700"></div>

        {/* 次要导航 */}
        <div className="space-y-2">
          {secondaryItems.map((item) => {
            const Icon = item.icon;
            return (
              <button
                key={item.id}
                onClick={() => onViewChange(item.id)}
                className={clsx(
                  'w-full flex items-center gap-3 px-3 py-2 rounded-md transition-colors',
                  activeView === item.id
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-400 hover:bg-gray-700 hover:text-gray-300'
                )}
                title={isCollapsed ? item.name : undefined}
              >
                <Icon size={20} />
                {!isCollapsed && <span>{item.name}</span>}
              </button>
            );
          })}
        </div>
      </nav>

      {/* 底部信息 */}
      {!isCollapsed && (
        <div className="p-4 border-t border-gray-700 flex-shrink-0">
          <div className="text-xs text-gray-400">
            <div>版本: v2.0.0</div>
            <div>构建: {new Date().toLocaleDateString()}</div>
          </div>
        </div>
      )}
    </div>
  );
};

// 仪表板概览组件
const DashboardOverview: React.FC<{
  documents: Document[];
  projectTree: ProjectTreeNode | null;
}> = ({ documents, projectTree }) => {
  const stats = {
    totalDocuments: documents.length,
    recentDocuments: documents.filter(doc => {
      const daysDiff = (Date.now() - new Date(doc.metadata.createdAt).getTime()) / (1000 * 60 * 60 * 24);
      return daysDiff <= 7;
    }).length,
    totalNodes: projectTree ? countNodes(projectTree) : 0,
    completedNodes: projectTree ? countNodesByStatus(projectTree, 'completed') : 0,
    activeNodes: projectTree ? countNodesByStatus(projectTree, 'active') : 0,
    blockedNodes: projectTree ? countNodesByStatus(projectTree, 'blocked') : 0,
  };

  function countNodes(node: ProjectTreeNode): number {
    let count = 1;
    if (node.children) {
      count += node.children.reduce((sum, child) => sum + countNodes(child), 0);
    }
    return count;
  }

  function countNodesByStatus(node: ProjectTreeNode, status: ProjectTreeNode['status']): number {
    let count = node.status === status ? 1 : 0;
    if (node.children) {
      count += node.children.reduce((sum, child) => sum + countNodesByStatus(child, status), 0);
    }
    return count;
  }

  const recentDocuments = documents
    .sort((a, b) => new Date(b.metadata.createdAt).getTime() - new Date(a.metadata.createdAt).getTime())
    .slice(0, 5);

  return (
    <div className="p-6 space-y-6 overflow-auto h-full">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">项目概览</h2>
        <p className="text-gray-600">dev-daily v2 项目管理仪表板</p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <FileText className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">总文档数</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalDocuments}</p>
            </div>
          </div>
          <div className="mt-4">
            <span className="text-sm text-gray-500">近7天新增: {stats.recentDocuments}</span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <GitBranch className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">项目节点</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalNodes}</p>
            </div>
          </div>
          <div className="mt-4">
            <span className="text-sm text-gray-500">已完成: {stats.completedNodes}</span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <LayoutDashboard className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">进行中</p>
              <p className="text-2xl font-bold text-gray-900">{stats.activeNodes}</p>
            </div>
          </div>
          <div className="mt-4">
            <span className="text-sm text-gray-500">阻塞: {stats.blockedNodes}</span>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Brain className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">AI建议</p>
              <p className="text-2xl font-bold text-gray-900">0</p>
            </div>
          </div>
          <div className="mt-4">
            <span className="text-sm text-gray-500">待处理: 0</span>
          </div>
        </div>
      </div>

      {/* 项目进度和最近文档 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 项目进度 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">项目进度</h3>
          </div>
          <div className="p-6">
            {projectTree ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">整体完成度</span>
                  <span className="text-sm font-medium text-gray-900">
                    {stats.totalNodes > 0 ? Math.round((stats.completedNodes / stats.totalNodes) * 100) : 0}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-600 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${stats.totalNodes > 0 ? (stats.completedNodes / stats.totalNodes) * 100 : 0}%`
                    }}
                  ></div>
                </div>

                <div className="grid grid-cols-3 gap-4 mt-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{stats.completedNodes}</div>
                    <div className="text-xs text-gray-500">已完成</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{stats.activeNodes}</div>
                    <div className="text-xs text-gray-500">进行中</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{stats.blockedNodes}</div>
                    <div className="text-xs text-gray-500">阻塞</div>
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">暂无项目数据</p>
            )}
          </div>
        </div>

        {/* 最近文档 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">最近文档</h3>
          </div>
          <div className="p-6">
            {recentDocuments.length === 0 ? (
              <p className="text-gray-500 text-center py-4">暂无文档</p>
            ) : (
              <div className="space-y-3">
                {recentDocuments.map((doc) => (
                  <div key={doc.id} className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-md">
                    <div className="flex items-center gap-3">
                      <FileText size={16} className="text-gray-400" />
                      <div>
                        <p className="font-medium text-gray-900">{doc.title}</p>
                        <p className="text-sm text-gray-500">
                          {new Date(doc.metadata.createdAt).toLocaleDateString()} · {doc.metadata.author}
                        </p>
                      </div>
                    </div>
                    <span className={clsx(
                      'px-2 py-1 text-xs font-medium rounded-full',
                      doc.type === 'daily-log' ? 'bg-blue-100 text-blue-800' :
                      doc.type === 'issue-report' ? 'bg-red-100 text-red-800' :
                      doc.type === 'guideline' ? 'bg-green-100 text-green-800' :
                      doc.type === 'summary' ? 'bg-purple-100 text-purple-800' :
                      'bg-gray-100 text-gray-800'
                    )}>
                      {doc.type}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 快速操作 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">快速操作</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Plus className="h-6 w-6 text-blue-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">新建文档</span>
            </button>
            <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <GitBranch className="h-6 w-6 text-green-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">添加节点</span>
            </button>
            <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Brain className="h-6 w-6 text-purple-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">AI分析</span>
            </button>
            <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Archive className="h-6 w-6 text-gray-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">导出数据</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// 占位符组件
const PlaceholderView: React.FC<{ title: string; description: string }> = ({ title, description }) => (
  <div className="flex items-center justify-center h-full">
    <div className="text-center">
      <h2 className="text-2xl font-bold text-gray-900 mb-2">{title}</h2>
      <p className="text-gray-600">{description}</p>
    </div>
  </div>
);

export const MainDashboard: React.FC<MainDashboardProps> = ({ className }) => {
  const [activeView, setActiveView] = useState<ViewType>('dashboard');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [selectedNode, setSelectedNode] = useState<ProjectTreeNode | null>(null);

  // 使用项目上下文获取数据
  const {
    currentProject,
    documents,
    projectTree,
    isLoading,
    error,
    switchProject
  } = useProjectContext();

  // 初始化数据 - 现在由 ProjectProvider 处理
  useEffect(() => {
    // 如果没有当前项目，尝试加载默认项目或创建测试项目
    if (!currentProject && !isLoading) {
      const initializeDefaultProject = async () => {
        try {
          // 获取或创建默认工作空间
          let workspace = MultiProjectService.getCurrentWorkspace();
          if (!workspace) {
            workspace = MultiProjectService.createWorkspace('默认工作空间', '系统默认工作空间');
          }

          // 检查是否有项目
          if (workspace.projects.length === 0) {
            // 创建测试项目
            const testProject = MultiProjectService.createProject({
              name: 'dev-daily-v2 演示项目',
              description: '这是一个演示项目，展示 dev-daily-v2 的功能',
              status: 'active',
              priority: 'high',
              tags: ['demo', 'test']
            });

            // 切换到测试项目
            await switchProject(testProject.id);
          } else {
            // 切换到第一个项目
            await switchProject(workspace.projects[0].id);
          }

          console.log('✅ 默认项目初始化成功');
        } catch (error) {
          console.error('❌ 默认项目初始化失败:', error);
        }
      };

      initializeDefaultProject();
    }
  }, [currentProject, isLoading, switchProject]);

  const renderMainContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-gray-500">正在加载...</div>
        </div>
      );
    }

    switch (activeView) {
      case 'dashboard':
        return <DashboardOverview documents={documents} projectTree={projectTree} />;
      
      case 'project-tree':
        return (
          <div className="h-full">
            {projectTree ? (
              <ProjectTreeContainer
                documents={documents}
                projectTree={projectTree}
                onNodeSelect={setSelectedNode}
                className="h-full"
              />
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="animate-spin h-8 w-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
                  <p className="text-gray-600">正在加载项目树...</p>
                </div>
              </div>
            )}
          </div>
        );
      
      case 'documents':
        return (
          <DocumentManager
            documents={documents}
            selectedNode={selectedNode}
            onDocumentSelect={(doc) => console.log('Selected document:', doc)}
            onDocumentEdit={(doc) => console.log('Edit document:', doc)}
            onDocumentCreate={() => console.log('Create new document')}
            className="h-full"
          />
        );
      
      case 'ai-assistant':
        return (
          <AIAssistant
            documents={documents}
            projectTree={projectTree}
            className="h-full"
          />
        );

      case 'thinking-models':
        return (
          <ThinkingModelsPage
            documents={documents}
            projectTree={projectTree}
            className="h-full"
          />
        );

      case 'settings':
        return <SettingsPage className="h-full" />;
      
      case 'legacy':
        return <PlaceholderView title="经典界面" description="传统的文档管理界面" />;
      
      case 'backup':
        return <BackupManager onRestore={(data) => console.log('Restore backup:', data)} />;

      case 'guide':
        return <UserGuide />;

      case 'calendar':
        return (
          <ProjectCalendar
            dailyProgress={[
              {
                date: format(new Date(), 'yyyy-MM-dd'),
                projectId: 'root',
                projectName: 'dev-daily v2 项目',
                completedTasks: 3,
                totalTasks: 5,
                hoursWorked: 6,
                issues: ['响应式设计在移动端存在布局问题'],
                achievements: ['完成项目树ABCD模块化', '实现备份管理功能', '创建使用指南页面'],
                notes: '今日进度良好，明天继续完善项目日历功能'
              }
            ]}
            onDateSelect={(date) => console.log('Selected date:', date)}
            onAddProgress={(date) => console.log('Add progress for date:', date)}
          />
        );

      case 'roadmap':
        return projectTree ? (
          <ProjectRoadmap projectTree={projectTree} className="h-full" />
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="animate-spin h-8 w-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
              <p className="text-gray-600">正在加载路线图...</p>
            </div>
          </div>
        );

      case 'dev-sync':
        return <DevSyncDashboard />;

      case 'local-projects':
        return <LocalProjectManager />;

      case 'dev-environment':
        return <DevEnvironmentIntegration />;

      case 'sync-manager':
        return <MultiProjectSyncManager />;

      case 'workflow':
        return <WorkflowManager />;

      case 'github':
        return <GitHubIntegrationPage />;

      case 'performance':
        return <PerformanceMonitor />;

      case 'integrations':
        return <OnlinePlatformManager />;

      case 'document-sync':
        return <DocumentSyncManager className="h-full" />;

      default:
        return <PlaceholderView title="页面未找到" description="请选择一个有效的功能模块" />;
    }
  };

  return (
    <div className={clsx('flex h-screen bg-gray-100 w-full', className)}>
      <Sidebar
        activeView={activeView}
        onViewChange={setActiveView}
        isCollapsed={isSidebarCollapsed}
        onToggleCollapse={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
      />

      <main className="flex-1 overflow-hidden bg-white flex flex-col">
        {/* 项目选择器 */}
        <div className="border-b border-gray-200 p-4 bg-white">
          <div className="flex items-center justify-between">
            <ProjectSelector
              onProjectChange={async (project) => {
                if (project) {
                  console.log('切换到项目:', project.name);
                  await switchProject(project.id);
                }
              }}
            />
            <div className="flex items-center space-x-4">
              {currentProject && (
                <div className="text-sm text-gray-600">
                  当前项目: <span className="font-medium">{currentProject.name}</span>
                </div>
              )}
              <OfflineIndicator />
              <div className="text-sm text-gray-500">
                多项目管理系统 v2.0
              </div>
            </div>
          </div>
        </div>

        {/* 主内容区域 */}
        <div className="flex-1 overflow-y-auto">
          {renderMainContent()}
        </div>
      </main>
    </div>
  );
};
