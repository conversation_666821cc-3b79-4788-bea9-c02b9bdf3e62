import React, { useState } from 'react';
import { 
  LayoutDashboard, 
  GitBranch, 
  FileText, 
  Brain, 
  Settings,
  Menu,
  X
} from 'lucide-react';
import clsx from 'clsx';

type ViewType = 'dashboard' | 'project-tree' | 'documents' | 'ai-assistant' | 'settings';

// 简化的侧边栏组件
const SimpleSidebar: React.FC<{
  activeView: ViewType;
  onViewChange: (view: ViewType) => void;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
}> = ({ activeView, onViewChange, isCollapsed, onToggleCollapse }) => {
  const navigationItems = [
    { id: 'dashboard' as ViewType, name: '仪表板', icon: LayoutDashboard },
    { id: 'project-tree' as ViewType, name: '项目树', icon: GitBranch },
    { id: 'documents' as ViewType, name: '文档管理', icon: FileText },
    { id: 'ai-assistant' as ViewType, name: 'AI助手', icon: <PERSON> },
    { id: 'settings' as ViewType, name: '设置', icon: Settings },
  ];

  return (
    <div className={clsx(
      'bg-gray-900 text-white h-full flex flex-col',
      isCollapsed ? 'w-16' : 'w-64'
    )}>
      {/* 头部 */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <h1 className="text-xl font-bold">dev-daily v2</h1>
          )}
          <button
            onClick={onToggleCollapse}
            className="p-2 hover:bg-gray-700 rounded-md"
          >
            {isCollapsed ? <Menu size={20} /> : <X size={20} />}
          </button>
        </div>
      </div>

      {/* 导航 */}
      <nav className="flex-1 p-4">
        <div className="space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <button
                key={item.id}
                onClick={() => onViewChange(item.id)}
                className={clsx(
                  'w-full flex items-center gap-3 px-3 py-2 rounded-md transition-colors',
                  activeView === item.id 
                    ? 'bg-blue-600 text-white' 
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                )}
                title={isCollapsed ? item.name : undefined}
              >
                <Icon size={20} />
                {!isCollapsed && <span>{item.name}</span>}
              </button>
            );
          })}
        </div>
      </nav>

      {/* 底部信息 */}
      {!isCollapsed && (
        <div className="p-4 border-t border-gray-700">
          <div className="text-xs text-gray-400">
            <div>版本: v2.0.0-test</div>
            <div>状态: 测试中</div>
          </div>
        </div>
      )}
    </div>
  );
};

// 简化的内容区域
const SimpleContent: React.FC<{ activeView: ViewType }> = ({ activeView }) => {
  const getContent = () => {
    switch (activeView) {
      case 'dashboard':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">项目仪表板</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white p-6 rounded-lg shadow border">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">总文档数</h3>
                <p className="text-3xl font-bold text-blue-600">5</p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow border">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">项目节点</h3>
                <p className="text-3xl font-bold text-green-600">12</p>
              </div>
              <div className="bg-white p-6 rounded-lg shadow border">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">完成进度</h3>
                <p className="text-3xl font-bold text-purple-600">75%</p>
              </div>
            </div>
          </div>
        );
      
      case 'project-tree':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">项目树管理</h2>
            <div className="bg-white p-6 rounded-lg shadow border">
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <GitBranch className="h-5 w-5 text-blue-500" />
                  <span className="font-medium">dev-daily v2 项目</span>
                </div>
                <div className="ml-8 space-y-2">
                  <div className="flex items-center gap-3">
                    <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                    <span>核心功能模块</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                    <span>用户界面</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-4 h-4 bg-purple-500 rounded-full"></div>
                    <span>数据迁移</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      
      case 'documents':
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">文档管理</h2>
            <div className="bg-white rounded-lg shadow border">
              <div className="p-4 border-b">
                <input 
                  type="text" 
                  placeholder="搜索文档..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>
              <div className="p-4 space-y-3">
                {['项目概览', '迁移进度记录', '功能特性分析', '问题跟踪记录', '部署指南'].map((doc, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 hover:bg-gray-50 rounded">
                    <FileText className="h-5 w-5 text-gray-400" />
                    <span>{doc}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        );
      
      default:
        return (
          <div className="p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">{activeView}</h2>
            <div className="bg-white p-6 rounded-lg shadow border">
              <p className="text-gray-600">此功能正在开发中...</p>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="flex-1 bg-gray-100 overflow-auto">
      {getContent()}
    </div>
  );
};

// 简化的主仪表板
export const SimpleDashboard: React.FC = () => {
  const [activeView, setActiveView] = useState<ViewType>('dashboard');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  return (
    <div className="flex h-screen bg-gray-100">
      <SimpleSidebar
        activeView={activeView}
        onViewChange={setActiveView}
        isCollapsed={isSidebarCollapsed}
        onToggleCollapse={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
      />
      <SimpleContent activeView={activeView} />
    </div>
  );
};
