import React, { useState, useEffect } from 'react';
import {
  Brain,
  Target,
  Lightbulb,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Users,
  Globe,
  Clock,
  MapPin,
  HelpCircle,
  Zap,
  Send,
  Download,
  RefreshCw,
  Eye,
  BarChart3
} from 'lucide-react';
import { Document, ProjectTreeNode } from '../../types/index';
import { OpenAIService } from '../../services/OpenAIService';
import clsx from 'clsx';

interface ThinkingModelsPageProps {
  documents: Document[];
  projectTree: ProjectTreeNode | null;
  className?: string;
}

// 思维模型类型
type ThinkingModel = 'six-hats' | 'swot' | '5w1h' | 'pest' | 'decision-matrix' | 'root-cause';

// 分析会话
interface AnalysisSession {
  id: string;
  topic: string;
  createdAt: Date;
  models: {
    [key in ThinkingModel]?: any;
  };
  status: 'active' | 'completed';
}

// 思维模型配置
const thinkingModels = [
  {
    id: 'six-hats' as ThinkingModel,
    name: '六顶思考帽',
    description: '多角度思考分析',
    icon: Brain,
    color: 'bg-purple-100 text-purple-800',
    hats: [
      { color: 'white', name: '白帽', role: '事实信息', icon: '🤍', description: '客观事实和数据' },
      { color: 'red', name: '红帽', role: '情感直觉', icon: '❤️', description: '情感和直觉反应' },
      { color: 'black', name: '黑帽', role: '批判思考', icon: '🖤', description: '风险和问题识别' },
      { color: 'yellow', name: '黄帽', role: '积极思考', icon: '💛', description: '积极面和机会' },
      { color: 'green', name: '绿帽', role: '创新思考', icon: '💚', description: '创意和替代方案' },
      { color: 'blue', name: '蓝帽', role: '流程控制', icon: '💙', description: '思考过程管理' }
    ]
  },
  {
    id: 'swot' as ThinkingModel,
    name: 'SWOT分析',
    description: '优势劣势机会威胁',
    icon: Target,
    color: 'bg-blue-100 text-blue-800',
    quadrants: [
      { key: 'strengths', name: '优势 (Strengths)', icon: '💪', color: 'bg-green-50 border-green-200' },
      { key: 'weaknesses', name: '劣势 (Weaknesses)', icon: '⚠️', color: 'bg-red-50 border-red-200' },
      { key: 'opportunities', name: '机会 (Opportunities)', icon: '🚀', color: 'bg-blue-50 border-blue-200' },
      { key: 'threats', name: '威胁 (Threats)', icon: '⚡', color: 'bg-yellow-50 border-yellow-200' }
    ]
  },
  {
    id: '5w1h' as ThinkingModel,
    name: '5W1H分析',
    description: '全面问题分析框架',
    icon: HelpCircle,
    color: 'bg-green-100 text-green-800',
    questions: [
      { key: 'what', question: 'What - 什么', icon: '❓', description: '问题或目标是什么' },
      { key: 'why', question: 'Why - 为什么', icon: '🤔', description: '为什么要做这件事' },
      { key: 'when', question: 'When - 何时', icon: '⏰', description: '什么时候执行' },
      { key: 'where', question: 'Where - 何地', icon: '📍', description: '在哪里执行' },
      { key: 'who', question: 'Who - 谁', icon: '👤', description: '谁来负责执行' },
      { key: 'how', question: 'How - 如何', icon: '⚙️', description: '如何具体实施' }
    ]
  },
  {
    id: 'pest' as ThinkingModel,
    name: 'PEST分析',
    description: '外部环境分析',
    icon: Globe,
    color: 'bg-orange-100 text-orange-800',
    factors: [
      { key: 'political', name: '政治 (Political)', icon: '🏛️', description: '政策法规影响' },
      { key: 'economic', name: '经济 (Economic)', icon: '💰', description: '经济环境因素' },
      { key: 'social', name: '社会 (Social)', icon: '👥', description: '社会文化趋势' },
      { key: 'technological', name: '技术 (Technological)', icon: '🔬', description: '技术发展趋势' }
    ]
  },
  {
    id: 'decision-matrix' as ThinkingModel,
    name: '决策矩阵',
    description: '多标准决策分析',
    icon: BarChart3,
    color: 'bg-indigo-100 text-indigo-800'
  },
  {
    id: 'root-cause' as ThinkingModel,
    name: '根因分析',
    description: '问题根本原因探索',
    icon: Zap,
    color: 'bg-red-100 text-red-800'
  }
];

export const ThinkingModelsPage: React.FC<ThinkingModelsPageProps> = ({
  documents,
  projectTree,
  className
}) => {
  const [activeModel, setActiveModel] = useState<ThinkingModel>('six-hats');
  const [currentSession, setCurrentSession] = useState<AnalysisSession | null>(null);
  const [topic, setTopic] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [openAIService, setOpenAIService] = useState<OpenAIService | null>(null);

  // 初始化AI服务
  useEffect(() => {
    const initializeAI = () => {
      try {
        const savedSettings = localStorage.getItem('dev-daily-settings');
        const savedApiKey = localStorage.getItem('dev-daily-api-key');

        console.log('ThinkingModels - Initializing AI service...');
        console.log('Saved settings:', savedSettings);
        console.log('Saved API key exists:', !!savedApiKey);

        // 使用保存的设置或默认设置
        let settings;
        if (savedSettings) {
          settings = JSON.parse(savedSettings);
        } else {
          // 使用默认设置
          settings = {
            ai: {
              enabled: true,
              provider: 'openai',
              model: 'gpt-3.5-turbo',
              temperature: 0.7,
              maxTokens: 2000,
              autoSuggestions: true
            }
          };
        }

        // 使用保存的API密钥
        const apiKey = savedApiKey;

        console.log('Using settings:', settings);
        console.log('AI enabled:', settings.ai?.enabled);
        console.log('AI provider:', settings.ai?.provider);
        console.log('API key available:', !!apiKey);

        if (settings.ai?.enabled && settings.ai?.provider === 'openai' && apiKey) {
          const service = new OpenAIService({
            apiKey: apiKey,
            model: settings.ai?.model || 'gpt-3.5-turbo',
            temperature: settings.ai?.temperature || 0.7,
            maxTokens: settings.ai?.maxTokens || 2000
          });
          setOpenAIService(service);
          console.log('AI service initialized successfully');

          // 测试API连接
          service.testConnection().then(result => {
            console.log('API connection test result:', result);
          }).catch(error => {
            console.error('API connection test failed:', error);
          });
        } else {
          console.log('AI not enabled, provider not OpenAI, or API key missing');
        }
      } catch (error) {
        console.error('Failed to initialize AI service:', error);
      }
    };

    initializeAI();

    // 监听设置变更
    const handleSettingsChange = (event: CustomEvent) => {
      console.log('Settings changed, reinitializing AI service');
      initializeAI();
    };

    window.addEventListener('settings-saved', handleSettingsChange as EventListener);
    return () => {
      window.removeEventListener('settings-saved', handleSettingsChange as EventListener);
    };
  }, []);

  // 开始新的分析会话
  const startAnalysis = async () => {
    if (!topic.trim()) {
      alert('请输入要分析的主题或问题');
      return;
    }

    if (!openAIService) {
      alert('请先在设置中配置OpenAI API密钥');
      return;
    }

    setIsAnalyzing(true);
    
    const session: AnalysisSession = {
      id: `session-${Date.now()}`,
      topic: topic.trim(),
      createdAt: new Date(),
      models: {},
      status: 'active'
    };

    setCurrentSession(session);

    try {
      // 并行执行所有思维模型分析
      const analysisPromises = thinkingModels.map(model => 
        analyzeWithModel(model.id, topic, openAIService)
      );

      const results = await Promise.allSettled(analysisPromises);
      
      // 收集成功的分析结果
      const modelResults: { [key in ThinkingModel]?: any } = {};
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          modelResults[thinkingModels[index].id] = result.value;
        }
      });

      const completedSession = {
        ...session,
        models: modelResults,
        status: 'completed' as const
      };

      setCurrentSession(completedSession);
    } catch (error) {
      console.error('Analysis failed:', error);
      alert('分析过程中出现错误，请重试');
    } finally {
      setIsAnalyzing(false);
    }
  };

  // 使用特定模型进行分析
  const analyzeWithModel = async (modelId: ThinkingModel, topic: string, aiService: OpenAIService) => {
    const prompts = {
      'six-hats': `请使用六顶思考帽方法分析以下主题："${topic}"

请按以下JSON格式返回分析结果：
{
  "white": "客观事实和数据分析",
  "red": "情感和直觉反应",
  "black": "风险和问题识别", 
  "yellow": "积极面和机会",
  "green": "创意和替代方案",
  "blue": "思考过程总结"
}`,

      'swot': `请对以下主题进行SWOT分析："${topic}"

请按以下JSON格式返回：
{
  "strengths": ["优势1", "优势2", "优势3"],
  "weaknesses": ["劣势1", "劣势2", "劣势3"],
  "opportunities": ["机会1", "机会2", "机会3"],
  "threats": ["威胁1", "威胁2", "威胁3"]
}`,

      '5w1h': `请使用5W1H方法分析以下主题："${topic}"

请按以下JSON格式返回：
{
  "what": "具体是什么问题或目标",
  "why": "为什么要解决这个问题",
  "when": "时间安排和节点",
  "where": "执行地点或范围",
  "who": "相关人员和责任分工",
  "how": "具体实施方法和步骤"
}`,

      'pest': `请对以下主题进行PEST环境分析："${topic}"

请按以下JSON格式返回：
{
  "political": ["政治因素1", "政治因素2"],
  "economic": ["经济因素1", "经济因素2"], 
  "social": ["社会因素1", "社会因素2"],
  "technological": ["技术因素1", "技术因素2"]
}`,

      'decision-matrix': `请为以下决策主题创建决策矩阵："${topic}"

请按以下JSON格式返回：
{
  "criteria": ["标准1", "标准2", "标准3"],
  "options": ["选项1", "选项2", "选项3"],
  "analysis": "决策建议和理由"
}`,

      'root-cause': `请对以下问题进行根因分析："${topic}"

请按以下JSON格式返回：
{
  "problem": "问题描述",
  "symptoms": ["症状1", "症状2"],
  "causes": ["直接原因1", "直接原因2"],
  "root_causes": ["根本原因1", "根本原因2"],
  "solutions": ["解决方案1", "解决方案2"]
}`
    };

    const response = await aiService.chat([{
      id: 'analysis-request',
      type: 'user',
      content: prompts[modelId],
      timestamp: new Date()
    }]);

    try {
      return JSON.parse(response);
    } catch (error) {
      // 如果JSON解析失败，返回原始文本
      return { raw: response };
    }
  };

  // 导出分析结果
  const exportAnalysis = () => {
    if (!currentSession) return;

    const exportData = {
      topic: currentSession.topic,
      createdAt: currentSession.createdAt,
      models: currentSession.models,
      exportedAt: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `thinking-analysis-${currentSession.topic.replace(/\s+/g, '-')}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className={clsx('flex h-full bg-white', className)}>
      {/* 左侧思维模型列表 */}
      <div className="w-80 bg-gray-50 border-r border-gray-200 p-4">
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Brain className="h-5 w-5" />
            思维模型
          </h2>
          <p className="text-sm text-gray-600 mt-1">多维度分析和决策支持</p>
        </div>

        {/* 主题输入 */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            分析主题
          </label>
          <textarea
            value={topic}
            onChange={(e) => setTopic(e.target.value)}
            placeholder="输入要分析的问题、决策或主题..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
            rows={3}
          />
          <button
            onClick={startAnalysis}
            disabled={isAnalyzing || !topic.trim()}
            className={clsx(
              'w-full mt-3 flex items-center justify-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors',
              isAnalyzing || !topic.trim()
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            )}
          >
            {isAnalyzing ? (
              <>
                <RefreshCw className="h-4 w-4 animate-spin" />
                分析中...
              </>
            ) : (
              <>
                <Zap className="h-4 w-4" />
                开始分析
              </>
            )}
          </button>
        </div>

        {/* 思维模型列表 */}
        <div className="space-y-2">
          {thinkingModels.map((model) => {
            const Icon = model.icon;
            const hasResult = currentSession?.models[model.id];
            
            return (
              <button
                key={model.id}
                onClick={() => setActiveModel(model.id)}
                className={clsx(
                  'w-full text-left p-3 rounded-lg transition-colors relative',
                  activeModel === model.id
                    ? 'bg-blue-100 text-blue-900 border border-blue-200'
                    : 'hover:bg-gray-100 text-gray-700'
                )}
              >
                <div className="flex items-center gap-3">
                  <Icon className="h-5 w-5" />
                  <div className="flex-1">
                    <div className="font-medium">{model.name}</div>
                    <div className="text-xs text-gray-500">{model.description}</div>
                  </div>
                  {hasResult && (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  )}
                </div>
              </button>
            );
          })}
        </div>

        {/* 导出按钮 */}
        {currentSession && (
          <button
            onClick={exportAnalysis}
            className="w-full mt-6 flex items-center justify-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
          >
            <Download className="h-4 w-4" />
            导出分析结果
          </button>
        )}
      </div>

      {/* 右侧分析结果展示 */}
      <div className="flex-1 p-6 overflow-auto">
        {currentSession ? (
          <ModelResultDisplay
            model={activeModel}
            session={currentSession}
            modelConfig={thinkingModels.find(m => m.id === activeModel)!}
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <Brain className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-medium text-gray-900 mb-2">开始思维分析</h3>
              <p className="text-gray-600 mb-4">输入您要分析的主题，AI将使用多种思维模型为您提供全面的分析和建议</p>
              <div className="text-sm text-gray-500">
                支持六顶思考帽、SWOT、5W1H、PEST等多种分析方法
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// 模型结果展示组件
const ModelResultDisplay: React.FC<{
  model: ThinkingModel;
  session: AnalysisSession;
  modelConfig: any;
}> = ({ model, session, modelConfig }) => {
  const result = session.models[model];

  if (!result) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无分析结果</h3>
          <p className="text-gray-600">请先开始分析以查看{modelConfig.name}的结果</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 标题 */}
      <div className="border-b border-gray-200 pb-4">
        <div className="flex items-center gap-3 mb-2">
          <modelConfig.icon className="h-6 w-6 text-blue-600" />
          <h2 className="text-2xl font-bold text-gray-900">{modelConfig.name}</h2>
        </div>
        <p className="text-gray-600">主题: {session.topic}</p>
        <p className="text-sm text-gray-500">分析时间: {session.createdAt.toLocaleString()}</p>
      </div>

      {/* 根据模型类型渲染不同的结果 */}
      {model === 'six-hats' && <SixHatsResult result={result} />}
      {model === 'swot' && <SwotResult result={result} />}
      {model === '5w1h' && <FiveW1HResult result={result} />}
      {model === 'pest' && <PestResult result={result} />}
      {model === 'decision-matrix' && <DecisionMatrixResult result={result} />}
      {model === 'root-cause' && <RootCauseResult result={result} />}
    </div>
  );
};

// 六顶思考帽结果
const SixHatsResult: React.FC<{ result: any }> = ({ result }) => {
  const hats = [
    { key: 'white', name: '白帽', emoji: '🤍', color: 'border-gray-300 bg-gray-50', description: '事实信息' },
    { key: 'red', name: '红帽', emoji: '❤️', color: 'border-red-300 bg-red-50', description: '情感直觉' },
    { key: 'black', name: '黑帽', emoji: '🖤', color: 'border-gray-800 bg-gray-100', description: '批判思考' },
    { key: 'yellow', name: '黄帽', emoji: '💛', color: 'border-yellow-300 bg-yellow-50', description: '积极思考' },
    { key: 'green', name: '绿帽', emoji: '💚', color: 'border-green-300 bg-green-50', description: '创新思考' },
    { key: 'blue', name: '蓝帽', emoji: '💙', color: 'border-blue-300 bg-blue-50', description: '流程控制' }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {hats.map((hat) => (
        <div key={hat.key} className={clsx('p-4 rounded-lg border-2', hat.color)}>
          <div className="flex items-center gap-2 mb-3">
            <span className="text-2xl">{hat.emoji}</span>
            <div>
              <h3 className="font-semibold text-gray-900">{hat.name}</h3>
              <p className="text-sm text-gray-600">{hat.description}</p>
            </div>
          </div>
          <p className="text-gray-800">{result[hat.key] || '暂无分析结果'}</p>
        </div>
      ))}
    </div>
  );
};

// SWOT分析结果
const SwotResult: React.FC<{ result: any }> = ({ result }) => {
  const quadrants = [
    { key: 'strengths', name: '优势', emoji: '💪', color: 'border-green-300 bg-green-50' },
    { key: 'weaknesses', name: '劣势', emoji: '⚠️', color: 'border-red-300 bg-red-50' },
    { key: 'opportunities', name: '机会', emoji: '🚀', color: 'border-blue-300 bg-blue-50' },
    { key: 'threats', name: '威胁', emoji: '⚡', color: 'border-yellow-300 bg-yellow-50' }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {quadrants.map((quad) => (
        <div key={quad.key} className={clsx('p-4 rounded-lg border-2', quad.color)}>
          <div className="flex items-center gap-2 mb-3">
            <span className="text-2xl">{quad.emoji}</span>
            <h3 className="font-semibold text-gray-900">{quad.name}</h3>
          </div>
          <ul className="space-y-2">
            {(result[quad.key] || []).map((item: string, index: number) => (
              <li key={index} className="flex items-start gap-2">
                <span className="text-gray-400 mt-1">•</span>
                <span className="text-gray-800">{item}</span>
              </li>
            ))}
          </ul>
        </div>
      ))}
    </div>
  );
};

// 5W1H分析结果
const FiveW1HResult: React.FC<{ result: any }> = ({ result }) => {
  const questions = [
    { key: 'what', name: 'What - 什么', emoji: '❓', color: 'border-purple-300 bg-purple-50' },
    { key: 'why', name: 'Why - 为什么', emoji: '🤔', color: 'border-blue-300 bg-blue-50' },
    { key: 'when', name: 'When - 何时', emoji: '⏰', color: 'border-green-300 bg-green-50' },
    { key: 'where', name: 'Where - 何地', emoji: '📍', color: 'border-red-300 bg-red-50' },
    { key: 'who', name: 'Who - 谁', emoji: '👤', color: 'border-yellow-300 bg-yellow-50' },
    { key: 'how', name: 'How - 如何', emoji: '⚙️', color: 'border-gray-300 bg-gray-50' }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {questions.map((q) => (
        <div key={q.key} className={clsx('p-4 rounded-lg border-2', q.color)}>
          <div className="flex items-center gap-2 mb-3">
            <span className="text-2xl">{q.emoji}</span>
            <h3 className="font-semibold text-gray-900">{q.name}</h3>
          </div>
          <p className="text-gray-800">{result[q.key] || '暂无分析结果'}</p>
        </div>
      ))}
    </div>
  );
};

// PEST分析结果
const PestResult: React.FC<{ result: any }> = ({ result }) => {
  const factors = [
    { key: 'political', name: '政治因素', emoji: '🏛️', color: 'border-red-300 bg-red-50' },
    { key: 'economic', name: '经济因素', emoji: '💰', color: 'border-green-300 bg-green-50' },
    { key: 'social', name: '社会因素', emoji: '👥', color: 'border-blue-300 bg-blue-50' },
    { key: 'technological', name: '技术因素', emoji: '🔬', color: 'border-purple-300 bg-purple-50' }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {factors.map((factor) => (
        <div key={factor.key} className={clsx('p-4 rounded-lg border-2', factor.color)}>
          <div className="flex items-center gap-2 mb-3">
            <span className="text-2xl">{factor.emoji}</span>
            <h3 className="font-semibold text-gray-900">{factor.name}</h3>
          </div>
          <ul className="space-y-2">
            {(result[factor.key] || []).map((item: string, index: number) => (
              <li key={index} className="flex items-start gap-2">
                <span className="text-gray-400 mt-1">•</span>
                <span className="text-gray-800">{item}</span>
              </li>
            ))}
          </ul>
        </div>
      ))}
    </div>
  );
};

// 决策矩阵结果
const DecisionMatrixResult: React.FC<{ result: any }> = ({ result }) => {
  return (
    <div className="space-y-6">
      <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
        <h3 className="font-semibold text-blue-900 mb-2">决策分析</h3>
        <p className="text-blue-800">{result.analysis || '暂无分析结果'}</p>
      </div>

      {result.criteria && (
        <div>
          <h4 className="font-medium text-gray-900 mb-2">评估标准</h4>
          <div className="flex flex-wrap gap-2">
            {result.criteria.map((criterion: string, index: number) => (
              <span key={index} className="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm">
                {criterion}
              </span>
            ))}
          </div>
        </div>
      )}

      {result.options && (
        <div>
          <h4 className="font-medium text-gray-900 mb-2">可选方案</h4>
          <div className="space-y-2">
            {result.options.map((option: string, index: number) => (
              <div key={index} className="p-3 bg-gray-50 rounded-lg">
                <span className="font-medium text-gray-900">方案 {index + 1}: </span>
                <span className="text-gray-800">{option}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// 根因分析结果
const RootCauseResult: React.FC<{ result: any }> = ({ result }) => {
  return (
    <div className="space-y-6">
      {result.problem && (
        <div className="bg-red-50 p-4 rounded-lg border border-red-200">
          <h3 className="font-semibold text-red-900 mb-2">问题描述</h3>
          <p className="text-red-800">{result.problem}</p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {result.symptoms && (
          <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
            <h4 className="font-medium text-yellow-900 mb-2">表面症状</h4>
            <ul className="space-y-1">
              {result.symptoms.map((symptom: string, index: number) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-yellow-600 mt-1">•</span>
                  <span className="text-yellow-800">{symptom}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {result.causes && (
          <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
            <h4 className="font-medium text-orange-900 mb-2">直接原因</h4>
            <ul className="space-y-1">
              {result.causes.map((cause: string, index: number) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-orange-600 mt-1">•</span>
                  <span className="text-orange-800">{cause}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {result.root_causes && (
          <div className="bg-red-50 p-4 rounded-lg border border-red-200">
            <h4 className="font-medium text-red-900 mb-2">根本原因</h4>
            <ul className="space-y-1">
              {result.root_causes.map((cause: string, index: number) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-red-600 mt-1">•</span>
                  <span className="text-red-800">{cause}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {result.solutions && (
          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <h4 className="font-medium text-green-900 mb-2">解决方案</h4>
            <ul className="space-y-1">
              {result.solutions.map((solution: string, index: number) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-green-600 mt-1">•</span>
                  <span className="text-green-800">{solution}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};
