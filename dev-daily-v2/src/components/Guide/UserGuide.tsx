import React, { useState } from 'react';
import { 
  BookOpen, 
  Target, 
  Zap, 
  TreePine, 
  Brain, 
  FileText, 
  Calendar,
  Settings,
  ChevronRight,
  ChevronDown,
  Lightbulb,
  Rocket,
  Shield
} from 'lucide-react';

interface GuideSection {
  id: string;
  title: string;
  icon: React.ReactNode;
  content: React.ReactNode;
  expanded?: boolean;
}

export const UserGuide: React.FC = () => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(['overview', 'getting-started'])
  );

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const guideSections: GuideSection[] = [
    {
      id: 'overview',
      title: '项目概览',
      icon: <Target className="w-5 h-5" />,
      content: (
        <div className="space-y-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-900 mb-2">🎯 项目目标</h4>
            <ul className="text-blue-800 space-y-1 text-sm">
              <li>• 提升开发效率 30%+</li>
              <li>• 改善项目管理体验</li>
              <li>• 集成AI辅助功能</li>
              <li>• 保持向后兼容性</li>
            </ul>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-semibold text-green-900 mb-2">✨ 核心价值</h4>
            <p className="text-green-800 text-sm">
              dev-daily v2 是基于Project Tree架构的智能开发日志管理系统，
              通过可视化项目管理、AI智能助手和思维模型分析，
              帮助开发者更高效地管理项目进度和决策过程。
            </p>
          </div>
        </div>
      )
    },
    {
      id: 'getting-started',
      title: '快速开始',
      icon: <Rocket className="w-5 h-5" />,
      content: (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-purple-50 p-4 rounded-lg">
              <h4 className="font-semibold text-purple-900 mb-2">1. 创建项目</h4>
              <p className="text-purple-800 text-sm">
                在项目树页面点击"新建项目"，设置项目名称、描述和目标。
              </p>
            </div>
            <div className="bg-orange-50 p-4 rounded-lg">
              <h4 className="font-semibold text-orange-900 mb-2">2. 规划结构</h4>
              <p className="text-orange-800 text-sm">
                使用ABCD模块化结构，创建清晰的项目层次和任务节点。
              </p>
            </div>
            <div className="bg-teal-50 p-4 rounded-lg">
              <h4 className="font-semibold text-teal-900 mb-2">3. 记录进度</h4>
              <p className="text-teal-800 text-sm">
                每日更新任务状态，记录开发日志和遇到的问题。
              </p>
            </div>
            <div className="bg-pink-50 p-4 rounded-lg">
              <h4 className="font-semibold text-pink-900 mb-2">4. AI辅助</h4>
              <p className="text-pink-800 text-sm">
                使用思维模型和AI助手进行决策分析和问题解决。
              </p>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'features',
      title: '核心功能',
      icon: <Zap className="w-5 h-5" />,
      content: (
        <div className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
              <TreePine className="w-5 h-5 text-green-600 mt-0.5" />
              <div>
                <h4 className="font-semibold text-gray-900">Project Tree 管理</h4>
                <p className="text-gray-600 text-sm">
                  可视化的项目结构管理，支持拖拽重组、节点CRUD操作和进度追踪。
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
              <Brain className="w-5 h-5 text-purple-600 mt-0.5" />
              <div>
                <h4 className="font-semibold text-gray-900">思维模型系统</h4>
                <p className="text-gray-600 text-sm">
                  集成六顶思考帽、SWOT分析等多维度分析框架，提供AI辅助决策支持。
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
              <FileText className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-semibold text-gray-900">文档管理</h4>
                <p className="text-gray-600 text-sm">
                  结构化的文档组织和搜索功能，支持Markdown格式和智能分类。
                </p>
              </div>
            </div>
            <div className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
              <Calendar className="w-5 h-5 text-orange-600 mt-0.5" />
              <div>
                <h4 className="font-semibold text-gray-900">项目日历</h4>
                <p className="text-gray-600 text-sm">
                  日历形式的进度追踪，每日项目更新记录，便于回顾和问题分析。
                </p>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'backup-strategy',
      title: '备份策略',
      icon: <Shield className="w-5 h-5" />,
      content: (
        <div className="space-y-4">
          <div className="bg-yellow-50 p-4 rounded-lg">
            <h4 className="font-semibold text-yellow-900 mb-2">🔄 自动备份机制</h4>
            <p className="text-yellow-800 text-sm mb-3">
              系统在每个功能节点完成后自动提示并执行备份，确保项目进度可以最大程度还原。
            </p>
            <ul className="text-yellow-800 space-y-1 text-sm">
              <li>• 节点完成时自动触发备份提示</li>
              <li>• 本地存储完整项目状态</li>
              <li>• 支持增量备份和完整备份</li>
              <li>• 备份历史版本管理</li>
            </ul>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-semibold text-green-900 mb-2">📦 备份内容</h4>
            <ul className="text-green-800 space-y-1 text-sm">
              <li>• 项目树结构和节点状态</li>
              <li>• 文档内容和元数据</li>
              <li>• 用户设置和配置</li>
              <li>• 进度记录和日志</li>
            </ul>
          </div>
        </div>
      )
    },
    {
      id: 'project-calendar',
      title: '项目日历',
      icon: <Calendar className="w-5 h-5" />,
      content: (
        <div className="space-y-4">
          <div className="bg-indigo-50 p-4 rounded-lg">
            <h4 className="font-semibold text-indigo-900 mb-2">📅 日历功能</h4>
            <p className="text-indigo-800 text-sm mb-3">
              以日历形式展示每天每个项目的进度更新，方便回顾进度和分析问题因素。
            </p>
            <ul className="text-indigo-800 space-y-1 text-sm">
              <li>• 每日进度可视化展示</li>
              <li>• 项目里程碑标记</li>
              <li>• 问题和解决方案记录</li>
              <li>• 工时统计和分析</li>
            </ul>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-900 mb-2">📊 分析功能</h4>
            <ul className="text-blue-800 space-y-1 text-sm">
              <li>• 进度趋势分析</li>
              <li>• 效率统计报告</li>
              <li>• 问题模式识别</li>
              <li>• 时间分配优化建议</li>
            </ul>
          </div>
        </div>
      )
    },
    {
      id: 'best-practices',
      title: '最佳实践',
      icon: <Lightbulb className="w-5 h-5" />,
      content: (
        <div className="space-y-4">
          <div className="space-y-3">
            <div className="bg-emerald-50 p-4 rounded-lg">
              <h4 className="font-semibold text-emerald-900 mb-2">🌳 项目树管理</h4>
              <ul className="text-emerald-800 space-y-1 text-sm">
                <li>• 使用ABCD模块化结构组织项目</li>
                <li>• 保持节点粒度适中（20分钟工作单元）</li>
                <li>• 及时更新节点状态和进度</li>
                <li>• 定期整理和优化项目结构</li>
              </ul>
            </div>
            <div className="bg-violet-50 p-4 rounded-lg">
              <h4 className="font-semibold text-violet-900 mb-2">🧠 思维模型使用</h4>
              <ul className="text-violet-800 space-y-1 text-sm">
                <li>• 重要决策前使用多维度分析</li>
                <li>• 结合AI建议进行综合判断</li>
                <li>• 记录分析过程和结论</li>
                <li>• 定期回顾决策效果</li>
              </ul>
            </div>
          </div>
        </div>
      )
    }
  ];

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-8">
        <div className="flex items-center space-x-3 mb-4">
          <BookOpen className="w-8 h-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">dev-daily v2 使用指南</h1>
        </div>
        <p className="text-gray-600 text-lg">
          欢迎使用 dev-daily v2！这是一个基于 Project Tree 架构的智能开发日志管理系统。
        </p>
      </div>

      <div className="space-y-4">
        {guideSections.map((section) => (
          <div key={section.id} className="bg-white border border-gray-200 rounded-lg shadow-sm">
            <button
              onClick={() => toggleSection(section.id)}
              className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                {section.icon}
                <h2 className="text-lg font-semibold text-gray-900">{section.title}</h2>
              </div>
              {expandedSections.has(section.id) ? (
                <ChevronDown className="w-5 h-5 text-gray-500" />
              ) : (
                <ChevronRight className="w-5 h-5 text-gray-500" />
              )}
            </button>
            {expandedSections.has(section.id) && (
              <div className="px-4 pb-4">
                {section.content}
              </div>
            )}
          </div>
        ))}
      </div>

      <div className="mt-8 bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg">
        <div className="flex items-start space-x-3">
          <Settings className="w-6 h-6 text-blue-600 mt-1" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">需要帮助？</h3>
            <p className="text-gray-600 mb-3">
              如果您在使用过程中遇到问题，可以：
            </p>
            <ul className="text-gray-600 space-y-1 text-sm">
              <li>• 查看相关文档和最佳实践</li>
              <li>• 使用AI助手获取智能建议</li>
              <li>• 在设置页面调整系统配置</li>
              <li>• 联系技术支持团队</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserGuide;
