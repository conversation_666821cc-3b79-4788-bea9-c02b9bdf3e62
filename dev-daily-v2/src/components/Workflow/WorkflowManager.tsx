/**
 * 工作流管理组件 - 配置和监控自动化工作流
 */

import React, { useState, useEffect } from 'react';
import { 
  Settings, 
  Play, 
  Pause, 
  RefreshCw, 
  GitBranch, 
  FileText, 
  Bell,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { 
  WorkflowAutomationService, 
  WorkflowConfig, 
  WorkflowEvent,
  WorkflowEventType 
} from '../../services/WorkflowAutomationService';
import { useProjectContext } from '../../hooks/useProjectContext';

export const WorkflowManager: React.FC = () => {
  const { currentProject } = useProjectContext();
  const [config, setConfig] = useState<WorkflowConfig | null>(null);
  const [events, setEvents] = useState<WorkflowEvent[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'config' | 'events' | 'status'>('config');

  useEffect(() => {
    if (currentProject) {
      loadWorkflowData();
      setupEventListeners();
    }
  }, [currentProject]);

  const loadWorkflowData = () => {
    if (!currentProject) return;

    // 加载配置
    const projectConfig = WorkflowAutomationService.getConfig?.(currentProject.id);
    setConfig(projectConfig || {
      projectId: currentProject.id,
      autoSync: false,
      syncInterval: 30,
      conflictResolution: 'manual',
      notifications: true
    });

    // 加载事件历史
    const projectEvents = WorkflowAutomationService.getWorkflowEvents?.(currentProject.id, 50) || [];
    setEvents(projectEvents);
  };

  const setupEventListeners = () => {
    if (!currentProject) return;

    const eventTypes: WorkflowEventType[] = [
      'local_file_changed',
      'github_push_received',
      'requirement_generated',
      'sync_requested',
      'conflict_detected'
    ];

    eventTypes.forEach(eventType => {
      WorkflowAutomationService.addEventListener?.(eventType, (event) => {
        if (event.projectId === currentProject.id) {
          setEvents(prev => [event, ...prev.slice(0, 49)]);
        }
      });
    });
  };

  const handleConfigChange = (updates: Partial<WorkflowConfig>) => {
    if (!currentProject || !config) return;

    const newConfig = { ...config, ...updates };
    setConfig(newConfig);
    WorkflowAutomationService.configureWorkflow?.(newConfig);
  };

  const handleManualSync = async () => {
    if (!currentProject) return;

    setIsLoading(true);
    try {
      // 触发手动同步
      await WorkflowAutomationService.handleLocalFileChange?.(currentProject.id, []);
    } catch (error) {
      console.error('手动同步失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getEventIcon = (eventType: WorkflowEventType) => {
    const iconMap = {
      'local_file_changed': FileText,
      'github_push_received': GitBranch,
      'requirement_generated': FileText,
      'sync_requested': RefreshCw,
      'conflict_detected': AlertCircle
    };
    return iconMap[eventType] || FileText;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed': return <XCircle className="w-4 h-4 text-red-500" />;
      case 'processing': return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
      default: return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  if (!currentProject) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">请先选择一个项目</p>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">工作流自动化</h1>
        <p className="text-gray-600">
          配置和监控项目的自动化工作流程
        </p>
      </div>

      {/* 标签页导航 */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'config', name: '配置', icon: Settings },
            { id: 'events', name: '事件历史', icon: Clock },
            { id: 'status', name: '状态监控', icon: RefreshCw }
          ].map(tab => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* 配置页面 */}
      {activeTab === 'config' && config && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold mb-4">自动化配置</h2>
            
            <div className="space-y-4">
              {/* 自动同步开关 */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">自动同步</label>
                  <p className="text-sm text-gray-500">自动检测文件变化并同步到 GitHub</p>
                </div>
                <button
                  onClick={() => handleConfigChange({ autoSync: !config.autoSync })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    config.autoSync ? 'bg-blue-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      config.autoSync ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              {/* 同步间隔 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  同步间隔 (分钟)
                </label>
                <input
                  type="number"
                  min="5"
                  max="1440"
                  value={config.syncInterval}
                  onChange={(e) => handleConfigChange({ syncInterval: parseInt(e.target.value) })}
                  className="w-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* 冲突解决策略 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  冲突解决策略
                </label>
                <select
                  value={config.conflictResolution}
                  onChange={(e) => handleConfigChange({ conflictResolution: e.target.value as any })}
                  className="w-48 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="local">本地优先</option>
                  <option value="remote">远程优先</option>
                  <option value="manual">手动解决</option>
                </select>
              </div>

              {/* 通知设置 */}
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700">桌面通知</label>
                  <p className="text-sm text-gray-500">同步完成时显示桌面通知</p>
                </div>
                <button
                  onClick={() => handleConfigChange({ notifications: !config.notifications })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    config.notifications ? 'bg-blue-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      config.notifications ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>

            {/* 手动操作 */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h3 className="text-md font-medium mb-4">手动操作</h3>
              <div className="flex space-x-4">
                <button
                  onClick={handleManualSync}
                  disabled={isLoading}
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
                  <span>立即同步</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 事件历史页面 */}
      {activeTab === 'events' && (
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold">事件历史</h2>
            <p className="text-sm text-gray-500 mt-1">最近的工作流事件和执行结果</p>
          </div>
          
          <div className="divide-y divide-gray-200">
            {events.length === 0 ? (
              <div className="p-6 text-center text-gray-500">
                暂无事件记录
              </div>
            ) : (
              events.map((event) => {
                const Icon = getEventIcon(event.type);
                return (
                  <div key={event.id} className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0">
                        <Icon className="w-5 h-5 text-gray-400" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-gray-900">
                            {event.type.replace(/_/g, ' ')}
                          </p>
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(event.status)}
                            <span className="text-xs text-gray-500">
                              {new Date(event.timestamp).toLocaleString()}
                            </span>
                          </div>
                        </div>
                        
                        {event.result && (
                          <div className="mt-2">
                            <p className="text-sm text-gray-600">{event.result.message}</p>
                            {event.result.actions.length > 0 && (
                              <div className="mt-2">
                                <p className="text-xs text-gray-500">执行的操作:</p>
                                <ul className="text-xs text-gray-600 mt-1 space-y-1">
                                  {event.result.actions.map((action, index) => (
                                    <li key={index}>• {action.description}</li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </div>
      )}

      {/* 状态监控页面 */}
      {activeTab === 'status' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* 同步状态 */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">同步状态</h3>
              <RefreshCw className="w-5 h-5 text-gray-400" />
            </div>
            <div className="mt-4">
              <p className="text-2xl font-bold text-green-600">正常</p>
              <p className="text-sm text-gray-500">最后同步: 2分钟前</p>
            </div>
          </div>

          {/* 事件统计 */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">今日事件</h3>
              <Bell className="w-5 h-5 text-gray-400" />
            </div>
            <div className="mt-4">
              <p className="text-2xl font-bold text-blue-600">{events.length}</p>
              <p className="text-sm text-gray-500">成功: {events.filter(e => e.status === 'completed').length}</p>
            </div>
          </div>

          {/* 配置状态 */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">自动化状态</h3>
              <Settings className="w-5 h-5 text-gray-400" />
            </div>
            <div className="mt-4">
              <p className="text-2xl font-bold text-green-600">
                {config?.autoSync ? '已启用' : '已禁用'}
              </p>
              <p className="text-sm text-gray-500">
                间隔: {config?.syncInterval || 30} 分钟
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
