import React, { useState, useEffect } from 'react';
import { 
  ChevronDown, 
  Plus, 
  Search, 
  Folder,
  Circle,
  CheckCircle,
  Clock,
  Pause,
  Archive
} from 'lucide-react';
import { Project, Workspace } from '../../types/MultiProject';
import { MultiProjectService } from '../../services/MultiProjectService';

interface ProjectSelectorProps {
  onProjectChange?: (project: Project | null) => void;
  onWorkspaceChange?: (workspace: Workspace | null) => void;
}

export const ProjectSelector: React.FC<ProjectSelectorProps> = ({
  onProjectChange,
  onWorkspaceChange
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentWorkspace, setCurrentWorkspace] = useState<Workspace | null>(null);
  const [currentProject, setCurrentProject] = useState<Project | null>(null);
  const [projects, setProjects] = useState<Project[]>([]);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    const workspace = MultiProjectService.getCurrentWorkspace();
    const project = MultiProjectService.getCurrentProject();
    const allProjects = MultiProjectService.getAllProjects();

    setCurrentWorkspace(workspace);
    setCurrentProject(project);
    setProjects(allProjects);
  };

  const handleProjectSelect = (project: Project) => {
    MultiProjectService.setCurrentProject(project.id);
    setCurrentProject(project);
    setIsOpen(false);
    onProjectChange?.(project);
  };

  const handleCreateProject = () => {
    const projectName = prompt('请输入项目名称:');
    if (projectName) {
      const newProject = MultiProjectService.createProject({
        name: projectName,
        description: '新创建的项目'
      });
      loadData();
      handleProjectSelect(newProject);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'active':
        return <Circle className="w-4 h-4 text-blue-500" />;
      case 'paused':
        return <Pause className="w-4 h-4 text-yellow-500" />;
      case 'archived':
        return <Archive className="w-4 h-4 text-gray-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusText = (status: string) => {
    const statusMap = {
      'planning': '规划中',
      'active': '进行中',
      'paused': '已暂停',
      'completed': '已完成',
      'archived': '已归档'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  const filteredProjects = projects.filter(project =>
    project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    project.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="relative">
      {/* 当前选择显示 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-3 px-4 py-2 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors min-w-64"
      >
        <div className="flex items-center space-x-2 flex-1">
          <Folder className="w-5 h-5 text-gray-500" />
          <div className="text-left">
            <div className="font-medium text-gray-900">
              {currentProject?.name || '选择项目'}
            </div>
            <div className="text-xs text-gray-500">
              {currentWorkspace?.name || '工作空间'}
            </div>
          </div>
        </div>
        <ChevronDown className={`w-4 h-4 text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* 下拉菜单 */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-hidden">
          {/* 搜索框 */}
          <div className="p-3 border-b border-gray-100">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索项目..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* 项目列表 */}
          <div className="max-h-64 overflow-y-auto">
            {filteredProjects.length > 0 ? (
              filteredProjects.map((project) => (
                <button
                  key={project.id}
                  onClick={() => handleProjectSelect(project)}
                  className={`w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors border-b border-gray-50 last:border-b-0 ${
                    currentProject?.id === project.id ? 'bg-blue-50' : ''
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div 
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: project.color }}
                    ></div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-gray-900">{project.name}</span>
                        {getStatusIcon(project.status)}
                      </div>
                      <div className="flex items-center space-x-4 mt-1">
                        <span className="text-xs text-gray-500">
                          {getStatusText(project.status)}
                        </span>
                        <span className="text-xs text-gray-500">
                          {project.progress}% 完成
                        </span>
                        {project.tags.length > 0 && (
                          <span className="text-xs text-gray-500">
                            {project.tags.slice(0, 2).join(', ')}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </button>
              ))
            ) : (
              <div className="px-4 py-8 text-center text-gray-500">
                {searchTerm ? '未找到匹配的项目' : '暂无项目'}
              </div>
            )}
          </div>

          {/* 操作按钮 */}
          <div className="p-3 border-t border-gray-100 bg-gray-50">
            <button
              onClick={handleCreateProject}
              className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>新建项目</span>
            </button>
          </div>
        </div>
      )}

      {/* 点击外部关闭 */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        ></div>
      )}
    </div>
  );
};

export default ProjectSelector;
