/**
 * 在线平台集成管理组件
 */

import React, { useState, useEffect } from 'react';
import {
  Database,
  Settings,
  RefreshCw as Sync,
  TestTube,
  CheckCircle,
  XCircle,
  RefreshCw,
  Plus,
  Trash2,
  Edit,
  ExternalLink
} from 'lucide-react';
import { NotionIntegration, NotionConfig } from '../../services/integrations/NotionIntegration';
import { AirtableIntegration, AirtableConfig } from '../../services/integrations/AirtableIntegration';
import { useProjectContext } from '../../hooks/useProjectContext';

type PlatformType = 'notion' | 'airtable';

interface PlatformConfig {
  id: string;
  type: PlatformType;
  name: string;
  config: NotionConfig | AirtableConfig;
  fieldMappings: Record<string, string>;
  lastSync?: string;
  status: 'connected' | 'disconnected' | 'error';
}

export const OnlinePlatformManager: React.FC = () => {
  const { currentProject } = useProjectContext();
  const [platforms, setPlatforms] = useState<PlatformConfig[]>([]);
  const [activeTab, setActiveTab] = useState<'list' | 'add' | 'sync'>('list');
  const [selectedPlatform, setSelectedPlatform] = useState<PlatformConfig | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (currentProject) {
      loadPlatformConfigs();
    }
  }, [currentProject]);

  const loadPlatformConfigs = () => {
    if (!currentProject) return;

    const stored = localStorage.getItem(`platform-configs-${currentProject.id}`);
    if (stored) {
      setPlatforms(JSON.parse(stored));
    }
  };

  const savePlatformConfigs = (configs: PlatformConfig[]) => {
    if (!currentProject) return;

    localStorage.setItem(`platform-configs-${currentProject.id}`, JSON.stringify(configs));
    setPlatforms(configs);
  };

  const handleAddPlatform = (platformConfig: PlatformConfig) => {
    const newConfigs = [...platforms, platformConfig];
    savePlatformConfigs(newConfigs);
    setActiveTab('list');
  };

  const handleUpdatePlatform = (updatedConfig: PlatformConfig) => {
    const newConfigs = platforms.map(p => 
      p.id === updatedConfig.id ? updatedConfig : p
    );
    savePlatformConfigs(newConfigs);
    setSelectedPlatform(null);
  };

  const handleDeletePlatform = (platformId: string) => {
    const newConfigs = platforms.filter(p => p.id !== platformId);
    savePlatformConfigs(newConfigs);
  };

  const handleTestConnection = async (platform: PlatformConfig) => {
    setIsLoading(true);
    try {
      let result;
      
      if (platform.type === 'notion') {
        result = await NotionIntegration.validateConnection(platform.config as NotionConfig);
      } else if (platform.type === 'airtable') {
        result = await AirtableIntegration.validateConnection(platform.config as AirtableConfig);
      }

      const updatedPlatform = {
        ...platform,
        status: result?.valid ? 'connected' as const : 'error' as const
      };

      handleUpdatePlatform(updatedPlatform);
    } catch (error) {
      console.error('测试连接失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSync = async (platform: PlatformConfig, direction: 'to-platform' | 'from-platform') => {
    if (!currentProject) return;

    setIsLoading(true);
    try {
      let result;

      if (direction === 'to-platform') {
        // 同步到在线平台
        const projectData = {
          id: currentProject.id,
          projectTree: null, // 从 ProjectContext 获取
          documents: [], // 从 ProjectContext 获取
        };

        if (platform.type === 'notion') {
          result = await NotionIntegration.syncProjectToNotion(
            platform.config as NotionConfig,
            { projectId: currentProject.id, notionDatabaseId: '', fieldMappings: platform.fieldMappings },
            projectData
          );
        } else if (platform.type === 'airtable') {
          result = await AirtableIntegration.syncProjectToAirtable(
            platform.config as AirtableConfig,
            projectData,
            platform.fieldMappings
          );
        }
      } else {
        // 从在线平台同步
        if (platform.type === 'notion') {
          result = await NotionIntegration.syncNotionToProject(
            platform.config as NotionConfig,
            { projectId: currentProject.id, notionDatabaseId: '', fieldMappings: platform.fieldMappings }
          );
        } else if (platform.type === 'airtable') {
          result = await AirtableIntegration.syncAirtableToProject(
            platform.config as AirtableConfig,
            currentProject.id,
            platform.fieldMappings
          );
        }
      }

      if (result?.success) {
        const updatedPlatform = {
          ...platform,
          lastSync: new Date().toISOString()
        };
        handleUpdatePlatform(updatedPlatform);
      }

      console.log('同步结果:', result);
    } catch (error) {
      console.error('同步失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getPlatformIcon = (type: PlatformType) => {
    switch (type) {
      case 'notion': return '📝';
      case 'airtable': return '📊';
      default: return '🔗';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'text-green-500';
      case 'error': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected': return <CheckCircle className="w-4 h-4" />;
      case 'error': return <XCircle className="w-4 h-4" />;
      default: return <RefreshCw className="w-4 h-4" />;
    }
  };

  if (!currentProject) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">请先选择一个项目</p>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* 页面头部 */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">在线平台集成</h1>
        <p className="text-gray-600">
          连接 Notion、Airtable 等在线平台，实现项目数据的双向同步
        </p>
      </div>

      {/* 标签页导航 */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'list', name: '平台列表', icon: Database },
            { id: 'add', name: '添加平台', icon: Plus },
            { id: 'sync', name: '同步管理', icon: Sync }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* 平台列表 */}
      {activeTab === 'list' && (
        <div className="space-y-4">
          {platforms.length === 0 ? (
            <div className="text-center py-12">
              <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">还没有配置任何在线平台</p>
              <button
                onClick={() => setActiveTab('add')}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                添加第一个平台
              </button>
            </div>
          ) : (
            platforms.map((platform) => (
              <div key={platform.id} className="bg-white rounded-lg border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="text-2xl">{getPlatformIcon(platform.type)}</div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{platform.name}</h3>
                      <p className="text-sm text-gray-500 capitalize">{platform.type}</p>
                    </div>
                    <div className={`flex items-center space-x-1 ${getStatusColor(platform.status)}`}>
                      {getStatusIcon(platform.status)}
                      <span className="text-sm font-medium capitalize">{platform.status}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleTestConnection(platform)}
                      disabled={isLoading}
                      className="flex items-center space-x-1 px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md"
                    >
                      <TestTube className="w-4 h-4" />
                      <span>测试</span>
                    </button>
                    
                    <button
                      onClick={() => handleSync(platform, 'to-platform')}
                      disabled={isLoading || platform.status !== 'connected'}
                      className="flex items-center space-x-1 px-3 py-1 text-sm bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-md disabled:opacity-50"
                    >
                      <Sync className="w-4 h-4" />
                      <span>推送</span>
                    </button>
                    
                    <button
                      onClick={() => handleSync(platform, 'from-platform')}
                      disabled={isLoading || platform.status !== 'connected'}
                      className="flex items-center space-x-1 px-3 py-1 text-sm bg-green-100 hover:bg-green-200 text-green-700 rounded-md disabled:opacity-50"
                    >
                      <RefreshCw className="w-4 h-4" />
                      <span>拉取</span>
                    </button>
                    
                    <button
                      onClick={() => setSelectedPlatform(platform)}
                      className="p-1 text-gray-400 hover:text-gray-600"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    
                    <button
                      onClick={() => handleDeletePlatform(platform.id)}
                      className="p-1 text-red-400 hover:text-red-600"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                
                {platform.lastSync && (
                  <div className="mt-4 text-sm text-gray-500">
                    最后同步: {new Date(platform.lastSync).toLocaleString()}
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      )}

      {/* 添加平台 */}
      {activeTab === 'add' && (
        <PlatformConfigForm
          onSave={handleAddPlatform}
          onCancel={() => setActiveTab('list')}
        />
      )}

      {/* 同步管理 */}
      {activeTab === 'sync' && (
        <SyncManagement
          platforms={platforms}
          onSync={handleSync}
          isLoading={isLoading}
        />
      )}

      {/* 编辑平台配置 */}
      {selectedPlatform && (
        <PlatformConfigForm
          platform={selectedPlatform}
          onSave={handleUpdatePlatform}
          onCancel={() => setSelectedPlatform(null)}
        />
      )}
    </div>
  );
};

// 平台配置表单组件
const PlatformConfigForm: React.FC<{
  platform?: PlatformConfig;
  onSave: (config: PlatformConfig) => void;
  onCancel: () => void;
}> = ({ platform, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    type: platform?.type || 'notion' as PlatformType,
    name: platform?.name || '',
    apiKey: '',
    databaseId: '',
    baseId: '',
    tableId: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const config: PlatformConfig = {
      id: platform?.id || `${formData.type}-${Date.now()}`,
      type: formData.type,
      name: formData.name,
      config: formData.type === 'notion' 
        ? { apiKey: formData.apiKey, databaseId: formData.databaseId }
        : { apiKey: formData.apiKey, baseId: formData.baseId, tableId: formData.tableId },
      fieldMappings: {},
      status: 'disconnected'
    };

    onSave(config);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 className="text-lg font-semibold mb-4">
          {platform ? '编辑平台配置' : '添加新平台'}
        </h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              平台类型
            </label>
            <select
              value={formData.type}
              onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as PlatformType }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="notion">Notion</option>
              <option value="airtable">Airtable</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              配置名称
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="例如：项目管理数据库"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              API Key
            </label>
            <input
              type="password"
              value={formData.apiKey}
              onChange={(e) => setFormData(prev => ({ ...prev, apiKey: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          {formData.type === 'notion' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Database ID
              </label>
              <input
                type="text"
                value={formData.databaseId}
                onChange={(e) => setFormData(prev => ({ ...prev, databaseId: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
          )}

          {formData.type === 'airtable' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Base ID
                </label>
                <input
                  type="text"
                  value={formData.baseId}
                  onChange={(e) => setFormData(prev => ({ ...prev, baseId: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Table ID
                </label>
                <input
                  type="text"
                  value={formData.tableId}
                  onChange={(e) => setFormData(prev => ({ ...prev, tableId: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
            </>
          )}

          <div className="flex space-x-3 pt-4">
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              保存
            </button>
            <button
              type="button"
              onClick={onCancel}
              className="flex-1 px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
            >
              取消
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// 同步管理组件
const SyncManagement: React.FC<{
  platforms: PlatformConfig[];
  onSync: (platform: PlatformConfig, direction: 'to-platform' | 'from-platform') => void;
  isLoading: boolean;
}> = ({ platforms, onSync, isLoading }) => {
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h2 className="text-lg font-semibold mb-4">批量同步</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            disabled={isLoading || platforms.length === 0}
            className="flex items-center justify-center space-x-2 p-4 border-2 border-dashed border-blue-300 rounded-lg hover:border-blue-400 disabled:opacity-50"
          >
            <Sync className="w-5 h-5 text-blue-500" />
            <span className="text-blue-700 font-medium">推送到所有平台</span>
          </button>
          
          <button
            disabled={isLoading || platforms.length === 0}
            className="flex items-center justify-center space-x-2 p-4 border-2 border-dashed border-green-300 rounded-lg hover:border-green-400 disabled:opacity-50"
          >
            <RefreshCw className="w-5 h-5 text-green-500" />
            <span className="text-green-700 font-medium">从所有平台拉取</span>
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h2 className="text-lg font-semibold mb-4">同步历史</h2>
        <div className="text-center py-8 text-gray-500">
          暂无同步历史记录
        </div>
      </div>
    </div>
  );
};
