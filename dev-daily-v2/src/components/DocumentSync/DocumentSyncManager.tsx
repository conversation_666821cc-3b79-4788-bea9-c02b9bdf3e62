/**
 * 文档同步管理器 - 专注于GitHub Markdown文档的同步和版本管理
 */

import React, { useState, useEffect } from 'react';
import {
  RefreshCw,
  Github,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  Settings,
  Plus,
  Eye,
  History,
  Download,
  Upload,
  Trash2,
  GitBranch
} from 'lucide-react';
import { DocumentSyncService, SyncProject, SyncResult, GitHubDocument } from '../../services/DocumentSyncService';
import clsx from 'clsx';

interface DocumentSyncManagerProps {
  className?: string;
}

// 项目卡片组件
const ProjectCard: React.FC<{
  project: SyncProject;
  onSync: (projectId: string) => void;
  onConfigure: (project: SyncProject) => void;
  onViewDocuments: (project: SyncProject) => void;
  isLoading?: boolean;
}> = ({ project, onSync, onConfigure, onViewDocuments, isLoading }) => {
  const getStatusColor = (enabled: boolean, lastSync?: string) => {
    if (!enabled) return 'text-gray-400';
    if (!lastSync) return 'text-yellow-500';
    
    const daysSinceSync = (Date.now() - new Date(lastSync).getTime()) / (1000 * 60 * 60 * 24);
    if (daysSinceSync > 7) return 'text-red-500';
    if (daysSinceSync > 3) return 'text-yellow-500';
    return 'text-green-500';
  };

  const getStatusText = (enabled: boolean, lastSync?: string) => {
    if (!enabled) return '已禁用';
    if (!lastSync) return '未同步';
    
    const daysSinceSync = (Date.now() - new Date(lastSync).getTime()) / (1000 * 60 * 60 * 24);
    if (daysSinceSync > 7) return '需要同步';
    if (daysSinceSync > 3) return '建议同步';
    return '已同步';
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <Github className="w-8 h-8 text-gray-700" />
          <div>
            <h3 className="font-semibold text-gray-900">{project.name}</h3>
            <p className="text-sm text-gray-500 truncate max-w-xs">{project.githubUrl}</p>
          </div>
        </div>
        <div className={clsx('flex items-center gap-1 text-sm', getStatusColor(project.syncEnabled, project.lastSyncAt))}>
          <div className="w-2 h-2 rounded-full bg-current"></div>
          {getStatusText(project.syncEnabled, project.lastSyncAt)}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">{project.documentCount}</div>
          <div className="text-sm text-gray-600">文档数量</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600">{project.totalVersions}</div>
          <div className="text-sm text-gray-600">版本总数</div>
        </div>
      </div>

      {project.lastSyncAt && (
        <div className="text-xs text-gray-500 mb-4">
          最后同步: {new Date(project.lastSyncAt).toLocaleString()}
        </div>
      )}

      <div className="flex gap-2">
        <button
          onClick={() => onSync(project.id)}
          disabled={isLoading || !project.syncEnabled}
          className={clsx(
            'flex-1 flex items-center justify-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors',
            project.syncEnabled && !isLoading
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
          )}
        >
          <RefreshCw className={clsx('w-4 h-4', isLoading && 'animate-spin')} />
          {isLoading ? '同步中...' : '同步'}
        </button>
        
        <button
          onClick={() => onViewDocuments(project)}
          className="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          title="查看文档"
        >
          <Eye className="w-4 h-4" />
        </button>
        
        <button
          onClick={() => onConfigure(project)}
          className="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          title="配置"
        >
          <Settings className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};

// 同步结果组件
const SyncResultCard: React.FC<{ result: SyncResult }> = ({ result }) => (
  <div className={clsx(
    'p-4 rounded-lg border',
    result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
  )}>
    <div className="flex items-center gap-2 mb-2">
      {result.success ? (
        <CheckCircle className="w-5 h-5 text-green-600" />
      ) : (
        <AlertCircle className="w-5 h-5 text-red-600" />
      )}
      <span className={clsx('font-medium', result.success ? 'text-green-800' : 'text-red-800')}>
        {result.success ? '同步成功' : '同步失败'}
      </span>
    </div>
    
    <div className="text-sm space-y-1">
      <div>处理文档: {result.documentsProcessed} 个</div>
      <div>创建版本: {result.versionsCreated} 个</div>
      <div>耗时: {(result.syncDuration / 1000).toFixed(1)} 秒</div>
      {result.errors.length > 0 && (
        <div className="text-red-600">
          错误: {result.errors.length} 个
          <details className="mt-1">
            <summary className="cursor-pointer">查看详情</summary>
            <ul className="mt-1 space-y-1">
              {result.errors.map((error, index) => (
                <li key={index} className="text-xs">{error}</li>
              ))}
            </ul>
          </details>
        </div>
      )}
    </div>
  </div>
);

// 文档列表组件
const DocumentList: React.FC<{
  documents: GitHubDocument[];
  onViewVersions: (document: GitHubDocument) => void;
}> = ({ documents, onViewVersions }) => (
  <div className="space-y-3">
    {documents.map((doc) => (
      <div key={doc.id} className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3">
            <FileText className="w-5 h-5 text-gray-500 mt-0.5" />
            <div>
              <h4 className="font-medium text-gray-900">{doc.title}</h4>
              <p className="text-sm text-gray-500">{doc.filePath}</p>
              <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                <span>大小: {(doc.size / 1024).toFixed(1)} KB</span>
                <span>更新: {new Date(doc.lastModified).toLocaleDateString()}</span>
                <span>作者: {doc.author}</span>
              </div>
            </div>
          </div>
          <button
            onClick={() => onViewVersions(doc)}
            className="flex items-center gap-1 px-2 py-1 text-sm text-blue-600 hover:bg-blue-50 rounded"
          >
            <History className="w-4 h-4" />
            版本历史
          </button>
        </div>
      </div>
    ))}
  </div>
);

// 主组件
export const DocumentSyncManager: React.FC<DocumentSyncManagerProps> = ({ className }) => {
  const [projects, setProjects] = useState<SyncProject[]>([]);
  const [selectedProject, setSelectedProject] = useState<SyncProject | null>(null);
  const [documents, setDocuments] = useState<GitHubDocument[]>([]);
  const [syncResults, setSyncResults] = useState<SyncResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'projects' | 'documents' | 'results'>('projects');

  // 模拟数据加载
  useEffect(() => {
    loadProjects();
  }, []);

  const loadProjects = async () => {
    // 模拟项目数据
    const mockProjects: SyncProject[] = [
      {
        id: 'proj-1',
        name: 'dev-daily-v2',
        githubUrl: 'https://github.com/user/dev-daily-v2',
        syncEnabled: true,
        lastSyncAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        documentCount: 15,
        totalVersions: 42
      },
      {
        id: 'proj-2',
        name: 'project-docs',
        githubUrl: 'https://github.com/user/project-docs',
        syncEnabled: false,
        documentCount: 8,
        totalVersions: 12
      }
    ];
    setProjects(mockProjects);
  };

  const handleSync = async (projectId: string) => {
    const project = projects.find(p => p.id === projectId);
    if (!project) return;

    setIsLoading(true);
    try {
      const result = await DocumentSyncService.syncProject(project);
      setSyncResults(prev => [result, ...prev.slice(0, 9)]);
      
      // 更新项目状态
      setProjects(prev => prev.map(p => 
        p.id === projectId 
          ? { ...p, lastSyncAt: new Date().toISOString(), documentCount: result.documentsProcessed }
          : p
      ));
    } catch (error) {
      console.error('同步失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewDocuments = async (project: SyncProject) => {
    setSelectedProject(project);
    setActiveTab('documents');
    
    // 加载项目文档
    try {
      const docs = await DocumentSyncService.discoverDocuments(
        project.githubUrl,
        project.githubToken || ''
      );
      setDocuments(docs);
    } catch (error) {
      console.error('加载文档失败:', error);
      setDocuments([]);
    }
  };

  const handleConfigure = (project: SyncProject) => {
    // 打开配置对话框
    console.log('配置项目:', project);
  };

  const handleViewVersions = (document: GitHubDocument) => {
    // 查看文档版本历史
    console.log('查看版本历史:', document);
  };

  return (
    <div className={clsx('h-full overflow-y-auto', className)}>
      <div className="max-w-6xl mx-auto p-6">
        {/* 页面头部 */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <GitBranch className="w-8 h-8 text-blue-600" />
              <div>
                <h1 className="text-3xl font-bold text-gray-900">文档同步管理</h1>
                <p className="text-gray-600">GitHub Markdown文档的同步和版本管理</p>
              </div>
            </div>
            <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
              <Plus className="w-4 h-4" />
              添加项目
            </button>
          </div>
        </div>

        {/* 标签页导航 */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'projects', name: '项目管理', icon: Github },
              { id: 'documents', name: '文档列表', icon: FileText },
              { id: 'results', name: '同步结果', icon: Clock }
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={clsx(
                    'py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors',
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  )}
                >
                  <Icon className="w-4 h-4" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>

        {/* 标签页内容 */}
        <div className="min-h-96">
          {activeTab === 'projects' && (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {projects.map((project) => (
                <ProjectCard
                  key={project.id}
                  project={project}
                  onSync={handleSync}
                  onConfigure={handleConfigure}
                  onViewDocuments={handleViewDocuments}
                  isLoading={isLoading}
                />
              ))}
            </div>
          )}

          {activeTab === 'documents' && (
            <div>
              {selectedProject ? (
                <div>
                  <div className="mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {selectedProject.name} - 文档列表
                    </h3>
                    <p className="text-sm text-gray-600">{selectedProject.githubUrl}</p>
                  </div>
                  <DocumentList
                    documents={documents}
                    onViewVersions={handleViewVersions}
                  />
                </div>
              ) : (
                <div className="text-center text-gray-500 py-12">
                  请先选择一个项目查看文档
                </div>
              )}
            </div>
          )}

          {activeTab === 'results' && (
            <div className="space-y-4">
              {syncResults.length > 0 ? (
                syncResults.map((result, index) => (
                  <SyncResultCard key={index} result={result} />
                ))
              ) : (
                <div className="text-center text-gray-500 py-12">
                  暂无同步记录
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
