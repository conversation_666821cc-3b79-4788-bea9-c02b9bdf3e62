/**
 * 离线状态指示器组件
 */

import React, { useState, useEffect } from 'react';
import { 
  Wifi, 
  WifiOff, 
  Cloud, 
  CloudOff, 
  RefreshCw, 
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { ServiceWorkerService, OfflineStatus } from '../../services/ServiceWorkerService';

export const OfflineIndicator: React.FC = () => {
  const [offlineStatus, setOfflineStatus] = useState<OfflineStatus>({
    isOnline: navigator.onLine,
    syncPending: false
  });
  const [networkQuality, setNetworkQuality] = useState<{
    speed: 'fast' | 'slow' | 'offline';
    latency?: number;
  }>({ speed: 'offline' });
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    // 初始化状态
    updateStatus();

    // 监听网络状态变化
    const handleOnline = () => {
      updateStatus();
      checkNetworkQuality();
    };

    const handleOffline = () => {
      updateStatus();
      setNetworkQuality({ speed: 'offline' });
    };

    const handleSyncPendingChange = (pending: boolean) => {
      setOfflineStatus(prev => ({ ...prev, syncPending: pending }));
    };

    ServiceWorkerService.addEventListener('online', handleOnline);
    ServiceWorkerService.addEventListener('offline', handleOffline);
    ServiceWorkerService.addEventListener('syncPendingChange', handleSyncPendingChange);

    // 定期检查网络质量
    const qualityInterval = setInterval(checkNetworkQuality, 30000);

    return () => {
      ServiceWorkerService.removeEventListener('online', handleOnline);
      ServiceWorkerService.removeEventListener('offline', handleOffline);
      ServiceWorkerService.removeEventListener('syncPendingChange', handleSyncPendingChange);
      clearInterval(qualityInterval);
    };
  }, []);

  const updateStatus = () => {
    const status = ServiceWorkerService.getOfflineStatus();
    setOfflineStatus(status);
  };

  const checkNetworkQuality = async () => {
    const quality = await ServiceWorkerService.checkNetworkQuality();
    setNetworkQuality(quality);
  };

  const handleRetryConnection = async () => {
    await checkNetworkQuality();
    updateStatus();
  };

  const getStatusColor = () => {
    if (!offlineStatus.isOnline) return 'text-red-500';
    if (offlineStatus.syncPending) return 'text-yellow-500';
    if (networkQuality.speed === 'slow') return 'text-yellow-500';
    return 'text-green-500';
  };

  const getStatusIcon = () => {
    if (!offlineStatus.isOnline) {
      return <WifiOff className="w-4 h-4" />;
    }
    if (offlineStatus.syncPending) {
      return <RefreshCw className="w-4 h-4 animate-spin" />;
    }
    if (networkQuality.speed === 'slow') {
      return <Wifi className="w-4 h-4" />;
    }
    return <Wifi className="w-4 h-4" />;
  };

  const getStatusText = () => {
    if (!offlineStatus.isOnline) return '离线';
    if (offlineStatus.syncPending) return '同步中';
    if (networkQuality.speed === 'slow') return '网络较慢';
    return '在线';
  };

  const formatLastOnline = (date?: Date) => {
    if (!date) return '未知';
    
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    return `${days}天前`;
  };

  return (
    <div className="relative">
      {/* 状态指示器 */}
      <button
        onClick={() => setShowDetails(!showDetails)}
        className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm transition-colors ${
          offlineStatus.isOnline 
            ? 'bg-green-50 hover:bg-green-100' 
            : 'bg-red-50 hover:bg-red-100'
        }`}
      >
        <span className={getStatusColor()}>
          {getStatusIcon()}
        </span>
        <span className={`font-medium ${getStatusColor()}`}>
          {getStatusText()}
        </span>
      </button>

      {/* 详细信息弹窗 */}
      {showDetails && (
        <div className="absolute top-full right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-50">
          <div className="space-y-4">
            {/* 网络状态 */}
            <div>
              <h3 className="text-sm font-semibold text-gray-900 mb-2">网络状态</h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">连接状态</span>
                  <div className="flex items-center space-x-1">
                    {offlineStatus.isOnline ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : (
                      <AlertCircle className="w-4 h-4 text-red-500" />
                    )}
                    <span className={`text-sm font-medium ${
                      offlineStatus.isOnline ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {offlineStatus.isOnline ? '在线' : '离线'}
                    </span>
                  </div>
                </div>

                {offlineStatus.isOnline && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">网络速度</span>
                    <span className={`text-sm font-medium ${
                      networkQuality.speed === 'fast' ? 'text-green-600' :
                      networkQuality.speed === 'slow' ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {networkQuality.speed === 'fast' ? '快速' :
                       networkQuality.speed === 'slow' ? '较慢' : '离线'}
                    </span>
                  </div>
                )}

                {networkQuality.latency && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">延迟</span>
                    <span className="text-sm font-medium text-gray-900">
                      {Math.round(networkQuality.latency)}ms
                    </span>
                  </div>
                )}

                {!offlineStatus.isOnline && offlineStatus.lastOnline && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">最后在线</span>
                    <span className="text-sm font-medium text-gray-900">
                      {formatLastOnline(offlineStatus.lastOnline)}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* 同步状态 */}
            <div>
              <h3 className="text-sm font-semibold text-gray-900 mb-2">同步状态</h3>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">待同步数据</span>
                <div className="flex items-center space-x-1">
                  {offlineStatus.syncPending ? (
                    <>
                      <Clock className="w-4 h-4 text-yellow-500" />
                      <span className="text-sm font-medium text-yellow-600">有待同步</span>
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-sm font-medium text-green-600">已同步</span>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* 离线功能 */}
            <div>
              <h3 className="text-sm font-semibold text-gray-900 mb-2">离线功能</h3>
              <div className="space-y-1 text-sm text-gray-600">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-3 h-3 text-green-500" />
                  <span>查看缓存的项目数据</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-3 h-3 text-green-500" />
                  <span>编辑项目树和文档</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-3 h-3 text-green-500" />
                  <span>使用 AI 助手功能</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-3 h-3 text-green-500" />
                  <span>自动保存本地更改</span>
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex space-x-2 pt-2 border-t border-gray-200">
              <button
                onClick={handleRetryConnection}
                className="flex-1 flex items-center justify-center space-x-1 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <RefreshCw className="w-4 h-4" />
                <span>重试连接</span>
              </button>
              <button
                onClick={() => setShowDetails(false)}
                className="px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// 全局离线提示横幅
export const OfflineBanner: React.FC = () => {
  const [isOffline, setIsOffline] = useState(!navigator.onLine);
  const [syncPending, setSyncPending] = useState(false);

  useEffect(() => {
    const handleOnline = () => setIsOffline(false);
    const handleOffline = () => setIsOffline(true);
    const handleSyncPendingChange = (pending: boolean) => setSyncPending(pending);

    ServiceWorkerService.addEventListener('online', handleOnline);
    ServiceWorkerService.addEventListener('offline', handleOffline);
    ServiceWorkerService.addEventListener('syncPendingChange', handleSyncPendingChange);

    // 初始化同步状态
    const status = ServiceWorkerService.getOfflineStatus();
    setSyncPending(status.syncPending);

    return () => {
      ServiceWorkerService.removeEventListener('online', handleOnline);
      ServiceWorkerService.removeEventListener('offline', handleOffline);
      ServiceWorkerService.removeEventListener('syncPendingChange', handleSyncPendingChange);
    };
  }, []);

  if (!isOffline && !syncPending) return null;

  return (
    <div className={`w-full px-4 py-2 text-center text-sm font-medium ${
      isOffline 
        ? 'bg-red-100 text-red-800 border-b border-red-200' 
        : 'bg-yellow-100 text-yellow-800 border-b border-yellow-200'
    }`}>
      <div className="flex items-center justify-center space-x-2">
        {isOffline ? (
          <>
            <CloudOff className="w-4 h-4" />
            <span>您当前处于离线状态，数据将在联网后自动同步</span>
          </>
        ) : (
          <>
            <RefreshCw className="w-4 h-4 animate-spin" />
            <span>正在同步离线期间的更改...</span>
          </>
        )}
      </div>
    </div>
  );
};
