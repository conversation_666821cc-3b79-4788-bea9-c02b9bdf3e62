import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { X, Plus, Trash2 } from 'lucide-react';
import { DailyProgress } from './ProjectCalendar';
import { ProjectLogService } from '../../services/ProjectLogService';

interface AddProgressDialogProps {
  date: Date;
  projectId?: string;
  projectName?: string;
  onClose: () => void;
  onSave: (progress: DailyProgress) => void;
  existingProgress?: DailyProgress;
}

export const AddProgressDialog: React.FC<AddProgressDialogProps> = ({
  date,
  projectId = 'root',
  projectName = 'dev-daily v2 项目',
  onClose,
  onSave,
  existingProgress
}) => {
  const [completedTasks, setCompletedTasks] = useState(existingProgress?.completedTasks || 0);
  const [totalTasks, setTotalTasks] = useState(existingProgress?.totalTasks || 0);
  const [hoursWorked, setHoursWorked] = useState(existingProgress?.hoursWorked || 0);
  const [issues, setIssues] = useState<string[]>(existingProgress?.issues || []);
  const [achievements, setAchievements] = useState<string[]>(existingProgress?.achievements || []);
  const [notes, setNotes] = useState(existingProgress?.notes || '');
  const [newIssue, setNewIssue] = useState('');
  const [newAchievement, setNewAchievement] = useState('');

  const handleAddIssue = () => {
    if (newIssue.trim()) {
      setIssues([...issues, newIssue.trim()]);
      setNewIssue('');
    }
  };

  const handleRemoveIssue = (index: number) => {
    setIssues(issues.filter((_, i) => i !== index));
  };

  const handleAddAchievement = () => {
    if (newAchievement.trim()) {
      setAchievements([...achievements, newAchievement.trim()]);
      setNewAchievement('');
    }
  };

  const handleRemoveAchievement = (index: number) => {
    setAchievements(achievements.filter((_, i) => i !== index));
  };

  const handleSave = () => {
    const progress: DailyProgress = {
      date: format(date, 'yyyy-MM-dd'),
      projectId,
      projectName,
      completedTasks,
      totalTasks,
      hoursWorked,
      issues,
      achievements,
      notes
    };

    // 记录到项目日志
    if (completedTasks > 0) {
      ProjectLogService.addLogEntry({
        date: format(date, 'yyyy-MM-dd'),
        projectId,
        projectName,
        action: 'task_completed',
        description: `完成了 ${completedTasks} 个任务`,
        metadata: {
          hoursWorked,
          tags: ['daily-progress']
        }
      });
    }

    if (issues.length > 0) {
      issues.forEach(issue => {
        ProjectLogService.logIssueReported(
          projectId,
          projectName,
          issue
        );
      });
    }

    if (notes) {
      ProjectLogService.addProjectNote(
        projectId,
        projectName,
        notes
      );
    }

    onSave(progress);
    onClose();
  };

  // 按Enter键添加
  const handleIssueKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleAddIssue();
    }
  };

  const handleAchievementKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleAddAchievement();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">
            {format(date, 'yyyy年MM月dd日')} 进度记录
          </h3>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-full"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        <div className="space-y-4">
          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                已完成任务
              </label>
              <input
                type="number"
                min="0"
                value={completedTasks}
                onChange={(e) => setCompletedTasks(parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                总任务数
              </label>
              <input
                type="number"
                min="0"
                value={totalTasks}
                onChange={(e) => setTotalTasks(parseInt(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                工作时长 (小时)
              </label>
              <input
                type="number"
                min="0"
                step="0.5"
                value={hoursWorked}
                onChange={(e) => setHoursWorked(parseFloat(e.target.value) || 0)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              遇到的问题
            </label>
            <div className="flex space-x-2 mb-2">
              <input
                type="text"
                value={newIssue}
                onChange={(e) => setNewIssue(e.target.value)}
                onKeyDown={handleIssueKeyDown}
                placeholder="添加问题..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
              />
              <button
                onClick={handleAddIssue}
                className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                <Plus className="w-4 h-4" />
              </button>
            </div>
            {issues.length > 0 ? (
              <ul className="space-y-1">
                {issues.map((issue, index) => (
                  <li key={index} className="flex items-center justify-between bg-red-50 px-3 py-2 rounded-md">
                    <span className="text-sm text-red-800">{issue}</span>
                    <button
                      onClick={() => handleRemoveIssue(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-sm text-gray-500 italic">暂无问题记录</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              完成的成就
            </label>
            <div className="flex space-x-2 mb-2">
              <input
                type="text"
                value={newAchievement}
                onChange={(e) => setNewAchievement(e.target.value)}
                onKeyDown={handleAchievementKeyDown}
                placeholder="添加成就..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md"
              />
              <button
                onClick={handleAddAchievement}
                className="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                <Plus className="w-4 h-4" />
              </button>
            </div>
            {achievements.length > 0 ? (
              <ul className="space-y-1">
                {achievements.map((achievement, index) => (
                  <li key={index} className="flex items-center justify-between bg-green-50 px-3 py-2 rounded-md">
                    <span className="text-sm text-green-800">{achievement}</span>
                    <button
                      onClick={() => handleRemoveAchievement(index)}
                      className="text-green-500 hover:text-green-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-sm text-gray-500 italic">暂无成就记录</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              日志笔记
            </label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="记录今日工作的总结、思考或计划..."
            ></textarea>
          </div>
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-md"
          >
            取消
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 rounded-md"
          >
            保存进度
          </button>
        </div>
      </div>
    </div>
  );
};

export default AddProgressDialog;
