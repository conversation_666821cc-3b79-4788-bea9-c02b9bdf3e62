import React, { useState, useEffect } from 'react';
import {
  Calendar as CalendarIcon,
  ChevronLeft,
  ChevronRight,
  Clock,
  Target,
  AlertCircle,
  CheckCircle,
  TrendingUp,
  BarChart3
} from 'lucide-react';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, isToday, addMonths, subMonths } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { AddProgressDialog } from './AddProgressDialog';
import { ProjectLogService } from '../../services/ProjectLogService';

export interface DailyProgress {
  date: string;
  projectId: string;
  projectName: string;
  completedTasks: number;
  totalTasks: number;
  hoursWorked: number;
  issues: string[];
  achievements: string[];
  notes: string;
}

export interface CalendarProps {
  dailyProgress: DailyProgress[];
  onDateSelect?: (date: Date) => void;
  onAddProgress?: (date: Date) => void;
}

export const ProjectCalendar: React.FC<CalendarProps> = ({
  dailyProgress,
  onDateSelect,
  onAddProgress
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [viewMode, setViewMode] = useState<'calendar' | 'list'>('calendar');
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [addDialogDate, setAddDialogDate] = useState<Date | null>(null);
  const [localProgress, setLocalProgress] = useState<DailyProgress[]>(dailyProgress);

  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);
  const monthDays = eachDayOfInterval({ start: monthStart, end: monthEnd });

  // 初始化时从服务加载进度数据
  useEffect(() => {
    const storedProgress = ProjectLogService.getDailyProgress();
    if (storedProgress.length > 0) {
      setLocalProgress(storedProgress);
    } else if (dailyProgress.length > 0) {
      setLocalProgress(dailyProgress);
    }
  }, [dailyProgress]);

  // 获取指定日期的进度数据
  const getProgressForDate = (date: Date): DailyProgress[] => {
    const dateStr = format(date, 'yyyy-MM-dd');
    return localProgress.filter(p => p.date === dateStr);
  };

  // 计算日期的进度状态
  const getDateStatus = (date: Date) => {
    const progress = getProgressForDate(date);
    if (progress.length === 0) return 'none';
    
    const totalCompleted = progress.reduce((sum, p) => sum + p.completedTasks, 0);
    const totalTasks = progress.reduce((sum, p) => sum + p.totalTasks, 0);
    const completionRate = totalTasks > 0 ? totalCompleted / totalTasks : 0;
    
    if (completionRate >= 0.8) return 'excellent';
    if (completionRate >= 0.6) return 'good';
    if (completionRate >= 0.3) return 'moderate';
    return 'low';
  };

  // 获取状态对应的样式
  const getStatusStyle = (status: string) => {
    switch (status) {
      case 'excellent':
        return 'bg-green-100 border-green-300 text-green-800';
      case 'good':
        return 'bg-blue-100 border-blue-300 text-blue-800';
      case 'moderate':
        return 'bg-yellow-100 border-yellow-300 text-yellow-800';
      case 'low':
        return 'bg-red-100 border-red-300 text-red-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-600';
    }
  };

  const handleDateClick = (date: Date) => {
    setSelectedDate(date);
    onDateSelect?.(date);
  };

  const handlePrevMonth = () => {
    setCurrentDate(subMonths(currentDate, 1));
  };

  const handleNextMonth = () => {
    setCurrentDate(addMonths(currentDate, 1));
  };

  const handleAddProgressClick = (date: Date) => {
    setAddDialogDate(date);
    setShowAddDialog(true);
    onAddProgress?.(date);
  };

  const handleSaveProgress = (progress: DailyProgress) => {
    const updatedProgress = [...localProgress];
    const existingIndex = updatedProgress.findIndex(
      p => p.date === progress.date && p.projectId === progress.projectId
    );

    if (existingIndex >= 0) {
      updatedProgress[existingIndex] = progress;
    } else {
      updatedProgress.push(progress);
    }

    setLocalProgress(updatedProgress);
  };

  const selectedDateProgress = selectedDate ? getProgressForDate(selectedDate) : [];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* 头部 */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <CalendarIcon className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">项目日历</h2>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('calendar')}
              className={`px-3 py-1 rounded text-sm ${
                viewMode === 'calendar' 
                  ? 'bg-blue-100 text-blue-700' 
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              日历视图
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`px-3 py-1 rounded text-sm ${
                viewMode === 'list' 
                  ? 'bg-blue-100 text-blue-700' 
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              列表视图
            </button>
          </div>
        </div>
      </div>

      <div className="flex">
        {/* 日历主体 */}
        <div className="flex-1">
          {viewMode === 'calendar' ? (
            <div className="p-4">
              {/* 月份导航 */}
              <div className="flex items-center justify-between mb-4">
                <button
                  onClick={handlePrevMonth}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <ChevronLeft className="w-5 h-5" />
                </button>
                <h3 className="text-lg font-semibold">
                  {format(currentDate, 'yyyy年MM月', { locale: zhCN })}
                </h3>
                <button
                  onClick={handleNextMonth}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                >
                  <ChevronRight className="w-5 h-5" />
                </button>
              </div>

              {/* 星期标题 */}
              <div className="grid grid-cols-7 gap-1 mb-2">
                {['日', '一', '二', '三', '四', '五', '六'].map((day) => (
                  <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
                    {day}
                  </div>
                ))}
              </div>

              {/* 日期网格 */}
              <div className="grid grid-cols-7 gap-1">
                {monthDays.map((date) => {
                  const status = getDateStatus(date);
                  const progress = getProgressForDate(date);
                  const isSelected = selectedDate && isSameDay(date, selectedDate);
                  const isTodayDate = isToday(date);

                  return (
                    <button
                      key={date.toISOString()}
                      onClick={() => handleDateClick(date)}
                      className={`
                        p-2 min-h-[60px] border rounded-lg text-left transition-colors
                        ${getStatusStyle(status)}
                        ${isSelected ? 'ring-2 ring-blue-500' : ''}
                        ${isTodayDate ? 'ring-1 ring-blue-300' : ''}
                        hover:shadow-sm
                      `}
                    >
                      <div className="text-sm font-medium">
                        {format(date, 'd')}
                      </div>
                      {progress.length > 0 && (
                        <div className="mt-1">
                          <div className="text-xs">
                            {progress.reduce((sum, p) => sum + p.completedTasks, 0)}/
                            {progress.reduce((sum, p) => sum + p.totalTasks, 0)} 任务
                          </div>
                          <div className="text-xs">
                            {progress.reduce((sum, p) => sum + p.hoursWorked, 0)}h
                          </div>
                        </div>
                      )}
                    </button>
                  );
                })}
              </div>
            </div>
          ) : (
            /* 列表视图 */
            <div className="p-4">
              <div className="space-y-4">
                {localProgress
                  .filter(p => {
                    const progressDate = new Date(p.date);
                    return progressDate >= monthStart && progressDate <= monthEnd;
                  })
                  .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                  .map((progress, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-gray-900">
                          {format(new Date(progress.date), 'MM月dd日', { locale: zhCN })} - {progress.projectName}
                        </h4>
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Target className="w-4 h-4" />
                          <span>{progress.completedTasks}/{progress.totalTasks}</span>
                          <Clock className="w-4 h-4 ml-2" />
                          <span>{progress.hoursWorked}h</span>
                        </div>
                      </div>
                      
                      {progress.achievements.length > 0 && (
                        <div className="mb-2">
                          <div className="flex items-center space-x-1 mb-1">
                            <CheckCircle className="w-4 h-4 text-green-600" />
                            <span className="text-sm font-medium text-green-800">成就</span>
                          </div>
                          <ul className="text-sm text-gray-700 ml-5">
                            {progress.achievements.map((achievement, i) => (
                              <li key={i}>• {achievement}</li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {progress.issues.length > 0 && (
                        <div className="mb-2">
                          <div className="flex items-center space-x-1 mb-1">
                            <AlertCircle className="w-4 h-4 text-orange-600" />
                            <span className="text-sm font-medium text-orange-800">问题</span>
                          </div>
                          <ul className="text-sm text-gray-700 ml-5">
                            {progress.issues.map((issue, i) => (
                              <li key={i}>• {issue}</li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {progress.notes && (
                        <div className="text-sm text-gray-600 bg-white p-2 rounded border">
                          {progress.notes}
                        </div>
                      )}
                    </div>
                  ))}
              </div>
            </div>
          )}
        </div>

        {/* 侧边栏 - 选中日期详情 */}
        {selectedDate && (
          <div className="w-80 border-l border-gray-200 p-4">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                {format(selectedDate, 'yyyy年MM月dd日', { locale: zhCN })}
              </h3>
              <p className="text-sm text-gray-600">
                {format(selectedDate, 'EEEE', { locale: zhCN })}
              </p>
            </div>

            {selectedDateProgress.length > 0 ? (
              <div className="space-y-4">
                {selectedDateProgress.map((progress, index) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-3">
                    <h4 className="font-medium text-gray-900 mb-2">{progress.projectName}</h4>
                    
                    <div className="grid grid-cols-2 gap-2 mb-3 text-sm">
                      <div className="flex items-center space-x-1">
                        <Target className="w-4 h-4 text-blue-600" />
                        <span>{progress.completedTasks}/{progress.totalTasks} 任务</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="w-4 h-4 text-green-600" />
                        <span>{progress.hoursWorked} 小时</span>
                      </div>
                    </div>

                    {progress.achievements.length > 0 && (
                      <div className="mb-2">
                        <div className="text-xs font-medium text-green-800 mb-1">✅ 完成</div>
                        <ul className="text-xs text-gray-700 space-y-1">
                          {progress.achievements.map((achievement, i) => (
                            <li key={i}>• {achievement}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {progress.issues.length > 0 && (
                      <div className="mb-2">
                        <div className="text-xs font-medium text-orange-800 mb-1">⚠️ 问题</div>
                        <ul className="text-xs text-gray-700 space-y-1">
                          {progress.issues.map((issue, i) => (
                            <li key={i}>• {issue}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {progress.notes && (
                      <div className="text-xs text-gray-600 bg-white p-2 rounded border">
                        {progress.notes}
                      </div>
                    )}
                  </div>
                ))}

                <button
                  onClick={() => handleAddProgressClick(selectedDate)}
                  className="w-full py-2 px-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                >
                  添加今日进度
                </button>
              </div>
            ) : (
              <div className="text-center py-8">
                <CalendarIcon className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                <p className="text-gray-500 text-sm mb-4">该日期暂无进度记录</p>
                <button
                  onClick={() => handleAddProgressClick(selectedDate)}
                  className="py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                >
                  添加进度记录
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* 添加进度对话框 */}
      {showAddDialog && addDialogDate && (
        <AddProgressDialog
          date={addDialogDate}
          onClose={() => {
            setShowAddDialog(false);
            setAddDialogDate(null);
          }}
          onSave={handleSaveProgress}
          existingProgress={getProgressForDate(addDialogDate)[0]}
        />
      )}
    </div>
  );
};

export default ProjectCalendar;
