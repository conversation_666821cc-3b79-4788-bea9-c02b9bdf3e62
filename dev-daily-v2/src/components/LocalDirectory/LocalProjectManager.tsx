import React, { useState, useEffect } from 'react';
import {
  Folder,
  Search,
  RefreshCw,
  Link,
  Unlink,
  ExternalLink,
  Code,
  GitBranch,
  Clock,
  FileText,
  Settings,
  BarChart3,
  AlertCircle,
  CheckCircle,
  Plus,
  Upload
} from 'lucide-react';
import { LocalProjectInfo, MultiProjectLocalScanner } from '../../services/MultiProjectLocalScanner';
import { MultiProjectService } from '../../services/MultiProjectService';
import { ProjectDocumentUploader } from './ProjectDocumentUploader';
import { LocalProjectDocumentReader, ProjectInfo } from '../../services/LocalProjectDocumentReader';
import { Project } from '../../types/MultiProject';

export const LocalProjectManager: React.FC = () => {
  const [localProjects, setLocalProjects] = useState<LocalProjectInfo[]>([]);
  const [onlineProjects, setOnlineProjects] = useState<Project[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProject, setSelectedProject] = useState<LocalProjectInfo | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filterType, setFilterType] = useState<'all' | 'linked' | 'unlinked'>('all');
  const [showDocumentUploader, setShowDocumentUploader] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    const locals = MultiProjectLocalScanner.getLocalProjects();
    const onlines = MultiProjectService.getAllProjects();
    setLocalProjects(locals);
    setOnlineProjects(onlines);
  };

  const handleScan = async () => {
    setIsScanning(true);
    try {
      const basePath = 'C:\\Users\\<USER>\\Desktop\\VScode';
      await MultiProjectLocalScanner.scanDirectory(basePath);
      loadData();
    } catch (error) {
      console.error('扫描失败:', error);
    } finally {
      setIsScanning(false);
    }
  };

  const handleManualAdd = () => {
    const projectPath = prompt('请输入项目路径:', 'C:\\Users\\<USER>\\Desktop\\VScode\\cloudflare-user-analytics');
    const projectName = prompt('请输入项目名称:', 'cloudflare-user-analytics');

    if (projectPath && projectName) {
      // 手动添加项目
      const newProject = {
        path: projectPath,
        name: projectName,
        type: 'web' as const,
        framework: ['Cloudflare Workers', 'TypeScript', 'Hono'],
        packageManager: 'npm',
        gitRepository: true,
        lastModified: new Date().toISOString(),
        size: 5000000, // 5MB
        fileCount: 25,
        structure: {
          src: ['workers', 'database', 'dashboard'],
          components: ['analytics-collector', 'dashboard', 'api'],
          services: ['MultiProjectLocalScanner', 'AnalyticsUtils'],
          pages: ['dashboard'],
          tests: ['tests'],
          docs: ['docs'],
          config: ['wrangler.toml', 'package.json'],
          assets: ['public']
        },
        dependencies: ['hono', '@cloudflare/workers-types', 'typescript'],
        scripts: {
          'dev': 'wrangler dev',
          'deploy': 'wrangler deploy',
          'build': 'npm run build'
        }
      };

      // 保存到本地存储
      const existingProjects = MultiProjectLocalScanner.getLocalProjects();
      const updatedProjects = [...existingProjects, newProject];
      MultiProjectLocalScanner.saveLocalProjects(updatedProjects);

      loadData();
      alert('项目添加成功！');
    }
  };

  const handleProjectFromDocument = (projectInfo: ProjectInfo) => {
    // 将从文档解析的项目信息转换为 LocalProjectInfo 格式
    const localProject: LocalProjectInfo = {
      path: projectInfo.path,
      name: projectInfo.name,
      type: projectInfo.type as any,
      framework: Object.values(projectInfo.technology || {}).map(tech =>
        typeof tech === 'object' && tech !== null ? Object.values(tech).join(', ') : String(tech)
      ).filter(Boolean),
      packageManager: 'npm', // 默认值
      gitRepository: true, // 假设有 Git
      lastModified: projectInfo.lastModified,
      size: 5000000, // 默认大小
      fileCount: 25, // 默认文件数
      structure: projectInfo.structure || {},
      dependencies: [],
      scripts: {},
      linkedProjectId: ''
    };

    // 添加到本地项目列表
    const existingProjects = MultiProjectLocalScanner.getLocalProjects();
    const updatedProjects = [...existingProjects, localProject];
    MultiProjectLocalScanner.saveLocalProjects(updatedProjects);

    // 刷新列表
    loadData();

    // 显示成功消息
    alert(`成功从文档加载项目: ${projectInfo.name}`);
  };

  const handleAutoLink = () => {
    MultiProjectLocalScanner.autoLinkProjects();
    loadData();
  };

  const handleLinkProject = (localProject: LocalProjectInfo, onlineProjectId: string) => {
    MultiProjectLocalScanner.linkLocalToOnlineProject(localProject.path, onlineProjectId);
    loadData();
  };

  const handleUnlinkProject = (localProject: LocalProjectInfo) => {
    MultiProjectLocalScanner.linkLocalToOnlineProject(localProject.path, '');
    loadData();
  };

  const handleCreateOnlineProject = (localProject: LocalProjectInfo) => {
    const newProject = MultiProjectService.createProject({
      name: localProject.name,
      description: `从本地项目 ${localProject.path} 创建`,
      tags: localProject.framework
    });
    
    handleLinkProject(localProject, newProject.id);
  };

  const getProjectTypeIcon = (type: string) => {
    switch (type) {
      case 'web': return '🌐';
      case 'mobile': return '📱';
      case 'desktop': return '💻';
      case 'api': return '🔌';
      case 'library': return '📚';
      default: return '📁';
    }
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-100';
    if (score >= 60) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const filteredProjects = localProjects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.path.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filterType === 'all' || 
                         (filterType === 'linked' && project.linkedProjectId) ||
                         (filterType === 'unlinked' && !project.linkedProjectId);
    
    return matchesSearch && matchesFilter;
  });

  const linkStatus = MultiProjectLocalScanner.getProjectLinkStatus();

  return (
    <div className="p-6 bg-gray-50 min-h-full">
      {/* 头部 */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Folder className="w-6 h-6 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">本地项目管理</h1>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleAutoLink}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2"
            >
              <Link className="w-4 h-4" />
              <span>自动关联</span>
            </button>
            <button
              onClick={handleScan}
              disabled={isScanning}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2 disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${isScanning ? 'animate-spin' : ''}`} />
              <span>{isScanning ? '扫描中...' : '重新扫描'}</span>
            </button>
            <button
              onClick={handleManualAdd}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 flex items-center space-x-2"
            >
              <Plus className="w-4 h-4" />
              <span>手动添加</span>
            </button>
            <button
              onClick={() => setShowDocumentUploader(true)}
              className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 flex items-center space-x-2"
            >
              <Upload className="w-4 h-4" />
              <span>文档加载</span>
            </button>
          </div>
        </div>
        
        {/* 统计信息 */}
        <div className="grid grid-cols-4 gap-4 mt-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center space-x-2">
              <Folder className="w-5 h-5 text-blue-600" />
              <span className="text-sm font-medium text-gray-600">总项目</span>
            </div>
            <div className="text-2xl font-bold text-gray-900 mt-1">{localProjects.length}</div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center space-x-2">
              <Link className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium text-gray-600">已关联</span>
            </div>
            <div className="text-2xl font-bold text-green-600 mt-1">{linkStatus.linked}</div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center space-x-2">
              <Unlink className="w-5 h-5 text-orange-600" />
              <span className="text-sm font-medium text-gray-600">未关联</span>
            </div>
            <div className="text-2xl font-bold text-orange-600 mt-1">{linkStatus.unlinked}</div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center space-x-2">
              <BarChart3 className="w-5 h-5 text-purple-600" />
              <span className="text-sm font-medium text-gray-600">平均健康度</span>
            </div>
            <div className="text-2xl font-bold text-purple-600 mt-1">
              {localProjects.length > 0 
                ? Math.round(localProjects.reduce((sum, p) => 
                    sum + MultiProjectLocalScanner.getProjectHealthScore(p).score, 0
                  ) / localProjects.length)
                : 0}%
            </div>
          </div>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white p-4 rounded-lg border border-gray-200 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索项目..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-200 rounded-md w-64"
              />
            </div>
            
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value as any)}
              className="px-3 py-2 border border-gray-200 rounded-md"
            >
              <option value="all">所有项目</option>
              <option value="linked">已关联</option>
              <option value="unlinked">未关联</option>
            </select>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-400'}`}
            >
              <div className="w-4 h-4 grid grid-cols-2 gap-0.5">
                <div className="bg-current rounded-sm"></div>
                <div className="bg-current rounded-sm"></div>
                <div className="bg-current rounded-sm"></div>
                <div className="bg-current rounded-sm"></div>
              </div>
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-400'}`}
            >
              <div className="w-4 h-4 flex flex-col space-y-0.5">
                <div className="bg-current h-0.5 rounded"></div>
                <div className="bg-current h-0.5 rounded"></div>
                <div className="bg-current h-0.5 rounded"></div>
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* 项目列表 */}
      <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
        {filteredProjects.map((project) => {
          const healthScore = MultiProjectLocalScanner.getProjectHealthScore(project);
          const linkedOnlineProject = onlineProjects.find(p => p.id === project.linkedProjectId);
          
          return (
            <div key={project.path} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
              {/* 项目头部 */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{getProjectTypeIcon(project.type)}</span>
                  <div>
                    <h3 className="font-semibold text-gray-900">{project.name}</h3>
                    <p className="text-sm text-gray-500">{project.type}</p>
                  </div>
                </div>
                <div className={`px-2 py-1 rounded-full text-xs font-medium ${getHealthScoreColor(healthScore.score)}`}>
                  {healthScore.score}%
                </div>
              </div>

              {/* 项目信息 */}
              <div className="space-y-2 mb-4">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Code className="w-4 h-4" />
                  <span>{project.framework.join(', ')}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <FileText className="w-4 h-4" />
                  <span>{project.fileCount} 个文件</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <Clock className="w-4 h-4" />
                  <span>{new Date(project.lastModified).toLocaleDateString()}</span>
                </div>
                {project.gitRepository && (
                  <div className="flex items-center space-x-2 text-sm text-green-600">
                    <GitBranch className="w-4 h-4" />
                    <span>Git 仓库</span>
                  </div>
                )}
              </div>

              {/* 关联状态 */}
              <div className="border-t border-gray-100 pt-4">
                {project.linkedProjectId ? (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-sm text-green-600">
                        已关联: {linkedOnlineProject?.name}
                      </span>
                    </div>
                    <button
                      onClick={() => handleUnlinkProject(project)}
                      className="text-xs text-gray-500 hover:text-red-600"
                    >
                      取消关联
                    </button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <AlertCircle className="w-4 h-4 text-orange-600" />
                      <span className="text-sm text-orange-600">未关联在线项目</span>
                    </div>
                    <div className="flex space-x-2">
                      <select
                        onChange={(e) => e.target.value && handleLinkProject(project, e.target.value)}
                        className="flex-1 text-xs border border-gray-200 rounded px-2 py-1"
                        defaultValue=""
                      >
                        <option value="">选择在线项目</option>
                        {onlineProjects.map(online => (
                          <option key={online.id} value={online.id}>{online.name}</option>
                        ))}
                      </select>
                      <button
                        onClick={() => handleCreateOnlineProject(project)}
                        className="px-2 py-1 bg-blue-600 text-white text-xs rounded hover:bg-blue-700"
                      >
                        <Plus className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {filteredProjects.length === 0 && (
        <div className="text-center py-12">
          <Folder className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchTerm ? '未找到匹配的项目' : '暂无本地项目'}
          </h3>
          <p className="text-gray-500 mb-4">
            {searchTerm ? '尝试调整搜索条件' : '点击"重新扫描"来发现本地项目'}
          </p>
          {!searchTerm && (
            <button
              onClick={handleScan}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              开始扫描
            </button>
          )}
        </div>
      )}

      {/* 文档上传器 */}
      {showDocumentUploader && (
        <ProjectDocumentUploader
          onProjectLoaded={handleProjectFromDocument}
          onClose={() => setShowDocumentUploader(false)}
        />
      )}
    </div>
  );
};

export default LocalProjectManager;
