import React, { useState, useEffect } from 'react';
import { 
  RefreshCw, 
  Upload, 
  Download, 
  CheckCircle, 
  AlertCircle,
  Clock,
  Settings,
  Database,
  Cloud,
  HardDrive,
  ArrowUpDown,
  Zap,
  Shield
} from 'lucide-react';
import { MultiProjectService } from '../../services/MultiProjectService';
import { MultiProjectLocalScanner } from '../../services/MultiProjectLocalScanner';
import { Project, Workspace } from '../../types/MultiProject';

interface SyncStatus {
  projectId: string;
  projectName: string;
  localPath?: string;
  lastSync: string;
  status: 'synced' | 'pending' | 'conflict' | 'error';
  changes: {
    local: number;
    online: number;
  };
}

interface SyncConflict {
  projectId: string;
  projectName: string;
  type: 'data' | 'settings' | 'structure';
  description: string;
  localValue: any;
  onlineValue: any;
}

export const MultiProjectSyncManager: React.FC = () => {
  const [syncStatuses, setSyncStatuses] = useState<SyncStatus[]>([]);
  const [conflicts, setConflicts] = useState<SyncConflict[]>([]);
  const [isAutoSyncEnabled, setIsAutoSyncEnabled] = useState(true);
  const [isSyncing, setIsSyncing] = useState(false);
  const [lastGlobalSync, setLastGlobalSync] = useState<string | null>(null);
  const [syncHistory, setSyncHistory] = useState<any[]>([]);

  useEffect(() => {
    loadSyncData();
    if (isAutoSyncEnabled) {
      const interval = setInterval(performAutoSync, 30000); // 每30秒检查一次
      return () => clearInterval(interval);
    }
  }, [isAutoSyncEnabled]);

  const loadSyncData = () => {
    const projects = MultiProjectService.getAllProjects();
    const localProjects = MultiProjectLocalScanner.getLocalProjects();
    
    const statuses: SyncStatus[] = projects.map(project => {
      const linkedLocal = localProjects.find(local => local.linkedProjectId === project.id);
      
      return {
        projectId: project.id,
        projectName: project.name,
        localPath: linkedLocal?.path,
        lastSync: project.updatedAt,
        status: Math.random() > 0.8 ? 'pending' : 'synced',
        changes: {
          local: Math.floor(Math.random() * 5),
          online: Math.floor(Math.random() * 3)
        }
      };
    });

    setSyncStatuses(statuses);
    
    // 模拟冲突
    const mockConflicts: SyncConflict[] = statuses
      .filter(s => s.status === 'conflict')
      .map(s => ({
        projectId: s.projectId,
        projectName: s.projectName,
        type: 'settings',
        description: '项目设置不一致',
        localValue: { autoBackup: true },
        onlineValue: { autoBackup: false }
      }));
    
    setConflicts(mockConflicts);
    
    // 加载同步历史
    const history = JSON.parse(localStorage.getItem('dev-daily-v2-sync-history') || '[]');
    setSyncHistory(history);
    
    const lastSync = localStorage.getItem('dev-daily-v2-last-global-sync');
    setLastGlobalSync(lastSync);
  };

  const performAutoSync = async () => {
    if (isSyncing) return;
    
    const pendingProjects = syncStatuses.filter(s => s.status === 'pending');
    if (pendingProjects.length > 0) {
      await performSync(pendingProjects.map(p => p.projectId));
    }
  };

  const performSync = async (projectIds?: string[]) => {
    setIsSyncing(true);
    
    try {
      const targetProjects = projectIds || syncStatuses.map(s => s.projectId);
      
      for (const projectId of targetProjects) {
        await syncProject(projectId);
      }
      
      // 更新全局同步时间
      const now = new Date().toISOString();
      setLastGlobalSync(now);
      localStorage.setItem('dev-daily-v2-last-global-sync', now);
      
      // 记录同步历史
      const syncRecord = {
        timestamp: now,
        projectCount: targetProjects.length,
        type: projectIds ? 'manual' : 'auto',
        status: 'success'
      };
      
      const newHistory = [syncRecord, ...syncHistory.slice(0, 9)];
      setSyncHistory(newHistory);
      localStorage.setItem('dev-daily-v2-sync-history', JSON.stringify(newHistory));
      
      loadSyncData();
      
    } catch (error) {
      console.error('同步失败:', error);
    } finally {
      setIsSyncing(false);
    }
  };

  const syncProject = async (projectId: string): Promise<void> => {
    // 模拟同步过程
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    // 实际同步逻辑会在这里实现
    // 1. 比较本地和在线数据
    // 2. 检测冲突
    // 3. 应用更改
    // 4. 更新状态
    
    console.log(`项目 ${projectId} 同步完成`);
  };

  const resolveConflict = (conflict: SyncConflict, resolution: 'local' | 'online') => {
    // 解决冲突
    const resolvedValue = resolution === 'local' ? conflict.localValue : conflict.onlineValue;
    
    // 更新项目数据
    MultiProjectService.updateProject(conflict.projectId, {
      settings: resolvedValue
    });
    
    // 移除冲突
    setConflicts(prev => prev.filter(c => c.projectId !== conflict.projectId));
    
    console.log(`冲突已解决: ${conflict.projectName} - 使用${resolution === 'local' ? '本地' : '在线'}数据`);
  };

  const exportSyncData = () => {
    const data = {
      syncStatuses,
      conflicts,
      syncHistory,
      exportTime: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `sync-data-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'synced':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'conflict':
        return <AlertCircle className="w-4 h-4 text-red-600" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusText = (status: string) => {
    const statusMap = {
      'synced': '已同步',
      'pending': '待同步',
      'conflict': '有冲突',
      'error': '同步失败'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  const syncStats = {
    total: syncStatuses.length,
    synced: syncStatuses.filter(s => s.status === 'synced').length,
    pending: syncStatuses.filter(s => s.status === 'pending').length,
    conflicts: conflicts.length
  };

  return (
    <div className="p-6 bg-gray-50 min-h-full">
      {/* 头部 */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <ArrowUpDown className="w-6 h-6 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">多项目数据同步</h1>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={exportSyncData}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 flex items-center space-x-2"
            >
              <Download className="w-4 h-4" />
              <span>导出数据</span>
            </button>
            <button
              onClick={() => performSync()}
              disabled={isSyncing}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2 disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${isSyncing ? 'animate-spin' : ''}`} />
              <span>{isSyncing ? '同步中...' : '立即同步'}</span>
            </button>
          </div>
        </div>
        
        {/* 统计信息 */}
        <div className="grid grid-cols-4 gap-4 mt-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center space-x-2">
              <Database className="w-5 h-5 text-blue-600" />
              <span className="text-sm font-medium text-gray-600">总项目</span>
            </div>
            <div className="text-2xl font-bold text-gray-900 mt-1">{syncStats.total}</div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium text-gray-600">已同步</span>
            </div>
            <div className="text-2xl font-bold text-green-600 mt-1">{syncStats.synced}</div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center space-x-2">
              <Clock className="w-5 h-5 text-yellow-600" />
              <span className="text-sm font-medium text-gray-600">待同步</span>
            </div>
            <div className="text-2xl font-bold text-yellow-600 mt-1">{syncStats.pending}</div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <span className="text-sm font-medium text-gray-600">冲突</span>
            </div>
            <div className="text-2xl font-bold text-red-600 mt-1">{syncStats.conflicts}</div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 同步状态列表 */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-gray-900">项目同步状态</h3>
                <div className="flex items-center space-x-2">
                  <label className="flex items-center space-x-2 text-sm">
                    <input
                      type="checkbox"
                      checked={isAutoSyncEnabled}
                      onChange={(e) => setIsAutoSyncEnabled(e.target.checked)}
                      className="rounded border-gray-300"
                    />
                    <span>自动同步</span>
                  </label>
                </div>
              </div>
            </div>
            
            <div className="divide-y divide-gray-100">
              {syncStatuses.map((status) => (
                <div key={status.projectId} className="p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(status.status)}
                      <div>
                        <div className="font-medium text-gray-900">{status.projectName}</div>
                        <div className="text-sm text-gray-500">
                          {status.localPath ? `本地: ${status.localPath}` : '未关联本地项目'}
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900">
                        {getStatusText(status.status)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {new Date(status.lastSync).toLocaleString()}
                      </div>
                      {(status.changes.local > 0 || status.changes.online > 0) && (
                        <div className="text-xs text-blue-600 mt-1">
                          本地: {status.changes.local} | 在线: {status.changes.online}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 冲突解决 */}
          {conflicts.length > 0 && (
            <div className="bg-white rounded-lg border border-red-200 mt-6">
              <div className="p-4 border-b border-red-200 bg-red-50">
                <h3 className="font-semibold text-red-900">需要解决的冲突</h3>
              </div>
              
              <div className="divide-y divide-red-100">
                {conflicts.map((conflict, index) => (
                  <div key={index} className="p-4">
                    <div className="mb-3">
                      <div className="font-medium text-gray-900">{conflict.projectName}</div>
                      <div className="text-sm text-gray-600">{conflict.description}</div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="p-3 bg-blue-50 rounded border border-blue-200">
                        <div className="text-sm font-medium text-blue-900 mb-1">本地数据</div>
                        <pre className="text-xs text-blue-700">
                          {JSON.stringify(conflict.localValue, null, 2)}
                        </pre>
                      </div>
                      <div className="p-3 bg-green-50 rounded border border-green-200">
                        <div className="text-sm font-medium text-green-900 mb-1">在线数据</div>
                        <pre className="text-xs text-green-700">
                          {JSON.stringify(conflict.onlineValue, null, 2)}
                        </pre>
                      </div>
                    </div>
                    
                    <div className="flex space-x-3">
                      <button
                        onClick={() => resolveConflict(conflict, 'local')}
                        className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                      >
                        使用本地
                      </button>
                      <button
                        onClick={() => resolveConflict(conflict, 'online')}
                        className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700"
                      >
                        使用在线
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 侧边栏 */}
        <div className="space-y-6">
          {/* 同步设置 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="font-semibold text-gray-900 mb-4">同步设置</h3>
            <div className="space-y-3">
              <label className="flex items-center space-x-2 text-sm">
                <input type="checkbox" defaultChecked className="rounded border-gray-300" />
                <span>自动解决简单冲突</span>
              </label>
              <label className="flex items-center space-x-2 text-sm">
                <input type="checkbox" defaultChecked className="rounded border-gray-300" />
                <span>同步项目设置</span>
              </label>
              <label className="flex items-center space-x-2 text-sm">
                <input type="checkbox" defaultChecked className="rounded border-gray-300" />
                <span>同步备份数据</span>
              </label>
              <label className="flex items-center space-x-2 text-sm">
                <input type="checkbox" className="rounded border-gray-300" />
                <span>同步敏感信息</span>
              </label>
            </div>
          </div>

          {/* 同步历史 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="font-semibold text-gray-900 mb-4">同步历史</h3>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {syncHistory.map((record, index) => (
                <div key={index} className="p-2 bg-gray-50 rounded text-sm">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">
                      {record.type === 'auto' ? '自动同步' : '手动同步'}
                    </span>
                    <CheckCircle className="w-3 h-3 text-green-600" />
                  </div>
                  <div className="text-xs text-gray-600 mt-1">
                    {record.projectCount} 个项目 • {new Date(record.timestamp).toLocaleString()}
                  </div>
                </div>
              ))}
              {syncHistory.length === 0 && (
                <div className="text-center text-gray-500 py-4">
                  <Clock className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                  <p className="text-sm">暂无同步历史</p>
                </div>
              )}
            </div>
          </div>

          {/* 状态信息 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="font-semibold text-gray-900 mb-4">系统状态</h3>
            <div className="space-y-3 text-sm">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">上次全局同步</span>
                <span className="font-medium">
                  {lastGlobalSync ? new Date(lastGlobalSync).toLocaleTimeString() : '从未'}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">自动同步</span>
                <span className={`font-medium ${isAutoSyncEnabled ? 'text-green-600' : 'text-gray-400'}`}>
                  {isAutoSyncEnabled ? '已启用' : '已禁用'}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">同步状态</span>
                <span className={`font-medium ${isSyncing ? 'text-blue-600' : 'text-green-600'}`}>
                  {isSyncing ? '同步中' : '空闲'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MultiProjectSyncManager;
