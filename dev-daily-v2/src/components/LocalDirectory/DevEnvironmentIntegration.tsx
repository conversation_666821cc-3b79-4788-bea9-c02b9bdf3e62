import React, { useState, useEffect } from 'react';
import { 
  Code, 
  Terminal, 
  GitBranch, 
  Play, 
  ExternalLink,
  Settings,
  Package,
  FileText,
  Folder,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Clock
} from 'lucide-react';
import { LocalProjectInfo, MultiProjectLocalScanner } from '../../services/MultiProjectLocalScanner';
import { MultiProjectService } from '../../services/MultiProjectService';

interface DevCommand {
  id: string;
  name: string;
  command: string;
  description: string;
  icon: React.ReactNode;
  category: 'development' | 'build' | 'test' | 'deploy';
}

export const DevEnvironmentIntegration: React.FC = () => {
  const [selectedProject, setSelectedProject] = useState<LocalProjectInfo | null>(null);
  const [localProjects, setLocalProjects] = useState<LocalProjectInfo[]>([]);
  const [isExecuting, setIsExecuting] = useState<string | null>(null);
  const [executionHistory, setExecutionHistory] = useState<any[]>([]);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = () => {
    const projects = MultiProjectLocalScanner.getLocalProjects();
    setLocalProjects(projects);
    if (projects.length > 0 && !selectedProject) {
      setSelectedProject(projects[0]);
    }
  };

  const getDevCommands = (project: LocalProjectInfo): DevCommand[] => {
    const commands: DevCommand[] = [];

    // 基于项目脚本生成命令
    Object.entries(project.scripts).forEach(([name, command]) => {
      commands.push({
        id: `script-${name}`,
        name: name,
        command: `npm run ${name}`,
        description: `执行 ${command}`,
        icon: <Play className="w-4 h-4" />,
        category: name.includes('build') ? 'build' : 
                 name.includes('test') ? 'test' : 
                 name.includes('deploy') ? 'deploy' : 'development'
      });
    });

    // 通用开发命令
    const commonCommands: DevCommand[] = [
      {
        id: 'vscode-open',
        name: '在 VS Code 中打开',
        command: `code "${project.path}"`,
        description: '在 Visual Studio Code 中打开项目',
        icon: <Code className="w-4 h-4" />,
        category: 'development'
      },
      {
        id: 'terminal-open',
        name: '打开终端',
        command: `cd "${project.path}"`,
        description: '在项目目录打开终端',
        icon: <Terminal className="w-4 h-4" />,
        category: 'development'
      },
      {
        id: 'explorer-open',
        name: '打开文件夹',
        command: `explorer "${project.path}"`,
        description: '在文件资源管理器中打开',
        icon: <Folder className="w-4 h-4" />,
        category: 'development'
      }
    ];

    // Git 命令
    if (project.gitRepository) {
      commonCommands.push(
        {
          id: 'git-status',
          name: 'Git 状态',
          command: 'git status',
          description: '查看 Git 仓库状态',
          icon: <GitBranch className="w-4 h-4" />,
          category: 'development'
        },
        {
          id: 'git-pull',
          name: '拉取更新',
          command: 'git pull',
          description: '从远程仓库拉取最新代码',
          icon: <RefreshCw className="w-4 h-4" />,
          category: 'development'
        }
      );
    }

    // 包管理命令
    if (project.packageManager) {
      commonCommands.push({
        id: 'install-deps',
        name: '安装依赖',
        command: `${project.packageManager} install`,
        description: '安装项目依赖',
        icon: <Package className="w-4 h-4" />,
        category: 'development'
      });
    }

    return [...commonCommands, ...commands];
  };

  const executeCommand = async (command: DevCommand) => {
    if (!selectedProject) return;

    setIsExecuting(command.id);
    
    try {
      // 模拟命令执行
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
      
      const execution = {
        id: Date.now().toString(),
        projectName: selectedProject.name,
        commandName: command.name,
        command: command.command,
        timestamp: new Date().toISOString(),
        status: Math.random() > 0.1 ? 'success' : 'error',
        output: generateMockOutput(command)
      };

      setExecutionHistory(prev => [execution, ...prev.slice(0, 9)]);
      
      // 实际项目中这里会执行真实的命令
      if (command.id === 'vscode-open') {
        // window.open(`vscode://file/${selectedProject.path}`);
        console.log(`打开 VS Code: ${selectedProject.path}`);
      }
      
    } catch (error) {
      console.error('命令执行失败:', error);
    } finally {
      setIsExecuting(null);
    }
  };

  const generateMockOutput = (command: DevCommand): string => {
    switch (command.category) {
      case 'development':
        if (command.id === 'git-status') {
          return `On branch main\nYour branch is up to date with 'origin/main'.\n\nnothing to commit, working tree clean`;
        }
        if (command.id === 'vscode-open') {
          return `正在启动 Visual Studio Code...`;
        }
        return `${command.name} 执行成功`;
      
      case 'build':
        return `✓ Build completed successfully\n✓ Assets optimized\n✓ Output: dist/`;
      
      case 'test':
        return `✓ All tests passed (12/12)\n✓ Coverage: 85%\n✓ Duration: 2.3s`;
      
      default:
        return `${command.name} 执行完成`;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'development': return <Code className="w-4 h-4" />;
      case 'build': return <Package className="w-4 h-4" />;
      case 'test': return <CheckCircle className="w-4 h-4" />;
      case 'deploy': return <ExternalLink className="w-4 h-4" />;
      default: return <Settings className="w-4 h-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'development': return 'bg-blue-100 text-blue-700';
      case 'build': return 'bg-green-100 text-green-700';
      case 'test': return 'bg-yellow-100 text-yellow-700';
      case 'deploy': return 'bg-purple-100 text-purple-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const commands = selectedProject ? getDevCommands(selectedProject) : [];
  const groupedCommands = commands.reduce((groups, command) => {
    const category = command.category;
    if (!groups[category]) groups[category] = [];
    groups[category].push(command);
    return groups;
  }, {} as Record<string, DevCommand[]>);

  return (
    <div className="p-6 bg-gray-50 min-h-full">
      {/* 头部 */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Code className="w-6 h-6 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">开发环境集成</h1>
          </div>
        </div>
        <p className="text-gray-600 mt-2">
          快速访问开发工具和执行常用命令
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 项目选择 */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="font-semibold text-gray-900 mb-4">选择项目</h3>
            <div className="space-y-2">
              {localProjects.map((project) => (
                <button
                  key={project.path}
                  onClick={() => setSelectedProject(project)}
                  className={`w-full text-left p-3 rounded-lg border transition-colors ${
                    selectedProject?.path === project.path
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-xl">
                      {project.type === 'web' ? '🌐' : 
                       project.type === 'mobile' ? '📱' : 
                       project.type === 'api' ? '🔌' : '📁'}
                    </span>
                    <div>
                      <div className="font-medium text-gray-900">{project.name}</div>
                      <div className="text-sm text-gray-500">{project.framework.join(', ')}</div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* 执行历史 */}
          <div className="bg-white rounded-lg border border-gray-200 p-4 mt-6">
            <h3 className="font-semibold text-gray-900 mb-4">执行历史</h3>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {executionHistory.map((execution) => (
                <div key={execution.id} className="p-2 bg-gray-50 rounded text-sm">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{execution.commandName}</span>
                    <div className="flex items-center space-x-1">
                      {execution.status === 'success' ? (
                        <CheckCircle className="w-3 h-3 text-green-600" />
                      ) : (
                        <AlertCircle className="w-3 h-3 text-red-600" />
                      )}
                      <span className="text-xs text-gray-500">
                        {new Date(execution.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                  <div className="text-xs text-gray-600 mt-1">{execution.projectName}</div>
                </div>
              ))}
              {executionHistory.length === 0 && (
                <div className="text-center text-gray-500 py-4">
                  <Clock className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                  <p className="text-sm">暂无执行历史</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 命令面板 */}
        <div className="lg:col-span-2">
          {selectedProject ? (
            <div className="space-y-6">
              {/* 项目信息 */}
              <div className="bg-white rounded-lg border border-gray-200 p-4">
                <h3 className="font-semibold text-gray-900 mb-3">{selectedProject.name}</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">路径:</span>
                    <div className="font-mono text-xs bg-gray-100 p-1 rounded mt-1">
                      {selectedProject.path}
                    </div>
                  </div>
                  <div>
                    <span className="text-gray-600">技术栈:</span>
                    <div className="mt-1">
                      {selectedProject.framework.map((tech, index) => (
                        <span key={index} className="inline-block bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded mr-1">
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* 命令分组 */}
              {Object.entries(groupedCommands).map(([category, categoryCommands]) => (
                <div key={category} className="bg-white rounded-lg border border-gray-200 p-4">
                  <div className="flex items-center space-x-2 mb-4">
                    {getCategoryIcon(category)}
                    <h3 className="font-semibold text-gray-900 capitalize">
                      {category === 'development' ? '开发工具' :
                       category === 'build' ? '构建命令' :
                       category === 'test' ? '测试命令' :
                       category === 'deploy' ? '部署命令' : category}
                    </h3>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {categoryCommands.map((command) => (
                      <button
                        key={command.id}
                        onClick={() => executeCommand(command)}
                        disabled={isExecuting === command.id}
                        className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-left"
                      >
                        <div className={`p-2 rounded ${getCategoryColor(category)}`}>
                          {isExecuting === command.id ? (
                            <RefreshCw className="w-4 h-4 animate-spin" />
                          ) : (
                            command.icon
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">{command.name}</div>
                          <div className="text-sm text-gray-500">{command.description}</div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
              <Code className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">选择一个项目</h3>
              <p className="text-gray-500">从左侧选择一个本地项目来查看可用的开发命令</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DevEnvironmentIntegration;
