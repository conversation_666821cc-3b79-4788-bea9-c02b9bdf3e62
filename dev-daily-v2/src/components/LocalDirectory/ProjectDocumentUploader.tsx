import React, { useState, useCallback } from 'react';
import { Upload, FileText, Folder, Check<PERSON>ircle, AlertCircle, X } from 'lucide-react';
import { LocalProjectDocumentReader, ProjectDocument, ProjectInfo } from '../../services/LocalProjectDocumentReader';

interface ProjectDocumentUploaderProps {
  onProjectLoaded: (project: ProjectInfo) => void;
  onClose: () => void;
}

interface UploadedFile {
  name: string;
  content: string;
  type: 'project' | 'progress' | 'config' | 'tasks' | 'notes';
  status: 'uploaded' | 'error';
}

export const ProjectDocumentUploader: React.FC<ProjectDocumentUploaderProps> = ({
  onProjectLoaded,
  onClose
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [projectPath, setProjectPath] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    Array.from(files).forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        const fileName = file.name.toLowerCase();
        
        let fileType: UploadedFile['type'] = 'project';
        if (fileName.includes('progress')) fileType = 'progress';
        else if (fileName.includes('config')) fileType = 'config';
        else if (fileName.includes('tasks')) fileType = 'tasks';
        else if (fileName.includes('notes')) fileType = 'notes';

        const uploadedFile: UploadedFile = {
          name: file.name,
          content,
          type: fileType,
          status: 'uploaded'
        };

        setUploadedFiles(prev => {
          // 替换同类型的文件
          const filtered = prev.filter(f => f.type !== fileType);
          return [...filtered, uploadedFile];
        });
      };
      reader.readAsText(file);
    });
  }, []);

  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    const files = event.dataTransfer.files;
    
    // 模拟文件上传
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    input.accept = '.md,.json';
    
    // 将拖拽的文件转换为 FileList
    const fileArray = Array.from(files);
    fileArray.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        const fileName = file.name.toLowerCase();
        
        let fileType: UploadedFile['type'] = 'project';
        if (fileName.includes('progress')) fileType = 'progress';
        else if (fileName.includes('config')) fileType = 'config';
        else if (fileName.includes('tasks')) fileType = 'tasks';
        else if (fileName.includes('notes')) fileType = 'notes';

        const uploadedFile: UploadedFile = {
          name: file.name,
          content,
          type: fileType,
          status: 'uploaded'
        };

        setUploadedFiles(prev => {
          const filtered = prev.filter(f => f.type !== fileType);
          return [...filtered, uploadedFile];
        });
      };
      reader.readAsText(file);
    });
  }, []);

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
  }, []);

  const removeFile = (fileName: string) => {
    setUploadedFiles(prev => prev.filter(f => f.name !== fileName));
  };

  const processProject = async () => {
    if (!projectPath.trim()) {
      setError('请输入项目路径');
      return;
    }

    if (uploadedFiles.length === 0) {
      setError('请至少上传一个项目文档文件');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // 构建项目文档对象
      const documents: ProjectDocument = {
        project: uploadedFiles.find(f => f.type === 'project')?.content || '',
        progress: uploadedFiles.find(f => f.type === 'progress')?.content || '',
        config: uploadedFiles.find(f => f.type === 'config')?.content || '{}',
        tasks: uploadedFiles.find(f => f.type === 'tasks')?.content,
        notes: uploadedFiles.find(f => f.type === 'notes')?.content
      };

      // 解析 config.json
      try {
        documents.config = JSON.parse(documents.config);
      } catch (e) {
        documents.config = {};
      }

      // 解析项目信息
      const projectInfo = LocalProjectDocumentReader.parseProjectInfo(documents, projectPath);
      
      // 调用回调函数
      onProjectLoaded(projectInfo);
      
      // 关闭对话框
      onClose();

    } catch (error) {
      setError('处理项目文档时出错: ' + (error as Error).message);
    } finally {
      setIsProcessing(false);
    }
  };

  const getFileTypeIcon = (type: string) => {
    switch (type) {
      case 'project': return '📋';
      case 'progress': return '📈';
      case 'config': return '⚙️';
      case 'tasks': return '✅';
      case 'notes': return '📝';
      default: return '📄';
    }
  };

  const getFileTypeLabel = (type: string) => {
    switch (type) {
      case 'project': return '项目信息';
      case 'progress': return '开发进度';
      case 'config': return '项目配置';
      case 'tasks': return '任务列表';
      case 'notes': return '开发笔记';
      default: return '其他文档';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <Upload className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">通过文档加载项目</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* 内容 */}
        <div className="p-6 space-y-6">
          {/* 项目路径输入 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              项目路径
            </label>
            <div className="flex items-center space-x-2">
              <Folder className="w-5 h-5 text-gray-400" />
              <input
                type="text"
                value={projectPath}
                onChange={(e) => setProjectPath(e.target.value)}
                placeholder="C:\Users\<USER>\Desktop\VScode\cloudflare-user-analytics"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* 文件上传区域 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              上传项目文档
            </label>
            <div
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors"
            >
              <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 mb-2">
                拖拽文件到此处，或
                <label className="text-blue-600 hover:text-blue-700 cursor-pointer ml-1">
                  点击选择文件
                  <input
                    type="file"
                    multiple
                    accept=".md,.json"
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                </label>
              </p>
              <p className="text-sm text-gray-500">
                支持 .md 和 .json 文件，建议上传：project.md, progress.md, config.json
              </p>
            </div>
          </div>

          {/* 已上传文件列表 */}
          {uploadedFiles.length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-3">已上传文件</h3>
              <div className="space-y-2">
                {uploadedFiles.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <span className="text-lg">{getFileTypeIcon(file.type)}</span>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{file.name}</p>
                        <p className="text-xs text-gray-500">{getFileTypeLabel(file.type)}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <button
                        onClick={() => removeFile(file.name)}
                        className="text-gray-400 hover:text-red-500"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 错误信息 */}
          {error && (
            <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg">
              <AlertCircle className="w-5 h-5 text-red-500" />
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}
        </div>

        {/* 底部按钮 */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            取消
          </button>
          <button
            onClick={processProject}
            disabled={isProcessing || !projectPath.trim() || uploadedFiles.length === 0}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isProcessing && <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />}
            <span>{isProcessing ? '处理中...' : '加载项目'}</span>
          </button>
        </div>
      </div>
    </div>
  );
};
