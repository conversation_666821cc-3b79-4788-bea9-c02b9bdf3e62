import React, { useState, useEffect } from 'react';
import { 
  Brain, 
  MessageSquare, 
  Lightbulb, 
  Code, 
  FileText, 
  TrendingUp,
  Zap,
  Send,
  Sparkles,
  Target,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { Document, ProjectTreeNode, UserSettings } from '../../types/index';
import { TestDataService } from '../../services/TestDataService';
import { OpenAIService, AIAnalysis, ChatMessage } from '../../services/OpenAIService';
import clsx from 'clsx';

interface AIAssistantProps {
  documents: Document[];
  projectTree: ProjectTreeNode | null;
  className?: string;
}

// 从OpenAIService导入类型，这里不需要重复定义

// AI分析卡片组件
const AnalysisCard: React.FC<{ analysis: AIAnalysis }> = ({ analysis }) => {
  const getTypeIcon = (type: AIAnalysis['type']) => {
    switch (type) {
      case 'insight': return <Lightbulb className="h-5 w-5" />;
      case 'suggestion': return <Target className="h-5 w-5" />;
      case 'warning': return <AlertCircle className="h-5 w-5" />;
      case 'opportunity': return <TrendingUp className="h-5 w-5" />;
    }
  };

  const getTypeColor = (type: AIAnalysis['type']) => {
    switch (type) {
      case 'insight': return 'text-blue-600 bg-blue-100';
      case 'suggestion': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-red-600 bg-red-100';
      case 'opportunity': return 'text-purple-600 bg-purple-100';
    }
  };

  return (
    <div className="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
      <div className="flex items-start gap-3">
        <div className={clsx('p-2 rounded-lg', getTypeColor(analysis.type))}>
          {getTypeIcon(analysis.type)}
        </div>
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-medium text-gray-900">{analysis.title}</h4>
            <div className="flex items-center gap-2">
              <div className="text-xs text-gray-500">
                置信度: {Math.round(analysis.confidence * 100)}%
              </div>
              {analysis.actionable && (
                <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                  可执行
                </span>
              )}
            </div>
          </div>
          <p className="text-sm text-gray-600 mb-3">{analysis.description}</p>
          {analysis.relatedDocuments.length > 0 && (
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-gray-400" />
              <span className="text-xs text-gray-500">
                相关文档: {analysis.relatedDocuments.length} 个
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// 聊天界面组件
const ChatInterface: React.FC<{
  messages: ChatMessage[];
  onSendMessage: (message: string) => void;
}> = ({ messages, onSendMessage }) => {
  const [inputMessage, setInputMessage] = useState('');

  const handleSend = () => {
    if (inputMessage.trim()) {
      onSendMessage(inputMessage.trim());
      setInputMessage('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 flex flex-col h-96">
      <div className="p-4 border-b border-gray-200">
        <h3 className="font-medium text-gray-900 flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          AI对话助手
        </h3>
      </div>
      
      <div className="flex-1 overflow-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <Brain className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p>开始与AI助手对话，获取项目建议和帮助</p>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={clsx(
                'flex',
                message.type === 'user' ? 'justify-end' : 'justify-start'
              )}
            >
              <div
                className={clsx(
                  'max-w-xs lg:max-w-md px-4 py-2 rounded-lg',
                  message.type === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-900'
                )}
              >
                <p className="text-sm">{message.content}</p>
                <p className="text-xs mt-1 opacity-70">
                  {message.timestamp.toLocaleTimeString()}
                </p>
              </div>
            </div>
          ))
        )}
      </div>
      
      <div className="p-4 border-t border-gray-200">
        <div className="flex gap-2">
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="输入您的问题..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            onClick={handleSend}
            disabled={!inputMessage.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Send className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

// 主AI助手组件
export const AIAssistant: React.FC<AIAssistantProps> = ({
  documents,
  projectTree,
  className
}) => {
  const [analyses, setAnalyses] = useState<AIAnalysis[]>([]);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [openAIService, setOpenAIService] = useState<OpenAIService | null>(null);
  const [aiEnabled, setAiEnabled] = useState(false);

  // 初始化OpenAI服务
  useEffect(() => {
    const initializeAI = () => {
      try {
        const savedSettings = localStorage.getItem('dev-daily-settings');
        const savedApiKey = localStorage.getItem('dev-daily-api-key');

        if (savedSettings && savedApiKey) {
          const settings: UserSettings = JSON.parse(savedSettings);
          if (settings.ai.enabled && settings.ai.provider === 'openai') {
            const service = new OpenAIService({
              apiKey: savedApiKey,
              model: settings.ai.model,
              temperature: settings.ai.temperature,
              maxTokens: settings.ai.maxTokens
            });
            setOpenAIService(service);
            setAiEnabled(true);
          }
        }
      } catch (error) {
        console.error('Failed to initialize AI service:', error);
        setAiEnabled(false);
      }
    };

    initializeAI();

    // 监听设置变更
    const handleSettingsChange = (event: CustomEvent) => {
      initializeAI();
    };

    window.addEventListener('settings-saved', handleSettingsChange as EventListener);
    return () => {
      window.removeEventListener('settings-saved', handleSettingsChange as EventListener);
    };
  }, []);

  // 生成AI分析结果
  useEffect(() => {
    const generateAnalyses = async () => {
      if (!aiEnabled || !openAIService || documents.length === 0) {
        // 如果AI未启用，显示模拟数据
        const mockAnalyses: AIAnalysis[] = [
          {
            id: 'mock-1',
            type: 'insight',
            title: '项目进度洞察',
            description: '基于当前文档分析，项目整体进度良好，但测试覆盖率可能需要提升。建议增加单元测试文档。',
            confidence: 0.85,
            actionable: true,
            relatedDocuments: ['project-overview.md', 'test-plan.md']
          },
          {
            id: 'mock-2',
            type: 'suggestion',
            title: '代码质量建议',
            description: '检测到多个功能模块，建议创建统一的代码规范文档，提高代码一致性。',
            confidence: 0.92,
            actionable: true,
            relatedDocuments: ['coding-standards.md']
          },
          {
            id: 'mock-3',
            type: 'opportunity',
            title: '自动化机会',
            description: '发现重复性任务模式，可以考虑引入CI/CD流程来自动化部署和测试。',
            confidence: 0.78,
            actionable: true,
            relatedDocuments: ['deployment-guide.md']
          },
          {
            id: 'mock-4',
            type: 'warning',
            title: 'AI功能未启用',
            description: '当前使用模拟数据。请在设置中配置OpenAI API密钥以启用真实AI分析功能。',
            confidence: 1.0,
            actionable: true,
            relatedDocuments: []
          }
        ];
        setAnalyses(mockAnalyses);
        return;
      }

      setIsAnalyzing(true);
      try {
        let allAnalyses: AIAnalysis[] = [];

        // 分析项目树
        if (projectTree) {
          const projectAnalyses = await openAIService.analyzeProjectTree(projectTree, documents);
          allAnalyses = [...allAnalyses, ...projectAnalyses];
        }

        // 分析最重要的文档
        if (documents.length > 0) {
          const importantDoc = documents[0]; // 取第一个文档作为示例
          const docAnalyses = await openAIService.analyzeDocument(importantDoc);
          allAnalyses = [...allAnalyses, ...docAnalyses];
        }

        setAnalyses(allAnalyses.slice(0, 4)); // 限制显示4个分析结果
      } catch (error) {
        console.error('AI analysis failed:', error);
        // 失败时显示错误信息
        setAnalyses([{
          id: 'error-1',
          type: 'warning',
          title: 'AI分析失败',
          description: `AI分析服务暂时不可用: ${error}。请检查网络连接和API配置。`,
          confidence: 1.0,
          actionable: true,
          relatedDocuments: []
        }]);
      } finally {
        setIsAnalyzing(false);
      }
    };

    generateAnalyses();
  }, [documents, projectTree, aiEnabled, openAIService]);

  const handleSendMessage = async (message: string) => {
    const userMessage: ChatMessage = {
      id: `msg-${Date.now()}`,
      type: 'user',
      content: message,
      timestamp: new Date()
    };

    setChatMessages(prev => [...prev, userMessage]);

    if (!aiEnabled || !openAIService) {
      // 模拟AI回复
      setTimeout(() => {
        const aiResponse: ChatMessage = {
          id: `msg-${Date.now()}-ai`,
          type: 'assistant',
          content: `我理解您的问题："${message}"。当前使用模拟模式，请在设置中配置OpenAI API密钥以启用真实AI对话功能。`,
          timestamp: new Date()
        };
        setChatMessages(prev => [...prev, aiResponse]);
      }, 1000);
      return;
    }

    try {
      // 使用真实AI服务
      const allMessages = [...chatMessages, userMessage];
      const aiReply = await openAIService.chat(allMessages);

      const aiResponse: ChatMessage = {
        id: `msg-${Date.now()}-ai`,
        type: 'assistant',
        content: aiReply,
        timestamp: new Date()
      };
      setChatMessages(prev => [...prev, aiResponse]);
    } catch (error) {
      console.error('AI chat failed:', error);
      const errorResponse: ChatMessage = {
        id: `msg-${Date.now()}-error`,
        type: 'assistant',
        content: `抱歉，AI服务暂时不可用。错误信息: ${error}`,
        timestamp: new Date()
      };
      setChatMessages(prev => [...prev, errorResponse]);
    }
  };

  const stats = {
    totalAnalyses: analyses.length,
    actionableItems: analyses.filter(a => a.actionable).length,
    avgConfidence: analyses.length > 0 ? 
      Math.round(analyses.reduce((sum, a) => sum + a.confidence, 0) / analyses.length * 100) : 0
  };

  return (
    <div className={clsx('p-6 space-y-6 overflow-auto h-full', className)}>
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2 flex items-center gap-2">
          <Brain className="h-7 w-7 text-purple-600" />
          AI智能助手
        </h2>
        <p className="text-gray-600">基于项目数据的智能分析和建议</p>
      </div>

      {/* 统计概览 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center gap-3">
            <Sparkles className="h-6 w-6 text-purple-600" />
            <div>
              <p className="text-sm text-gray-600">AI分析</p>
              <p className="text-xl font-bold text-gray-900">{stats.totalAnalyses}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center gap-3">
            <Zap className="h-6 w-6 text-green-600" />
            <div>
              <p className="text-sm text-gray-600">可执行建议</p>
              <p className="text-xl font-bold text-gray-900">{stats.actionableItems}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="flex items-center gap-3">
            <CheckCircle className="h-6 w-6 text-blue-600" />
            <div>
              <p className="text-sm text-gray-600">平均置信度</p>
              <p className="text-xl font-bold text-gray-900">{stats.avgConfidence}%</p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* AI分析结果 */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">智能分析结果</h3>
          {isAnalyzing ? (
            <div className="bg-white p-8 rounded-lg border border-gray-200 text-center">
              <div className="animate-spin h-8 w-8 border-2 border-purple-500 border-t-transparent rounded-full mx-auto mb-4"></div>
              <p className="text-gray-600">AI正在分析您的项目数据...</p>
            </div>
          ) : (
            <div className="space-y-4">
              {analyses.map((analysis) => (
                <AnalysisCard key={analysis.id} analysis={analysis} />
              ))}
            </div>
          )}
        </div>

        {/* AI对话界面 */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">AI对话助手</h3>
          <ChatInterface
            messages={chatMessages}
            onSendMessage={handleSendMessage}
          />
        </div>
      </div>
    </div>
  );
};
