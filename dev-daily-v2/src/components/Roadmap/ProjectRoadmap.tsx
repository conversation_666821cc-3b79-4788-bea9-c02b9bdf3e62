import React, { useState, useMemo } from 'react';
import {
  Calendar,
  Clock,
  Target,
  CheckCircle,
  Circle,
  AlertCircle,
  Flag,
  ArrowRight,
  Zap,
  Users,
  TrendingUp
} from 'lucide-react';
import { format, addDays, differenceInDays, parseISO } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { ProjectTreeNode } from '../../types/index';
import { GanttChart } from './GanttChart';

interface RoadmapItem {
  id: string;
  name: string;
  description: string;
  type: 'module' | 'feature' | 'task' | 'milestone';
  status: 'completed' | 'active' | 'planned' | 'blocked';
  priority: 'high' | 'medium' | 'low';
  progress: number;
  startDate?: string;
  endDate?: string;
  estimatedHours?: number;
  actualHours?: number;
  dependencies?: string[];
  children?: RoadmapItem[];
  moduleId?: string; // A, B, C, D
}

interface ProjectRoadmapProps {
  projectTree: ProjectTreeNode;
  className?: string;
}

export const ProjectRoadmap: React.FC<ProjectRoadmapProps> = ({
  projectTree,
  className = ''
}) => {
  const [viewMode, setViewMode] = useState<'timeline' | 'gantt' | 'milestone'>('timeline');
  const [selectedModule, setSelectedModule] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'month' | 'quarter' | 'year'>('quarter');

  // 将ProjectTree转换为Roadmap数据
  const roadmapData = useMemo(() => {
    const convertTreeToRoadmap = (node: ProjectTreeNode, moduleId?: string): RoadmapItem => {
      const item: RoadmapItem = {
        id: node.id,
        name: node.name,
        description: node.description || '',
        type: node.type as any,
        status: node.status as any,
        priority: node.priority as any,
        progress: node.progress || 0,
        estimatedHours: node.metadata?.estimatedHours,
        actualHours: node.metadata?.actualHours,
        moduleId: moduleId || (node.id.match(/^[ABCD]$/) ? node.id : undefined)
      };

      // 根据模块和节点类型估算时间
      if (node.metadata?.createdAt) {
        item.startDate = node.metadata.createdAt;
        if (node.metadata.estimatedHours) {
          const startDate = parseISO(node.metadata.createdAt);
          const endDate = addDays(startDate, Math.ceil(node.metadata.estimatedHours / 8));
          item.endDate = endDate.toISOString();
        }
      }

      if (node.children) {
        item.children = node.children.map(child => 
          convertTreeToRoadmap(child, item.moduleId || moduleId)
        );
      }

      return item;
    };

    return convertTreeToRoadmap(projectTree);
  }, [projectTree]);

  // 获取模块数据
  const modules = useMemo(() => {
    return roadmapData.children?.filter(item => item.type === 'module') || [];
  }, [roadmapData]);

  // 获取里程碑
  const milestones = useMemo(() => {
    const extractMilestones = (items: RoadmapItem[]): RoadmapItem[] => {
      const milestones: RoadmapItem[] = [];
      
      for (const item of items) {
        // 模块完成是里程碑
        if (item.type === 'module' && item.status === 'completed') {
          milestones.push({
            ...item,
            type: 'milestone',
            name: `${item.name} 完成`
          });
        }
        
        // 重要功能完成是里程碑
        if (item.type === 'feature' && item.status === 'completed' && item.priority === 'high') {
          milestones.push({
            ...item,
            type: 'milestone',
            name: `${item.name} 完成`
          });
        }

        if (item.children) {
          milestones.push(...extractMilestones(item.children));
        }
      }
      
      return milestones;
    };

    return extractMilestones([roadmapData]);
  }, [roadmapData]);

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500';
      case 'active':
        return 'bg-blue-500';
      case 'planned':
        return 'bg-gray-400';
      case 'blocked':
        return 'bg-red-500';
      default:
        return 'bg-gray-400';
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'active':
        return <Zap className="w-4 h-4 text-blue-600" />;
      case 'planned':
        return <Circle className="w-4 h-4 text-gray-500" />;
      case 'blocked':
        return <AlertCircle className="w-4 h-4 text-red-600" />;
      default:
        return <Circle className="w-4 h-4 text-gray-500" />;
    }
  };

  // 获取模块图标
  const getModuleIcon = (moduleId: string) => {
    switch (moduleId) {
      case 'A':
        return '🌍';
      case 'B':
        return '⚡';
      case 'C':
        return '🎨';
      case 'D':
        return '📊';
      default:
        return '📋';
    }
  };

  // 渲染时间线视图
  const renderTimelineView = () => (
    <div className="space-y-8">
      {modules.map((module, index) => (
        <div key={module.id} className="relative">
          {/* 时间线连接线 */}
          {index < modules.length - 1 && (
            <div className="absolute left-6 top-16 w-0.5 h-full bg-gray-300 -z-10"></div>
          )}
          
          {/* 模块标题 */}
          <div className="flex items-center space-x-4 mb-4">
            <div className={`w-12 h-12 rounded-full ${getStatusColor(module.status)} flex items-center justify-center text-white text-xl`}>
              {getModuleIcon(module.id)}
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900">{module.name}</h3>
              <p className="text-sm text-gray-600">{module.description}</p>
              <div className="flex items-center space-x-4 mt-1">
                <span className="text-xs text-gray-500">
                  进度: {module.progress}%
                </span>
                {module.estimatedHours && (
                  <span className="text-xs text-gray-500">
                    预估: {module.estimatedHours}h
                  </span>
                )}
                {module.actualHours && (
                  <span className="text-xs text-gray-500">
                    实际: {module.actualHours}h
                  </span>
                )}
              </div>
            </div>
            <div className="text-right">
              {getStatusIcon(module.status)}
            </div>
          </div>

          {/* 进度条 */}
          <div className="ml-16 mb-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full ${getStatusColor(module.status)}`}
                style={{ width: `${module.progress}%` }}
              ></div>
            </div>
          </div>

          {/* 子任务 */}
          {module.children && (
            <div className="ml-16 space-y-2">
              {module.children.map((child) => (
                <div key={child.id} className="flex items-center space-x-3 p-2 bg-gray-50 rounded-lg">
                  {getStatusIcon(child.status)}
                  <span className="flex-1 text-sm">{child.name}</span>
                  <span className="text-xs text-gray-500">{child.progress}%</span>
                  <div className="w-16 bg-gray-200 rounded-full h-1">
                    <div
                      className={`h-1 rounded-full ${getStatusColor(child.status)}`}
                      style={{ width: `${child.progress}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      ))}
    </div>
  );

  // 渲染里程碑视图
  const renderMilestoneView = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {milestones.map((milestone) => (
          <div key={milestone.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
            <div className="flex items-center space-x-3 mb-3">
              <Flag className="w-5 h-5 text-orange-600" />
              <h4 className="font-medium text-gray-900">{milestone.name}</h4>
            </div>
            <p className="text-sm text-gray-600 mb-3">{milestone.description}</p>
            <div className="flex items-center justify-between">
              <span className={`px-2 py-1 rounded-full text-xs ${
                milestone.status === 'completed' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-blue-100 text-blue-800'
              }`}>
                {milestone.status === 'completed' ? '已完成' : '进行中'}
              </span>
              {milestone.endDate && (
                <span className="text-xs text-gray-500">
                  {format(parseISO(milestone.endDate), 'MM/dd', { locale: zhCN })}
                </span>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  // 准备甘特图数据
  const ganttTasks = useMemo(() => {
    const flattenTasks = (items: RoadmapItem[], level = 0, result: any[] = []) => {
      items.forEach(item => {
        if (item.startDate && item.endDate) {
          result.push({
            id: item.id,
            name: item.name,
            startDate: parseISO(item.startDate),
            endDate: parseISO(item.endDate),
            progress: item.progress,
            status: item.status,
            dependencies: item.dependencies,
            moduleId: item.moduleId,
            level
          });
        }

        if (item.children) {
          flattenTasks(item.children, level + 1, result);
        }
      });

      return result;
    };

    // 如果没有日期数据，生成模拟数据
    const processedData = roadmapData.children?.map(module => {
      if (!module.startDate) {
        const now = new Date();
        module.startDate = new Date(now.getFullYear(), now.getMonth() - 2, 1).toISOString();

        if (module.children) {
          let currentDate = parseISO(module.startDate);
          module.children.forEach(child => {
            child.startDate = currentDate.toISOString();
            const duration = child.estimatedHours ? Math.ceil(child.estimatedHours / 8) : 7;
            currentDate = addDays(currentDate, duration);
            child.endDate = currentDate.toISOString();
            currentDate = addDays(currentDate, 2); // 间隔
          });
        }

        module.endDate = module.children?.length
          ? module.children[module.children.length - 1].endDate
          : addDays(parseISO(module.startDate), 30).toISOString();
      }

      return module;
    }) || [];

    return flattenTasks(processedData);
  }, [roadmapData]);

  // 渲染甘特图视图
  const renderGanttView = () => (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      {ganttTasks.length > 0 ? (
        <GanttChart tasks={ganttTasks} />
      ) : (
        <div className="text-center text-gray-500 py-8">
          <TrendingUp className="w-12 h-12 mx-auto mb-3 text-gray-400" />
          <p>暂无甘特图数据</p>
          <p className="text-sm">请确保项目树中包含时间信息</p>
        </div>
      )}
    </div>
  );

  return (
    <div className={`bg-gray-50 min-h-full ${className}`}>
      {/* 头部 */}
      <div className="bg-white border-b border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Target className="w-6 h-6 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">项目路线图</h1>
          </div>
          <div className="flex items-center space-x-4">
            {/* 视图切换 */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              {[
                { id: 'timeline', name: '时间线', icon: Clock },
                { id: 'milestone', name: '里程碑', icon: Flag },
                { id: 'gantt', name: '甘特图', icon: Calendar }
              ].map(({ id, name, icon: Icon }) => (
                <button
                  key={id}
                  onClick={() => setViewMode(id as any)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm transition-colors ${
                    viewMode === id
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{name}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-4 gap-4 mt-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-sm font-medium text-blue-900">总模块</span>
            </div>
            <div className="text-2xl font-bold text-blue-900 mt-1">{modules.length}</div>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm font-medium text-green-900">已完成</span>
            </div>
            <div className="text-2xl font-bold text-green-900 mt-1">
              {modules.filter(m => m.status === 'completed').length}
            </div>
          </div>
          <div className="bg-orange-50 p-4 rounded-lg">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
              <span className="text-sm font-medium text-orange-900">里程碑</span>
            </div>
            <div className="text-2xl font-bold text-orange-900 mt-1">{milestones.length}</div>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span className="text-sm font-medium text-purple-900">整体进度</span>
            </div>
            <div className="text-2xl font-bold text-purple-900 mt-1">{roadmapData.progress}%</div>
          </div>
        </div>
      </div>

      {/* 主内容 */}
      <div className="p-6">
        {viewMode === 'timeline' && renderTimelineView()}
        {viewMode === 'milestone' && renderMilestoneView()}
        {viewMode === 'gantt' && renderGanttView()}
      </div>
    </div>
  );
};

export default ProjectRoadmap;
