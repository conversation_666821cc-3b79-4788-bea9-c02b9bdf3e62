import React, { useMemo } from 'react';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, addMonths, parseISO, isWithinInterval } from 'date-fns';
import { zhCN } from 'date-fns/locale';

interface GanttTask {
  id: string;
  name: string;
  startDate: Date;
  endDate: Date;
  progress: number;
  status: 'completed' | 'active' | 'planned' | 'blocked';
  dependencies?: string[];
  moduleId?: string;
  level: number;
}

interface GanttChartProps {
  tasks: GanttTask[];
  startDate?: Date;
  endDate?: Date;
  className?: string;
}

export const GanttChart: React.FC<GanttChartProps> = ({
  tasks,
  startDate,
  endDate,
  className = ''
}) => {
  // 计算时间范围
  const timeRange = useMemo(() => {
    const start = startDate || new Date(Math.min(...tasks.map(t => t.startDate.getTime())));
    const end = endDate || new Date(Math.max(...tasks.map(t => t.endDate.getTime())));
    
    const rangeStart = startOfMonth(start);
    const rangeEnd = endOfMonth(end);
    
    return {
      start: rangeStart,
      end: rangeEnd,
      days: eachDayOfInterval({ start: rangeStart, end: rangeEnd })
    };
  }, [tasks, startDate, endDate]);

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500';
      case 'active':
        return 'bg-blue-500';
      case 'planned':
        return 'bg-gray-400';
      case 'blocked':
        return 'bg-red-500';
      default:
        return 'bg-gray-400';
    }
  };

  // 计算任务在时间轴上的位置和宽度
  const getTaskPosition = (task: GanttTask) => {
    const totalDays = timeRange.days.length;
    const taskStart = Math.max(0, timeRange.days.findIndex(day => 
      day.getTime() >= task.startDate.getTime()
    ));
    const taskEnd = Math.min(totalDays - 1, timeRange.days.findIndex(day => 
      day.getTime() >= task.endDate.getTime()
    ));
    
    const left = (taskStart / totalDays) * 100;
    const width = Math.max(1, ((taskEnd - taskStart + 1) / totalDays) * 100);
    
    return { left, width };
  };

  // 生成月份标题
  const monthHeaders = useMemo(() => {
    const months: { name: string; width: number; start: number }[] = [];
    let currentMonth = startOfMonth(timeRange.start);
    let dayIndex = 0;
    
    while (currentMonth <= timeRange.end) {
      const monthEnd = endOfMonth(currentMonth);
      const monthDays = eachDayOfInterval({
        start: currentMonth > timeRange.start ? currentMonth : timeRange.start,
        end: monthEnd < timeRange.end ? monthEnd : timeRange.end
      });
      
      months.push({
        name: format(currentMonth, 'yyyy年MM月', { locale: zhCN }),
        width: (monthDays.length / timeRange.days.length) * 100,
        start: (dayIndex / timeRange.days.length) * 100
      });
      
      dayIndex += monthDays.length;
      currentMonth = addMonths(currentMonth, 1);
    }
    
    return months;
  }, [timeRange]);

  return (
    <div className={`bg-white border border-gray-200 rounded-lg overflow-hidden ${className}`}>
      {/* 时间轴头部 */}
      <div className="border-b border-gray-200">
        {/* 月份行 */}
        <div className="flex bg-gray-50 border-b border-gray-200">
          <div className="w-64 p-3 border-r border-gray-200 font-medium text-gray-900">
            任务名称
          </div>
          <div className="flex-1 relative">
            {monthHeaders.map((month, index) => (
              <div
                key={index}
                className="absolute top-0 h-full flex items-center justify-center border-r border-gray-200 text-sm font-medium text-gray-700"
                style={{
                  left: `${month.start}%`,
                  width: `${month.width}%`
                }}
              >
                {month.name}
              </div>
            ))}
          </div>
        </div>
        
        {/* 日期行 */}
        <div className="flex bg-gray-25">
          <div className="w-64 p-2 border-r border-gray-200"></div>
          <div className="flex-1 flex">
            {timeRange.days.map((day, index) => (
              <div
                key={index}
                className="flex-1 min-w-0 p-1 text-center text-xs text-gray-500 border-r border-gray-100"
                style={{ width: `${100 / timeRange.days.length}%` }}
              >
                {format(day, 'd')}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 任务列表 */}
      <div className="max-h-96 overflow-y-auto">
        {tasks.map((task, index) => {
          const position = getTaskPosition(task);
          
          return (
            <div key={task.id} className="flex border-b border-gray-100 hover:bg-gray-50">
              {/* 任务名称 */}
              <div className="w-64 p-3 border-r border-gray-200">
                <div 
                  className="flex items-center space-x-2"
                  style={{ paddingLeft: `${task.level * 16}px` }}
                >
                  <div className={`w-3 h-3 rounded-full ${getStatusColor(task.status)}`}></div>
                  <span className="text-sm font-medium text-gray-900 truncate">
                    {task.name}
                  </span>
                </div>
                <div className="mt-1 text-xs text-gray-500">
                  {format(task.startDate, 'MM/dd')} - {format(task.endDate, 'MM/dd')}
                </div>
              </div>
              
              {/* 甘特条 */}
              <div className="flex-1 relative p-2">
                <div className="relative h-6 bg-gray-100 rounded">
                  {/* 任务条 */}
                  <div
                    className={`absolute top-0 h-full rounded ${getStatusColor(task.status)} opacity-80`}
                    style={{
                      left: `${position.left}%`,
                      width: `${position.width}%`
                    }}
                  >
                    {/* 进度条 */}
                    <div
                      className="h-full bg-white bg-opacity-30 rounded"
                      style={{ width: `${task.progress}%` }}
                    ></div>
                  </div>
                  
                  {/* 任务标签 */}
                  {position.width > 10 && (
                    <div
                      className="absolute top-0 h-full flex items-center px-2 text-xs text-white font-medium"
                      style={{
                        left: `${position.left}%`,
                        width: `${position.width}%`
                      }}
                    >
                      <span className="truncate">{task.progress}%</span>
                    </div>
                  )}
                </div>
                
                {/* 依赖关系线 */}
                {task.dependencies && task.dependencies.map(depId => {
                  const depTask = tasks.find(t => t.id === depId);
                  if (!depTask) return null;
                  
                  const depPosition = getTaskPosition(depTask);
                  const currentTaskIndex = tasks.findIndex(t => t.id === task.id);
                  const depTaskIndex = tasks.findIndex(t => t.id === depId);
                  
                  if (depTaskIndex < currentTaskIndex) {
                    return (
                      <svg
                        key={depId}
                        className="absolute top-0 left-0 w-full h-full pointer-events-none"
                        style={{ zIndex: 1 }}
                      >
                        <line
                          x1={`${depPosition.left + depPosition.width}%`}
                          y1="12"
                          x2={`${position.left}%`}
                          y2="12"
                          stroke="#6B7280"
                          strokeWidth="1"
                          strokeDasharray="2,2"
                        />
                        <polygon
                          points={`${position.left - 0.5},8 ${position.left + 0.5},12 ${position.left - 0.5},16`}
                          fill="#6B7280"
                        />
                      </svg>
                    );
                  }
                  return null;
                })}
              </div>
            </div>
          );
        })}
      </div>

      {/* 图例 */}
      <div className="border-t border-gray-200 p-4 bg-gray-50">
        <div className="flex items-center space-x-6 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded"></div>
            <span className="text-gray-600">已完成</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-500 rounded"></div>
            <span className="text-gray-600">进行中</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-gray-400 rounded"></div>
            <span className="text-gray-600">计划中</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded"></div>
            <span className="text-gray-600">阻塞</span>
          </div>
          <div className="flex items-center space-x-2">
            <svg width="16" height="2">
              <line x1="0" y1="1" x2="16" y2="1" stroke="#6B7280" strokeWidth="1" strokeDasharray="2,2" />
            </svg>
            <span className="text-gray-600">依赖关系</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GanttChart;
