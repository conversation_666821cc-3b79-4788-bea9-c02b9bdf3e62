import React, { useState, useEffect } from 'react';
import {
  GitBranch,
  FileText,
  Clock,
  Activity,
  RefreshCw as Sync,
  Settings,
  Play,
  Pause,
  Download,
  Upload,
  AlertCircle,
  CheckCircle,
  TrendingUp
} from 'lucide-react';
import { GitIntegrationService } from '../../services/GitIntegrationService';
import { FileWatcherService } from '../../services/FileWatcherService';

export const DevSyncDashboard: React.FC = () => {
  const [isGitSyncEnabled, setIsGitSyncEnabled] = useState(true);
  const [isFileWatchEnabled, setIsFileWatchEnabled] = useState(false);
  const [gitStats, setGitStats] = useState<any>(null);
  const [fileStats, setFileStats] = useState<any>(null);
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'error'>('idle');

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = () => {
    setGitStats(GitIntegrationService.getGitStats(7));
    setFileStats(FileWatcherService.getFileActivityStats(7));
  };

  const handleGitSync = async () => {
    setSyncStatus('syncing');
    try {
      // 模拟Git同步
      await GitIntegrationService.simulateGitCommit({
        message: 'feat(B1): 完成项目树拖拽功能 [B1]',
        author: 'Developer',
        files: ['src/components/ProjectTree/DragDrop.tsx', 'src/hooks/useDragDrop.ts'],
        additions: 150,
        deletions: 20
      });
      
      loadStats();
      setSyncStatus('idle');
    } catch (error) {
      setSyncStatus('error');
    }
  };

  const toggleFileWatcher = () => {
    if (isFileWatchEnabled) {
      FileWatcherService.stopWatching();
    } else {
      FileWatcherService.startWatching();
    }
    setIsFileWatchEnabled(!isFileWatchEnabled);
  };

  const handleManualSync = () => {
    // 手动同步逻辑
    console.log('执行手动同步...');
  };

  return (
    <div className="p-6 bg-gray-50 min-h-full">
      {/* 头部 */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Sync className="w-6 h-6 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">开发同步中心</h1>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleManualSync}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2"
            >
              <Sync className="w-4 h-4" />
              <span>手动同步</span>
            </button>
          </div>
        </div>
        <p className="text-gray-600 mt-2">
          连接本地开发与项目管理，实现自动化进度同步
        </p>
      </div>

      {/* 同步状态卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <GitBranch className="w-5 h-5 text-green-600" />
              <h3 className="font-semibold text-gray-900">Git 集成</h3>
            </div>
            <div className={`w-3 h-3 rounded-full ${isGitSyncEnabled ? 'bg-green-500' : 'bg-gray-400'}`}></div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">最近7天提交</span>
              <span className="font-medium">{gitStats?.totalCommits || 0}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">代码变更</span>
              <span className="font-medium">+{gitStats?.totalAdditions || 0} -{gitStats?.totalDeletions || 0}</span>
            </div>
            <button
              onClick={handleGitSync}
              disabled={syncStatus === 'syncing'}
              className="w-full mt-3 px-3 py-2 bg-green-100 text-green-700 rounded hover:bg-green-200 disabled:opacity-50"
            >
              {syncStatus === 'syncing' ? '同步中...' : '模拟Git提交'}
            </button>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <FileText className="w-5 h-5 text-blue-600" />
              <h3 className="font-semibold text-gray-900">文件监听</h3>
            </div>
            <button
              onClick={toggleFileWatcher}
              className={`w-3 h-3 rounded-full ${isFileWatchEnabled ? 'bg-blue-500' : 'bg-gray-400'}`}
            ></button>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">文件变化</span>
              <span className="font-medium">{fileStats?.totalChanges || 0}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">活跃文件</span>
              <span className="font-medium">{Object.keys(fileStats?.mostActiveFiles || {}).length}</span>
            </div>
            <button
              onClick={toggleFileWatcher}
              className={`w-full mt-3 px-3 py-2 rounded ${
                isFileWatchEnabled 
                  ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                  : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
              }`}
            >
              {isFileWatchEnabled ? (
                <><Pause className="w-4 h-4 inline mr-1" />停止监听</>
              ) : (
                <><Play className="w-4 h-4 inline mr-1" />开始监听</>
              )}
            </button>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <Activity className="w-5 h-5 text-purple-600" />
              <h3 className="font-semibold text-gray-900">同步状态</h3>
            </div>
            <div className="flex items-center space-x-1">
              {syncStatus === 'idle' && <CheckCircle className="w-4 h-4 text-green-500" />}
              {syncStatus === 'syncing' && <Clock className="w-4 h-4 text-blue-500 animate-spin" />}
              {syncStatus === 'error' && <AlertCircle className="w-4 h-4 text-red-500" />}
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">上次同步</span>
              <span className="font-medium">刚刚</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">同步项目</span>
              <span className="font-medium">dev-daily v2</span>
            </div>
            <div className="w-full mt-3 px-3 py-2 bg-purple-100 text-purple-700 rounded text-center text-sm">
              {syncStatus === 'idle' && '同步正常'}
              {syncStatus === 'syncing' && '正在同步...'}
              {syncStatus === 'error' && '同步异常'}
            </div>
          </div>
        </div>
      </div>

      {/* 详细统计 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Git 活动图表 */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Git 提交活动</h3>
          {gitStats?.commitFrequency ? (
            <div className="space-y-3">
              {Object.entries(gitStats.commitFrequency).map(([date, count]) => (
                <div key={date} className="flex items-center space-x-3">
                  <span className="text-sm text-gray-600 w-20">{date.slice(5)}</span>
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-500 h-2 rounded-full"
                      style={{ width: `${Math.min(100, (count as number) * 20)}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium w-8">{count as number}</span>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center text-gray-500 py-8">
              <GitBranch className="w-12 h-12 mx-auto mb-3 text-gray-300" />
              <p>暂无Git提交数据</p>
            </div>
          )}
        </div>

        {/* 文件活动统计 */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">文件活动统计</h3>
          {fileStats?.fileTypes ? (
            <div className="space-y-3">
              {Object.entries(fileStats.fileTypes).slice(0, 5).map(([type, count]) => (
                <div key={type} className="flex items-center space-x-3">
                  <span className="text-sm text-gray-600 w-16">.{type}</span>
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full"
                      style={{ width: `${Math.min(100, (count as number) * 10)}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium w-8">{count as number}</span>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center text-gray-500 py-8">
              <FileText className="w-12 h-12 mx-auto mb-3 text-gray-300" />
              <p>暂无文件活动数据</p>
            </div>
          )}
        </div>
      </div>

      {/* 同步配置 */}
      <div className="mt-6 bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div className="flex items-center space-x-2 mb-4">
          <Settings className="w-5 h-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-900">同步配置</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Git 集成设置</h4>
            <label className="flex items-center space-x-2">
              <input 
                type="checkbox" 
                checked={isGitSyncEnabled}
                onChange={(e) => setIsGitSyncEnabled(e.target.checked)}
                className="rounded border-gray-300"
              />
              <span className="text-sm text-gray-700">启用Git自动同步</span>
            </label>
            <label className="flex items-center space-x-2">
              <input type="checkbox" defaultChecked className="rounded border-gray-300" />
              <span className="text-sm text-gray-700">提交时自动备份</span>
            </label>
            <label className="flex items-center space-x-2">
              <input type="checkbox" defaultChecked className="rounded border-gray-300" />
              <span className="text-sm text-gray-700">识别节点标识 [A1], [B2]</span>
            </label>
          </div>
          
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">文件监听设置</h4>
            <label className="flex items-center space-x-2">
              <input 
                type="checkbox" 
                checked={isFileWatchEnabled}
                onChange={(e) => setIsFileWatchEnabled(e.target.checked)}
                className="rounded border-gray-300"
              />
              <span className="text-sm text-gray-700">启用文件变化监听</span>
            </label>
            <label className="flex items-center space-x-2">
              <input type="checkbox" defaultChecked className="rounded border-gray-300" />
              <span className="text-sm text-gray-700">自动记录工时</span>
            </label>
            <label className="flex items-center space-x-2">
              <input type="checkbox" className="rounded border-gray-300" />
              <span className="text-sm text-gray-700">忽略临时文件</span>
            </label>
          </div>
        </div>

        <div className="mt-6 flex space-x-3">
          <button 
            onClick={() => GitIntegrationService.exportGitData()}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 flex items-center space-x-2"
          >
            <Download className="w-4 h-4" />
            <span>导出同步数据</span>
          </button>
          <button className="px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 flex items-center space-x-2">
            <Upload className="w-4 h-4" />
            <span>导入配置</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default DevSyncDashboard;
