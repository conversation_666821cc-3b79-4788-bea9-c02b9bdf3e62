import React, { useState, useEffect } from 'react';
import { 
  Shield, 
  Download, 
  Upload, 
  Trash2, 
  RefreshCw,
  Clock,
  HardDrive,
  CheckCircle,
  AlertTriangle,
  Settings,
  Plus
} from 'lucide-react';
import { BackupService, BackupData, BackupStrategy } from '../../services/BackupService';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

export interface BackupManagerProps {
  onRestore?: (backupData: any) => void;
}

export const BackupManager: React.FC<BackupManagerProps> = ({ onRestore }) => {
  const [backups, setBackups] = useState<BackupData[]>([]);
  const [strategy, setStrategy] = useState<BackupStrategy>(BackupService.getBackupStrategy());
  const [loading, setLoading] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [selectedBackup, setSelectedBackup] = useState<string | null>(null);

  useEffect(() => {
    loadBackups();
  }, []);

  const loadBackups = async () => {
    setLoading(true);
    try {
      const allBackups = await BackupService.getAllBackups();
      setBackups(allBackups);
    } catch (error) {
      console.error('Failed to load backups:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateManualBackup = async () => {
    setLoading(true);
    try {
      // 这里需要从应用状态获取当前数据
      // 暂时使用模拟数据
      const mockProjectTree = { id: 'root', name: 'Mock Project', children: [] } as any;
      const mockDocuments = [] as any[];
      const mockSettings = {} as any;

      await BackupService.createBackup(mockProjectTree, mockDocuments, mockSettings, {
        description: `手动备份 - ${new Date().toLocaleString()}`,
        triggerType: 'manual'
      });
      
      await loadBackups();
    } catch (error) {
      console.error('Failed to create backup:', error);
      alert('创建备份失败: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  const handleRestore = async (backupId: string) => {
    if (!window.confirm('确定要恢复此备份吗？当前数据将被覆盖。')) {
      return;
    }

    setLoading(true);
    try {
      const restoredData = await BackupService.restoreBackup(backupId);
      if (restoredData && onRestore) {
        onRestore(restoredData);
        alert('备份恢复成功！');
      }
    } catch (error) {
      console.error('Failed to restore backup:', error);
      alert('恢复备份失败: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (backupId: string) => {
    if (!window.confirm('确定要删除此备份吗？此操作不可撤销。')) {
      return;
    }

    try {
      await BackupService.deleteBackup(backupId);
      await loadBackups();
    } catch (error) {
      console.error('Failed to delete backup:', error);
      alert('删除备份失败: ' + (error as Error).message);
    }
  };

  const handleExport = async (backupId: string) => {
    try {
      await BackupService.exportBackup(backupId);
    } catch (error) {
      console.error('Failed to export backup:', error);
      alert('导出备份失败: ' + (error as Error).message);
    }
  };

  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setLoading(true);
    try {
      await BackupService.importBackup(file);
      await loadBackups();
      alert('备份导入成功！');
    } catch (error) {
      console.error('Failed to import backup:', error);
      alert('导入备份失败: ' + (error as Error).message);
    } finally {
      setLoading(false);
      // 重置文件输入
      event.target.value = '';
    }
  };

  const handleStrategyUpdate = (updates: Partial<BackupStrategy>) => {
    const newStrategy = { ...strategy, ...updates };
    setStrategy(newStrategy);
    BackupService.updateBackupStrategy(newStrategy);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getTriggerTypeIcon = (type: string) => {
    switch (type) {
      case 'auto':
        return <RefreshCw className="w-4 h-4 text-blue-600" />;
      case 'milestone':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      default:
        return <Plus className="w-4 h-4 text-gray-600" />;
    }
  };

  const getTriggerTypeText = (type: string) => {
    switch (type) {
      case 'auto':
        return '自动备份';
      case 'milestone':
        return '里程碑';
      default:
        return '手动备份';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* 头部 */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Shield className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900">备份管理</h2>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg"
              title="备份设置"
            >
              <Settings className="w-5 h-5" />
            </button>
            <label className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg cursor-pointer" title="导入备份">
              <Upload className="w-5 h-5" />
              <input
                type="file"
                accept=".json"
                onChange={handleImport}
                className="hidden"
              />
            </label>
            <button
              onClick={handleCreateManualBackup}
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 text-sm"
            >
              {loading ? '创建中...' : '创建备份'}
            </button>
          </div>
        </div>
      </div>

      {/* 备份策略设置 */}
      {showSettings && (
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-3">备份策略设置</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={strategy.autoBackupOnNodeComplete}
                  onChange={(e) => handleStrategyUpdate({ autoBackupOnNodeComplete: e.target.checked })}
                  className="rounded border-gray-300"
                />
                <span className="text-sm text-gray-700">节点完成时自动备份</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={strategy.autoBackupOnMilestone}
                  onChange={(e) => handleStrategyUpdate({ autoBackupOnMilestone: e.target.checked })}
                  className="rounded border-gray-300"
                />
                <span className="text-sm text-gray-700">里程碑时自动备份</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={strategy.compressionEnabled}
                  onChange={(e) => handleStrategyUpdate({ compressionEnabled: e.target.checked })}
                  className="rounded border-gray-300"
                />
                <span className="text-sm text-gray-700">启用压缩</span>
              </label>
            </div>
            <div className="space-y-3">
              <div>
                <label className="block text-sm text-gray-700 mb-1">最大备份数量</label>
                <input
                  type="number"
                  min="1"
                  max="100"
                  value={strategy.maxBackupCount}
                  onChange={(e) => handleStrategyUpdate({ maxBackupCount: parseInt(e.target.value) })}
                  className="w-full px-3 py-1 border border-gray-300 rounded text-sm"
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 备份列表 */}
      <div className="p-4">
        {loading && backups.length === 0 ? (
          <div className="text-center py-8">
            <RefreshCw className="w-8 h-8 text-gray-400 mx-auto mb-2 animate-spin" />
            <p className="text-gray-500">加载备份列表...</p>
          </div>
        ) : backups.length === 0 ? (
          <div className="text-center py-8">
            <Shield className="w-12 h-12 text-gray-300 mx-auto mb-3" />
            <p className="text-gray-500 mb-4">暂无备份记录</p>
            <button
              onClick={handleCreateManualBackup}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              创建第一个备份
            </button>
          </div>
        ) : (
          <div className="space-y-3">
            {backups.map((backup) => (
              <div
                key={backup.id}
                className={`p-4 border rounded-lg transition-colors ${
                  selectedBackup === backup.id 
                    ? 'border-blue-300 bg-blue-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedBackup(selectedBackup === backup.id ? null : backup.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      {getTriggerTypeIcon(backup.metadata.triggerType)}
                      <h4 className="font-medium text-gray-900">{backup.description}</h4>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <div className="flex items-center space-x-1">
                        <Clock className="w-4 h-4" />
                        <span>{format(new Date(backup.timestamp), 'yyyy-MM-dd HH:mm', { locale: zhCN })}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <HardDrive className="w-4 h-4" />
                        <span>{formatFileSize(backup.metadata.size)}</span>
                      </div>
                      <span className="px-2 py-1 bg-gray-100 rounded text-xs">
                        {getTriggerTypeText(backup.metadata.triggerType)}
                      </span>
                    </div>
                    {backup.metadata.nodeName && (
                      <div className="mt-1 text-sm text-gray-500">
                        关联节点: {backup.metadata.nodeName}
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleExport(backup.id);
                      }}
                      className="p-2 text-gray-600 hover:bg-gray-100 rounded"
                      title="导出备份"
                    >
                      <Download className="w-4 h-4" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleRestore(backup.id);
                      }}
                      className="p-2 text-blue-600 hover:bg-blue-100 rounded"
                      title="恢复备份"
                    >
                      <RefreshCw className="w-4 h-4" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(backup.id);
                      }}
                      className="p-2 text-red-600 hover:bg-red-100 rounded"
                      title="删除备份"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 统计信息 */}
      <div className="p-4 bg-gray-50 border-t border-gray-200">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-blue-600">{backups.length}</div>
            <div className="text-sm text-gray-600">总备份数</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-600">
              {formatFileSize(backups.reduce((sum, b) => sum + b.metadata.size, 0))}
            </div>
            <div className="text-sm text-gray-600">总大小</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-purple-600">
              {backups.filter(b => b.metadata.triggerType === 'auto').length}
            </div>
            <div className="text-sm text-gray-600">自动备份</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BackupManager;
