import React, { useState, useEffect, useCallback } from 'react';
import { 
  FileText, 
  Search, 
  Filter, 
  Plus, 
  Edit3, 
  Trash2, 
  Eye,
  Calendar,
  Tag,
  User,
  Clock
} from 'lucide-react';
import { Document, ProjectTreeNode } from '../../types/index';
import { DevDailyAdapter } from '../../services/DevDailyAdapter';
import clsx from 'clsx';
import { format } from 'date-fns';

interface DocumentManagerProps {
  documents: Document[];
  selectedNode?: ProjectTreeNode | null;
  onDocumentSelect?: (document: Document) => void;
  onDocumentEdit?: (document: Document) => void;
  onDocumentCreate?: () => void;
  className?: string;
}

// 文档卡片组件
const DocumentCard: React.FC<{
  document: Document;
  isSelected?: boolean;
  onSelect: () => void;
  onEdit: () => void;
  onDelete: () => void;
}> = ({ document, isSelected, onSelect, onEdit, onDelete }) => {
  const getTypeColor = (type: Document['type']) => {
    const colors = {
      'daily-log': 'bg-blue-100 text-blue-800',
      'issue-report': 'bg-red-100 text-red-800',
      'deployment': 'bg-green-100 text-green-800',
      'summary': 'bg-purple-100 text-purple-800',
      'guideline': 'bg-yellow-100 text-yellow-800',
      'template': 'bg-gray-100 text-gray-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const getTypeName = (type: Document['type']) => {
    const names = {
      'daily-log': '日常记录',
      'issue-report': '问题报告',
      'deployment': '部署记录',
      'summary': '总结报告',
      'guideline': '开发指南',
      'template': '文档模板'
    };
    return names[type] || type;
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return 'text-green-600';
      case 'negative': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div
      className={clsx(
        'p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md',
        isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
      )}
      onClick={onSelect}
    >
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-2">
          <FileText size={16} className="text-gray-500" />
          <h3 className="font-medium text-gray-900 truncate">{document.title}</h3>
        </div>
        <div className="flex items-center gap-1">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onEdit();
            }}
            className="p-1 text-blue-600 hover:bg-blue-100 rounded"
            title="编辑文档"
          >
            <Edit3 size={14} />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            className="p-1 text-red-600 hover:bg-red-100 rounded"
            title="删除文档"
          >
            <Trash2 size={14} />
          </button>
        </div>
      </div>

      <div className="flex items-center gap-2 mb-2">
        <span className={clsx('px-2 py-1 text-xs font-medium rounded-full', getTypeColor(document.type))}>
          {getTypeName(document.type)}
        </span>
        <span className={clsx('px-2 py-1 text-xs rounded-full', 
          document.status === 'published' ? 'bg-green-100 text-green-800' : 
          document.status === 'draft' ? 'bg-yellow-100 text-yellow-800' : 
          'bg-gray-100 text-gray-800'
        )}>
          {document.status === 'published' ? '已发布' : 
           document.status === 'draft' ? '草稿' : '已归档'}
        </span>
      </div>

      <p className="text-sm text-gray-600 mb-3 line-clamp-2">
        {document.metadata.aiAnalysis?.summary || '暂无摘要'}
      </p>

      <div className="flex items-center justify-between text-xs text-gray-500">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-1">
            <Calendar size={12} />
            <span>{format(new Date(document.metadata.createdAt), 'MM-dd')}</span>
          </div>
          <div className="flex items-center gap-1">
            <User size={12} />
            <span>{document.metadata.author}</span>
          </div>
          {document.metadata.wordCount && (
            <div className="flex items-center gap-1">
              <FileText size={12} />
              <span>{document.metadata.wordCount}字</span>
            </div>
          )}
        </div>
        {document.metadata.aiAnalysis?.sentiment && (
          <div className={clsx('flex items-center gap-1', getSentimentColor(document.metadata.aiAnalysis.sentiment))}>
            <div className="w-2 h-2 rounded-full bg-current"></div>
            <span className="capitalize">{document.metadata.aiAnalysis.sentiment}</span>
          </div>
        )}
      </div>

      {document.metadata.tags && document.metadata.tags.length > 0 && (
        <div className="flex flex-wrap gap-1 mt-2">
          {document.metadata.tags.slice(0, 3).map((tag, index) => (
            <span key={index} className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
              #{tag}
            </span>
          ))}
          {document.metadata.tags.length > 3 && (
            <span className="text-xs text-gray-500">+{document.metadata.tags.length - 3}</span>
          )}
        </div>
      )}
    </div>
  );
};

// 文档统计组件
const DocumentStats: React.FC<{ documents: Document[] }> = ({ documents }) => {
  const stats = {
    total: documents.length,
    byType: documents.reduce((acc, doc) => {
      acc[doc.type] = (acc[doc.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    byStatus: documents.reduce((acc, doc) => {
      acc[doc.status] = (acc[doc.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    totalWords: documents.reduce((acc, doc) => acc + (doc.metadata.wordCount || 0), 0),
    recentCount: documents.filter(doc => {
      const daysDiff = (Date.now() - new Date(doc.metadata.createdAt).getTime()) / (1000 * 60 * 60 * 24);
      return daysDiff <= 7;
    }).length
  };

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
      <div className="text-center">
        <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
        <div className="text-sm text-gray-600">总文档数</div>
      </div>
      <div className="text-center">
        <div className="text-2xl font-bold text-green-600">{stats.recentCount}</div>
        <div className="text-sm text-gray-600">近7天新增</div>
      </div>
      <div className="text-center">
        <div className="text-2xl font-bold text-purple-600">{Math.round(stats.totalWords / 1000)}k</div>
        <div className="text-sm text-gray-600">总字数</div>
      </div>
      <div className="text-center">
        <div className="text-2xl font-bold text-orange-600">{stats.byStatus.published || 0}</div>
        <div className="text-sm text-gray-600">已发布</div>
      </div>
    </div>
  );
};

export const DocumentManager: React.FC<DocumentManagerProps> = ({
  documents,
  selectedNode,
  onDocumentSelect,
  onDocumentEdit,
  onDocumentCreate,
  className
}) => {
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>(documents);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<Document['type'] | 'all'>('all');
  const [filterStatus, setFilterStatus] = useState<Document['status'] | 'all'>('all');
  const [sortBy, setSortBy] = useState<'date' | 'name' | 'type'>('date');
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);

  // 过滤和搜索文档
  useEffect(() => {
    let filtered = [...documents];

    // 如果选中了节点，只显示关联的文档
    if (selectedNode && selectedNode.metadata.linkedDocuments) {
      filtered = filtered.filter(doc => 
        selectedNode.metadata.linkedDocuments!.includes(doc.filename)
      );
    }

    // 搜索过滤
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(doc =>
        doc.title.toLowerCase().includes(term) ||
        doc.content.toLowerCase().includes(term) ||
        doc.metadata.tags.some(tag => tag.toLowerCase().includes(term))
      );
    }

    // 类型过滤
    if (filterType !== 'all') {
      filtered = filtered.filter(doc => doc.type === filterType);
    }

    // 状态过滤
    if (filterStatus !== 'all') {
      filtered = filtered.filter(doc => doc.status === filterStatus);
    }

    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.title.localeCompare(b.title);
        case 'type':
          return a.type.localeCompare(b.type);
        case 'date':
        default:
          return new Date(b.metadata.createdAt).getTime() - new Date(a.metadata.createdAt).getTime();
      }
    });

    setFilteredDocuments(filtered);
  }, [documents, selectedNode, searchTerm, filterType, filterStatus, sortBy]);

  const handleDocumentSelect = useCallback((document: Document) => {
    setSelectedDocument(document);
    onDocumentSelect?.(document);
  }, [onDocumentSelect]);

  const handleDocumentEdit = useCallback((document: Document) => {
    onDocumentEdit?.(document);
  }, [onDocumentEdit]);

  const handleDocumentDelete = useCallback((document: Document) => {
    if (window.confirm(`确定要删除文档 "${document.title}" 吗？`)) {
      // TODO: 实现删除逻辑
      console.log('Delete document:', document.id);
    }
  }, []);

  return (
    <div className={clsx('flex flex-col h-full', className)}>
      {/* 统计信息 */}
      <DocumentStats documents={documents} />

      {/* 工具栏 */}
      <div className="flex flex-col gap-4 p-4 border-b border-gray-200">
        <div className="flex items-center gap-4">
          <div className="flex-1 relative">
            <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="搜索文档..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <button
            onClick={onDocumentCreate}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center gap-2"
          >
            <Plus size={16} />
            新建文档
          </button>
        </div>

        <div className="flex items-center gap-4">
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value as Document['type'] | 'all')}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">所有类型</option>
            <option value="daily-log">日常记录</option>
            <option value="issue-report">问题报告</option>
            <option value="deployment">部署记录</option>
            <option value="summary">总结报告</option>
            <option value="guideline">开发指南</option>
            <option value="template">文档模板</option>
          </select>

          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value as Document['status'] | 'all')}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">所有状态</option>
            <option value="published">已发布</option>
            <option value="draft">草稿</option>
            <option value="archived">已归档</option>
          </select>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'date' | 'name' | 'type')}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="date">按日期排序</option>
            <option value="name">按名称排序</option>
            <option value="type">按类型排序</option>
          </select>
        </div>

        {selectedNode && (
          <div className="text-sm text-gray-600 bg-blue-50 p-2 rounded">
            显示与节点 "{selectedNode.name}" 关联的文档 ({filteredDocuments.length} 个)
          </div>
        )}
      </div>

      {/* 文档列表 */}
      <div className="flex-1 overflow-auto p-4">
        {filteredDocuments.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            {searchTerm || filterType !== 'all' || filterStatus !== 'all' ? 
              '没有找到匹配的文档' : 
              '暂无文档，点击"新建文档"开始创建'}
          </div>
        ) : (
          <div className="grid gap-4">
            {filteredDocuments.map((document) => (
              <DocumentCard
                key={document.id}
                document={document}
                isSelected={selectedDocument?.id === document.id}
                onSelect={() => handleDocumentSelect(document)}
                onEdit={() => handleDocumentEdit(document)}
                onDelete={() => handleDocumentDelete(document)}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
