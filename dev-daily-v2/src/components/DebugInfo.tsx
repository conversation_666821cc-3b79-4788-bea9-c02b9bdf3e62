import React, { useEffect, useState } from 'react';
import { TestDataService } from '../services/TestDataService';
import { Document, ProjectTreeNode } from '../types/index';

// 调试信息组件 - 用于验证数据加载
export const DebugInfo: React.FC = () => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [projectTree, setProjectTree] = useState<ProjectTreeNode | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        console.log('🔄 开始加载测试数据...');
        
        const testDocuments = TestDataService.generateTestDocuments();
        const testProjectTree = TestDataService.generateTestProjectTree();
        const stats = TestDataService.getProjectStats(testProjectTree, testDocuments);
        
        setDocuments(testDocuments);
        setProjectTree(testProjectTree);
        
        console.log('✅ 测试数据加载成功');
        console.log('📊 统计信息:', stats);
        
      } catch (err) {
        const errorMsg = err instanceof Error ? err.message : '未知错误';
        setError(errorMsg);
        console.error('❌ 数据加载失败:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  if (isLoading) {
    return (
      <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-center gap-3">
          <div className="animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full"></div>
          <span className="text-blue-700">正在加载测试数据...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <h3 className="text-lg font-semibold text-red-800 mb-2">数据加载错误</h3>
        <p className="text-red-600">{error}</p>
      </div>
    );
  }

  const stats = projectTree ? TestDataService.getProjectStats(projectTree, documents) : null;

  return (
    <div className="p-6 space-y-6">
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-green-800 mb-2">✅ 数据加载成功</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-green-600 font-medium">文档数量:</span>
            <span className="ml-2 text-green-800">{documents.length}</span>
          </div>
          <div>
            <span className="text-green-600 font-medium">项目节点:</span>
            <span className="ml-2 text-green-800">{stats?.totalNodes || 0}</span>
          </div>
          <div>
            <span className="text-green-600 font-medium">已完成:</span>
            <span className="ml-2 text-green-800">{stats?.completedNodes || 0}</span>
          </div>
          <div>
            <span className="text-green-600 font-medium">进行中:</span>
            <span className="ml-2 text-green-800">{stats?.activeNodes || 0}</span>
          </div>
        </div>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h4 className="font-semibold text-gray-800 mb-3">📄 文档列表</h4>
        <div className="space-y-2">
          {documents.map((doc) => (
            <div key={doc.id} className="flex items-center gap-3 p-2 bg-gray-50 rounded">
              <div className={`w-3 h-3 rounded-full ${
                doc.type === 'guideline' ? 'bg-blue-500' :
                doc.type === 'daily-log' ? 'bg-green-500' :
                doc.type === 'summary' ? 'bg-purple-500' :
                doc.type === 'issue-report' ? 'bg-red-500' : 'bg-gray-500'
              }`}></div>
              <span className="text-sm text-gray-700">{doc.title}</span>
              <span className="text-xs text-gray-500 ml-auto">{doc.type}</span>
            </div>
          ))}
        </div>
      </div>

      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h4 className="font-semibold text-gray-800 mb-3">🌳 项目树结构</h4>
        {projectTree && (
          <div className="text-sm">
            <div className="font-medium text-gray-800">{projectTree.name}</div>
            {projectTree.children && (
              <div className="ml-4 mt-2 space-y-1">
                {projectTree.children.map((child) => (
                  <div key={child.id} className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${
                      child.status === 'completed' ? 'bg-green-500' :
                      child.status === 'active' ? 'bg-blue-500' :
                      child.status === 'planned' ? 'bg-yellow-500' : 'bg-gray-500'
                    }`}></div>
                    <span className="text-gray-700">{child.name}</span>
                    {child.children && (
                      <span className="text-xs text-gray-500">({child.children.length} 子项)</span>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      <div className="text-center">
        <button 
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
        >
          重新加载
        </button>
      </div>
    </div>
  );
};
