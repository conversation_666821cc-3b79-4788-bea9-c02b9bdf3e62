/**
 * 虚拟滚动列表组件 - 支持大量数据的高性能渲染
 */

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';

export interface VirtualizedListProps<T> {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  overscan?: number; // 预渲染的额外项目数量
  className?: string;
  onScroll?: (scrollTop: number) => void;
}

export function VirtualizedList<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  overscan = 5,
  className = '',
  onScroll
}: VirtualizedListProps<T>) {
  const [scrollTop, setScrollTop] = useState(0);
  const scrollElementRef = useRef<HTMLDivElement>(null);

  // 计算可见范围
  const visibleRange = useMemo(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    );
    return { startIndex, endIndex };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan]);

  // 计算总高度
  const totalHeight = items.length * itemHeight;

  // 计算可见项目
  const visibleItems = useMemo(() => {
    const { startIndex, endIndex } = visibleRange;
    return items.slice(startIndex, endIndex + 1).map((item, index) => ({
      item,
      index: startIndex + index
    }));
  }, [items, visibleRange]);

  // 处理滚动事件
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = e.currentTarget.scrollTop;
    setScrollTop(newScrollTop);
    onScroll?.(newScrollTop);
  }, [onScroll]);

  // 滚动到指定索引
  const scrollToIndex = useCallback((index: number) => {
    if (scrollElementRef.current) {
      const targetScrollTop = index * itemHeight;
      scrollElementRef.current.scrollTop = targetScrollTop;
      setScrollTop(targetScrollTop);
    }
  }, [itemHeight]);

  // 滚动到指定项目
  const scrollToItem = useCallback((item: T) => {
    const index = items.indexOf(item);
    if (index !== -1) {
      scrollToIndex(index);
    }
  }, [items, scrollToIndex]);

  return (
    <div
      ref={scrollElementRef}
      className={`overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div
          style={{
            transform: `translateY(${visibleRange.startIndex * itemHeight}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0
          }}
        >
          {visibleItems.map(({ item, index }) => (
            <div
              key={index}
              style={{ height: itemHeight }}
              className="flex items-center"
            >
              {renderItem(item, index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Hook 用于虚拟滚动状态管理
export function useVirtualizedList<T>(items: T[], itemHeight: number) {
  const [scrollTop, setScrollTop] = useState(0);
  const [containerHeight, setContainerHeight] = useState(400);

  const scrollToIndex = useCallback((index: number) => {
    setScrollTop(index * itemHeight);
  }, [itemHeight]);

  const scrollToItem = useCallback((item: T) => {
    const index = items.indexOf(item);
    if (index !== -1) {
      scrollToIndex(index);
    }
  }, [items, scrollToIndex]);

  return {
    scrollTop,
    containerHeight,
    setContainerHeight,
    scrollToIndex,
    scrollToItem,
    setScrollTop
  };
}

// 虚拟化项目树组件
export interface VirtualizedTreeProps {
  nodes: any[];
  nodeHeight?: number;
  containerHeight?: number;
  onNodeSelect?: (node: any) => void;
  className?: string;
}

export const VirtualizedTree: React.FC<VirtualizedTreeProps> = ({
  nodes,
  nodeHeight = 40,
  containerHeight = 600,
  onNodeSelect,
  className
}) => {
  // 扁平化树结构以支持虚拟滚动
  const flattenedNodes = useMemo(() => {
    const flatten = (nodes: any[], level = 0): any[] => {
      return nodes.reduce((acc, node) => {
        acc.push({ ...node, level });
        if (node.children && node.expanded !== false) {
          acc.push(...flatten(node.children, level + 1));
        }
        return acc;
      }, []);
    };
    return flatten(nodes);
  }, [nodes]);

  const renderNode = useCallback((node: any, index: number) => {
    const paddingLeft = node.level * 20;
    
    return (
      <div
        className="flex items-center px-4 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100"
        style={{ paddingLeft: paddingLeft + 16 }}
        onClick={() => onNodeSelect?.(node)}
      >
        <div className="flex items-center space-x-2 flex-1">
          {node.children && (
            <button
              className="w-4 h-4 flex items-center justify-center text-gray-400 hover:text-gray-600"
              onClick={(e) => {
                e.stopPropagation();
                // 切换展开状态的逻辑
              }}
            >
              {node.expanded !== false ? '−' : '+'}
            </button>
          )}
          <span className="text-sm font-medium text-gray-900">{node.name}</span>
        </div>
        <div className="flex items-center space-x-2">
          <span className={`px-2 py-1 text-xs rounded-full ${
            node.status === 'completed' ? 'bg-green-100 text-green-800' :
            node.status === 'active' ? 'bg-blue-100 text-blue-800' :
            'bg-gray-100 text-gray-800'
          }`}>
            {node.status}
          </span>
          <span className="text-xs text-gray-500">{node.progress}%</span>
        </div>
      </div>
    );
  }, [onNodeSelect]);

  return (
    <VirtualizedList
      items={flattenedNodes}
      itemHeight={nodeHeight}
      containerHeight={containerHeight}
      renderItem={renderNode}
      className={className}
    />
  );
};

// 虚拟化文档列表组件
export interface VirtualizedDocumentListProps {
  documents: any[];
  itemHeight?: number;
  containerHeight?: number;
  onDocumentSelect?: (document: any) => void;
  className?: string;
}

export const VirtualizedDocumentList: React.FC<VirtualizedDocumentListProps> = ({
  documents,
  itemHeight = 60,
  containerHeight = 400,
  onDocumentSelect,
  className
}) => {
  const renderDocument = useCallback((document: any, index: number) => {
    return (
      <div
        className="flex items-center px-4 py-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100"
        onClick={() => onDocumentSelect?.(document)}
      >
        <div className="flex-1">
          <h3 className="text-sm font-medium text-gray-900">{document.title}</h3>
          <p className="text-xs text-gray-500 mt-1">{document.description}</p>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-xs text-gray-400">
            {new Date(document.updatedAt).toLocaleDateString()}
          </span>
          <span className={`px-2 py-1 text-xs rounded-full ${
            document.type === 'requirement' ? 'bg-blue-100 text-blue-800' :
            document.type === 'design' ? 'bg-purple-100 text-purple-800' :
            'bg-gray-100 text-gray-800'
          }`}>
            {document.type}
          </span>
        </div>
      </div>
    );
  }, [onDocumentSelect]);

  return (
    <VirtualizedList
      items={documents}
      itemHeight={itemHeight}
      containerHeight={containerHeight}
      renderItem={renderDocument}
      className={className}
    />
  );
};
