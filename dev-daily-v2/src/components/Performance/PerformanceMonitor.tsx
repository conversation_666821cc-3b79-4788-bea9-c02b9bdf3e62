/**
 * 性能监控组件 - 实时监控应用性能指标
 */

import React, { useState, useEffect } from 'react';
import { 
  Activity, 
  Zap, 
  Database, 
  Clock, 
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  RefreshCw
} from 'lucide-react';
import { CacheService } from '../../services/CacheService';

interface PerformanceMetrics {
  memoryUsage: number;
  renderTime: number;
  cacheHitRate: number;
  loadTime: number;
  fps: number;
  bundleSize: number;
}

interface PerformanceAlert {
  type: 'warning' | 'error' | 'info';
  message: string;
  timestamp: number;
}

export const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    memoryUsage: 0,
    renderTime: 0,
    cacheHitRate: 0,
    loadTime: 0,
    fps: 0,
    bundleSize: 0
  });
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);

  useEffect(() => {
    if (isMonitoring) {
      const interval = setInterval(updateMetrics, 1000);
      return () => clearInterval(interval);
    }
  }, [isMonitoring]);

  const updateMetrics = async () => {
    const newMetrics: PerformanceMetrics = {
      memoryUsage: getMemoryUsage(),
      renderTime: getRenderTime(),
      cacheHitRate: await getCacheHitRate(),
      loadTime: getLoadTime(),
      fps: getFPS(),
      bundleSize: getBundleSize()
    };

    setMetrics(newMetrics);
    checkForAlerts(newMetrics);
  };

  const getMemoryUsage = (): number => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100);
    }
    return 0;
  };

  const getRenderTime = (): number => {
    const entries = performance.getEntriesByType('measure');
    const renderEntries = entries.filter(entry => entry.name.includes('render'));
    if (renderEntries.length > 0) {
      const avgDuration = renderEntries.reduce((sum, entry) => sum + entry.duration, 0) / renderEntries.length;
      return Math.round(avgDuration * 100) / 100;
    }
    return 0;
  };

  const getCacheHitRate = async (): Promise<number> => {
    const cache = CacheService.getInstance();
    const stats = cache.getStats();
    return Math.round(stats.hitRate * 100);
  };

  const getLoadTime = (): number => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigation) {
      return Math.round(navigation.loadEventEnd - navigation.fetchStart);
    }
    return 0;
  };

  const getFPS = (): number => {
    // 简化的 FPS 计算
    return 60; // 实际实现需要更复杂的逻辑
  };

  const getBundleSize = (): number => {
    // 估算的包大小（KB）
    return 1250; // 实际实现需要从构建工具获取
  };

  const checkForAlerts = (metrics: PerformanceMetrics) => {
    const newAlerts: PerformanceAlert[] = [];

    if (metrics.memoryUsage > 80) {
      newAlerts.push({
        type: 'warning',
        message: `内存使用率过高: ${metrics.memoryUsage}%`,
        timestamp: Date.now()
      });
    }

    if (metrics.renderTime > 16) {
      newAlerts.push({
        type: 'warning',
        message: `渲染时间过长: ${metrics.renderTime}ms`,
        timestamp: Date.now()
      });
    }

    if (metrics.cacheHitRate < 50) {
      newAlerts.push({
        type: 'info',
        message: `缓存命中率较低: ${metrics.cacheHitRate}%`,
        timestamp: Date.now()
      });
    }

    if (newAlerts.length > 0) {
      setAlerts(prev => [...newAlerts, ...prev.slice(0, 9)]);
    }
  };

  const getMetricStatus = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'good';
    if (value <= thresholds.warning) return 'warning';
    return 'error';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'error': return <AlertTriangle className="w-4 h-4 text-red-500" />;
      default: return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  const clearAlerts = () => {
    setAlerts([]);
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* 控制面板 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">性能监控</h2>
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setIsMonitoring(!isMonitoring)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                isMonitoring 
                  ? 'bg-red-600 text-white hover:bg-red-700' 
                  : 'bg-green-600 text-white hover:bg-green-700'
              }`}
            >
              {isMonitoring ? (
                <>
                  <Activity className="w-4 h-4" />
                  <span>停止监控</span>
                </>
              ) : (
                <>
                  <Activity className="w-4 h-4" />
                  <span>开始监控</span>
                </>
              )}
            </button>
            <button
              onClick={updateMetrics}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
            >
              <RefreshCw className="w-4 h-4" />
              <span>刷新</span>
            </button>
          </div>
        </div>

        {/* 性能指标卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* 内存使用率 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <Database className="w-5 h-5 text-blue-500" />
                <span className="font-medium text-gray-900">内存使用</span>
              </div>
              {getStatusIcon(getMetricStatus(metrics.memoryUsage, { good: 50, warning: 80 }))}
            </div>
            <div className="text-2xl font-bold text-gray-900">{metrics.memoryUsage}%</div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${
                  metrics.memoryUsage > 80 ? 'bg-red-500' :
                  metrics.memoryUsage > 50 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{ width: `${Math.min(metrics.memoryUsage, 100)}%` }}
              />
            </div>
          </div>

          {/* 渲染时间 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <Zap className="w-5 h-5 text-yellow-500" />
                <span className="font-medium text-gray-900">渲染时间</span>
              </div>
              {getStatusIcon(getMetricStatus(metrics.renderTime, { good: 8, warning: 16 }))}
            </div>
            <div className="text-2xl font-bold text-gray-900">{metrics.renderTime}ms</div>
            <div className="text-sm text-gray-500 mt-1">目标: &lt;16ms</div>
          </div>

          {/* 缓存命中率 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-5 h-5 text-green-500" />
                <span className="font-medium text-gray-900">缓存命中率</span>
              </div>
              {getStatusIcon(getMetricStatus(100 - metrics.cacheHitRate, { good: 20, warning: 50 }))}
            </div>
            <div className="text-2xl font-bold text-gray-900">{metrics.cacheHitRate}%</div>
            <div className="text-sm text-gray-500 mt-1">目标: &gt;80%</div>
          </div>

          {/* 加载时间 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <Clock className="w-5 h-5 text-purple-500" />
                <span className="font-medium text-gray-900">加载时间</span>
              </div>
              {getStatusIcon(getMetricStatus(metrics.loadTime, { good: 1000, warning: 3000 }))}
            </div>
            <div className="text-2xl font-bold text-gray-900">{metrics.loadTime}ms</div>
            <div className="text-sm text-gray-500 mt-1">目标: &lt;3s</div>
          </div>

          {/* FPS */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <Activity className="w-5 h-5 text-red-500" />
                <span className="font-medium text-gray-900">帧率</span>
              </div>
              {getStatusIcon(getMetricStatus(60 - metrics.fps, { good: 10, warning: 20 }))}
            </div>
            <div className="text-2xl font-bold text-gray-900">{metrics.fps} FPS</div>
            <div className="text-sm text-gray-500 mt-1">目标: 60 FPS</div>
          </div>

          {/* 包大小 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <Database className="w-5 h-5 text-indigo-500" />
                <span className="font-medium text-gray-900">包大小</span>
              </div>
              {getStatusIcon(getMetricStatus(metrics.bundleSize, { good: 1000, warning: 2000 }))}
            </div>
            <div className="text-2xl font-bold text-gray-900">{metrics.bundleSize}KB</div>
            <div className="text-sm text-gray-500 mt-1">目标: &lt;2MB</div>
          </div>
        </div>
      </div>

      {/* 性能警告 */}
      {alerts.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">性能警告</h3>
            <button
              onClick={clearAlerts}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              清除全部
            </button>
          </div>
          <div className="space-y-3">
            {alerts.map((alert, index) => (
              <div
                key={index}
                className={`flex items-center space-x-3 p-3 rounded-lg ${
                  alert.type === 'error' ? 'bg-red-50 text-red-800' :
                  alert.type === 'warning' ? 'bg-yellow-50 text-yellow-800' :
                  'bg-blue-50 text-blue-800'
                }`}
              >
                <AlertTriangle className="w-5 h-5 flex-shrink-0" />
                <span className="flex-1">{alert.message}</span>
                <span className="text-xs opacity-75">
                  {new Date(alert.timestamp).toLocaleTimeString()}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 性能建议 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">性能优化建议</h3>
        <div className="space-y-3 text-sm text-gray-700">
          <div className="flex items-start space-x-2">
            <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
            <span>使用虚拟滚动处理大量数据列表</span>
          </div>
          <div className="flex items-start space-x-2">
            <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
            <span>启用智能缓存减少重复计算</span>
          </div>
          <div className="flex items-start space-x-2">
            <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
            <span>使用增量同步减少数据传输</span>
          </div>
          <div className="flex items-start space-x-2">
            <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
            <span>定期清理过期缓存和临时数据</span>
          </div>
        </div>
      </div>
    </div>
  );
};
