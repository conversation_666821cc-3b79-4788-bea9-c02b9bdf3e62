import React, { useState, useEffect } from 'react';
import {
  Setting<PERSON>,
  <PERSON>,
  Palette,
  Globe,
  Shield,
  Database,
  Monitor,
  Save,
  RotateCcw,
  Download,
  Upload,
  Key,
  Zap,
  Eye,
  EyeOff,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { UserSettings } from '../../types/index';
import { OpenAIService } from '../../services/OpenAIService';
import clsx from 'clsx';

interface SettingsPageProps {
  className?: string;
}

// 默认设置
const defaultSettings: UserSettings = {
  theme: 'light',
  language: 'zh-CN',
  editor: {
    fontSize: 14,
    tabSize: 2,
    wordWrap: true,
    showLineNumbers: true,
    autoSave: true,
    autoSaveDelay: 1000
  },
  ai: {
    enabled: true,
    provider: 'openai',
    model: 'gpt-3.5-turbo',
    temperature: 0.7,
    maxTokens: 2000,
    autoSuggestions: true
  },
  backup: {
    autoBackup: true,
    backupInterval: 'daily',
    retentionDays: 30,
    compressionEnabled: true
  },
  ui: {
    sidebarWidth: 280,
    showMinimap: false,
    compactMode: false,
    animationsEnabled: true
  }
};

// 设置分类
type SettingsCategory = 'ai' | 'appearance' | 'editor' | 'backup' | 'advanced';

export const SettingsPage: React.FC<SettingsPageProps> = ({ className }) => {
  const [settings, setSettings] = useState<UserSettings>(defaultSettings);
  const [activeCategory, setActiveCategory] = useState<SettingsCategory>('ai');
  const [showApiKey, setShowApiKey] = useState(false);
  const [apiKey, setApiKey] = useState(''); // API密钥将从localStorage加载或用户输入
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // 加载设置
  useEffect(() => {
    const savedSettings = localStorage.getItem('dev-daily-settings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setSettings({ ...defaultSettings, ...parsed });
      } catch (error) {
        console.error('Failed to load settings:', error);
      }
    }

    const savedApiKey = localStorage.getItem('dev-daily-api-key');
    if (savedApiKey) {
      setApiKey(savedApiKey);
    }
  }, []);

  // 保存设置
  const handleSaveSettings = () => {
    try {
      localStorage.setItem('dev-daily-settings', JSON.stringify(settings));
      localStorage.setItem('dev-daily-api-key', apiKey);
      setHasUnsavedChanges(false);
      
      // 显示保存成功提示
      const event = new CustomEvent('settings-saved', { detail: settings });
      window.dispatchEvent(event);
      
      alert('设置已保存成功！');
    } catch (error) {
      console.error('Failed to save settings:', error);
      alert('保存设置失败，请重试。');
    }
  };

  // 重置设置
  const handleResetSettings = () => {
    if (window.confirm('确定要重置所有设置到默认值吗？此操作不可撤销。')) {
      setSettings(defaultSettings);
      setApiKey('');
      setHasUnsavedChanges(true);
    }
  };

  // 导出设置
  const handleExportSettings = () => {
    const exportData = {
      settings,
      apiKey: apiKey ? '***已设置***' : '',
      exportedAt: new Date().toISOString(),
      version: '2.0.0'
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `dev-daily-settings-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 导入设置
  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importData = JSON.parse(e.target?.result as string);
        if (importData.settings) {
          setSettings({ ...defaultSettings, ...importData.settings });
          setHasUnsavedChanges(true);
          alert('设置导入成功！请检查并保存设置。');
        }
      } catch (error) {
        console.error('Failed to import settings:', error);
        alert('导入设置失败，请检查文件格式。');
      }
    };
    reader.readAsText(file);
  };

  // 更新设置
  const updateSettings = (path: string, value: any) => {
    const keys = path.split('.');
    const newSettings = { ...settings };
    let current: any = newSettings;
    
    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;
    
    setSettings(newSettings);
    setHasUnsavedChanges(true);
  };

  // 设置分类配置
  const categories = [
    { id: 'ai' as SettingsCategory, name: 'AI设置', icon: Brain, description: 'AI模型和API配置' },
    { id: 'appearance' as SettingsCategory, name: '外观', icon: Palette, description: '主题和界面设置' },
    { id: 'editor' as SettingsCategory, name: '编辑器', icon: Monitor, description: '编辑器偏好设置' },
    { id: 'backup' as SettingsCategory, name: '备份', icon: Database, description: '数据备份和恢复' },
    { id: 'advanced' as SettingsCategory, name: '高级', icon: Settings, description: '高级系统设置' }
  ];

  return (
    <div className={clsx('flex h-full bg-white', className)}>
      {/* 侧边栏 */}
      <div className="w-64 bg-gray-50 border-r border-gray-200 p-4">
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Settings className="h-5 w-5" />
            系统设置
          </h2>
          <p className="text-sm text-gray-600 mt-1">配置您的dev-daily体验</p>
        </div>

        <nav className="space-y-2">
          {categories.map((category) => {
            const Icon = category.icon;
            return (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={clsx(
                  'w-full text-left p-3 rounded-lg transition-colors',
                  activeCategory === category.id
                    ? 'bg-blue-100 text-blue-900 border border-blue-200'
                    : 'hover:bg-gray-100 text-gray-700'
                )}
              >
                <div className="flex items-center gap-3">
                  <Icon className="h-5 w-5" />
                  <div>
                    <div className="font-medium">{category.name}</div>
                    <div className="text-xs text-gray-500">{category.description}</div>
                  </div>
                </div>
              </button>
            );
          })}
        </nav>

        {/* 操作按钮 */}
        <div className="mt-8 space-y-2">
          <button
            onClick={handleSaveSettings}
            disabled={!hasUnsavedChanges}
            className={clsx(
              'w-full flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors',
              hasUnsavedChanges
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-100 text-gray-400 cursor-not-allowed'
            )}
          >
            <Save className="h-4 w-4" />
            保存设置
          </button>

          <button
            onClick={handleResetSettings}
            className="w-full flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium text-gray-600 hover:bg-gray-100 transition-colors"
          >
            <RotateCcw className="h-4 w-4" />
            重置默认
          </button>

          <div className="flex gap-2">
            <button
              onClick={handleExportSettings}
              className="flex-1 flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium text-gray-600 hover:bg-gray-100 transition-colors"
            >
              <Download className="h-4 w-4" />
              导出
            </button>
            
            <label className="flex-1 flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium text-gray-600 hover:bg-gray-100 transition-colors cursor-pointer">
              <Upload className="h-4 w-4" />
              导入
              <input
                type="file"
                accept=".json"
                onChange={handleImportSettings}
                className="hidden"
              />
            </label>
          </div>
        </div>

        {hasUnsavedChanges && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800">
              ⚠️ 您有未保存的更改
            </p>
          </div>
        )}
      </div>

      {/* 主内容区 */}
      <div className="flex-1 p-6 overflow-auto">
        {activeCategory === 'ai' && (
          <AISettingsPanel
            settings={settings.ai}
            apiKey={apiKey}
            showApiKey={showApiKey}
            onUpdateSettings={(path, value) => updateSettings(`ai.${path}`, value)}
            onUpdateApiKey={setApiKey}
            onToggleShowApiKey={() => setShowApiKey(!showApiKey)}
            onApiKeyChange={() => setHasUnsavedChanges(true)}
          />
        )}

        {activeCategory === 'appearance' && (
          <AppearanceSettingsPanel
            settings={settings}
            onUpdateSettings={updateSettings}
          />
        )}

        {activeCategory === 'editor' && (
          <EditorSettingsPanel
            settings={settings.editor}
            onUpdateSettings={(path, value) => updateSettings(`editor.${path}`, value)}
          />
        )}

        {activeCategory === 'backup' && (
          <BackupSettingsPanel
            settings={settings.backup}
            onUpdateSettings={(path, value) => updateSettings(`backup.${path}`, value)}
          />
        )}

        {activeCategory === 'advanced' && (
          <AdvancedSettingsPanel
            settings={settings.ui}
            onUpdateSettings={(path, value) => updateSettings(`ui.${path}`, value)}
          />
        )}
      </div>
    </div>
  );
};

// AI设置面板
const AISettingsPanel: React.FC<{
  settings: UserSettings['ai'];
  apiKey: string;
  showApiKey: boolean;
  onUpdateSettings: (path: string, value: any) => void;
  onUpdateApiKey: (key: string) => void;
  onToggleShowApiKey: () => void;
  onApiKeyChange: () => void;
}> = ({ settings, apiKey, showApiKey, onUpdateSettings, onUpdateApiKey, onToggleShowApiKey, onApiKeyChange }) => {
  const handleApiKeyChange = (value: string) => {
    onUpdateApiKey(value);
    onApiKeyChange();
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">AI设置</h3>
        <p className="text-gray-600">配置AI模型和API参数</p>
      </div>

      {/* AI启用开关 */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-gray-900">启用AI功能</h4>
            <p className="text-sm text-gray-600">开启AI智能分析和建议功能</p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={settings.enabled}
              onChange={(e) => onUpdateSettings('enabled', e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
          </label>
        </div>
      </div>

      {settings.enabled && (
        <>
          {/* API配置 */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900 flex items-center gap-2">
              <Key className="h-4 w-4" />
              API配置
            </h4>

            {/* API提供商 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                AI提供商
              </label>
              <select
                value={settings.provider}
                onChange={(e) => onUpdateSettings('provider', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="openai">OpenAI</option>
                <option value="local">本地模型</option>
                <option value="mock">模拟模式</option>
              </select>
            </div>

            {/* API密钥 */}
            {settings.provider === 'openai' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  OpenAI API密钥
                </label>
                <div className="relative">
                  <input
                    type={showApiKey ? 'text' : 'password'}
                    value={apiKey}
                    onChange={(e) => handleApiKeyChange(e.target.value)}
                    placeholder="sk-..."
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <button
                    type="button"
                    onClick={onToggleShowApiKey}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showApiKey ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </button>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  API密钥将安全存储在本地浏览器中
                </p>
              </div>
            )}

            {/* 模型选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                AI模型
              </label>
              <select
                value={settings.model}
                onChange={(e) => onUpdateSettings('model', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {settings.provider === 'openai' && (
                  <>
                    <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                    <option value="gpt-4">GPT-4</option>
                    <option value="gpt-4-turbo">GPT-4 Turbo</option>
                  </>
                )}
                {settings.provider === 'local' && (
                  <option value="local-model">本地模型</option>
                )}
                {settings.provider === 'mock' && (
                  <option value="mock-model">模拟模型</option>
                )}
              </select>
            </div>
          </div>

          {/* 模型参数 */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900 flex items-center gap-2">
              <Zap className="h-4 w-4" />
              模型参数
            </h4>

            {/* 温度 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                创造性 (Temperature): {settings.temperature}
              </label>
              <input
                type="range"
                min="0"
                max="2"
                step="0.1"
                value={settings.temperature}
                onChange={(e) => onUpdateSettings('temperature', parseFloat(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>保守 (0.0)</span>
                <span>平衡 (1.0)</span>
                <span>创新 (2.0)</span>
              </div>
            </div>

            {/* 最大令牌数 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                最大令牌数
              </label>
              <input
                type="number"
                min="100"
                max="4000"
                step="100"
                value={settings.maxTokens}
                onChange={(e) => onUpdateSettings('maxTokens', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">
                控制AI回复的最大长度，更高的值允许更长的回复
              </p>
            </div>

            {/* 自动建议 */}
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-gray-900">自动建议</h4>
                <p className="text-sm text-gray-600">自动生成项目改进建议</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.autoSuggestions}
                  onChange={(e) => onUpdateSettings('autoSuggestions', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>

          {/* API状态测试 */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">API连接测试</h4>
            <p className="text-sm text-blue-700 mb-3">
              测试您的API配置是否正常工作
            </p>
            <TestAPIButton
              apiKey={apiKey}
              model={settings.model}
              temperature={settings.temperature}
              maxTokens={settings.maxTokens}
            />
          </div>
        </>
      )}
    </div>
  );
};

// 外观设置面板
const AppearanceSettingsPanel: React.FC<{
  settings: UserSettings;
  onUpdateSettings: (path: string, value: any) => void;
}> = ({ settings, onUpdateSettings }) => (
  <div className="space-y-6">
    <div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">外观设置</h3>
      <p className="text-gray-600">自定义界面主题和显示选项</p>
    </div>

    {/* 主题设置 */}
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-3">主题</label>
      <div className="grid grid-cols-3 gap-3">
        {[
          { value: 'light', name: '浅色', desc: '经典浅色主题' },
          { value: 'dark', name: '深色', desc: '护眼深色主题' },
          { value: 'auto', name: '自动', desc: '跟随系统设置' }
        ].map((theme) => (
          <label key={theme.value} className="cursor-pointer">
            <input
              type="radio"
              name="theme"
              value={theme.value}
              checked={settings.theme === theme.value}
              onChange={(e) => onUpdateSettings('theme', e.target.value)}
              className="sr-only peer"
            />
            <div className="p-4 border-2 border-gray-200 rounded-lg peer-checked:border-blue-500 peer-checked:bg-blue-50 hover:bg-gray-50 transition-colors">
              <div className="font-medium text-gray-900">{theme.name}</div>
              <div className="text-sm text-gray-600">{theme.desc}</div>
            </div>
          </label>
        ))}
      </div>
    </div>

    {/* 语言设置 */}
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">语言</label>
      <select
        value={settings.language}
        onChange={(e) => onUpdateSettings('language', e.target.value)}
        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <option value="zh-CN">简体中文</option>
        <option value="en-US">English</option>
      </select>
    </div>
  </div>
);

// 编辑器设置面板
const EditorSettingsPanel: React.FC<{
  settings: UserSettings['editor'];
  onUpdateSettings: (path: string, value: any) => void;
}> = ({ settings, onUpdateSettings }) => (
  <div className="space-y-6">
    <div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">编辑器设置</h3>
      <p className="text-gray-600">配置文档编辑器的行为和外观</p>
    </div>

    <div className="grid grid-cols-2 gap-6">
      {/* 字体大小 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          字体大小: {settings.fontSize}px
        </label>
        <input
          type="range"
          min="10"
          max="24"
          value={settings.fontSize}
          onChange={(e) => onUpdateSettings('fontSize', parseInt(e.target.value))}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
        />
      </div>

      {/* 缩进大小 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          缩进大小: {settings.tabSize}
        </label>
        <input
          type="range"
          min="2"
          max="8"
          value={settings.tabSize}
          onChange={(e) => onUpdateSettings('tabSize', parseInt(e.target.value))}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
        />
      </div>
    </div>

    {/* 编辑器选项 */}
    <div className="space-y-4">
      {[
        { key: 'wordWrap', label: '自动换行', desc: '长行自动换行显示' },
        { key: 'showLineNumbers', label: '显示行号', desc: '在编辑器左侧显示行号' },
        { key: 'autoSave', label: '自动保存', desc: '编辑时自动保存文档' }
      ].map((option) => (
        <div key={option.key} className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-gray-900">{option.label}</h4>
            <p className="text-sm text-gray-600">{option.desc}</p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={settings[option.key as keyof typeof settings] as boolean}
              onChange={(e) => onUpdateSettings(option.key, e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
          </label>
        </div>
      ))}
    </div>

    {/* 自动保存延迟 */}
    {settings.autoSave && (
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          自动保存延迟: {settings.autoSaveDelay}ms
        </label>
        <input
          type="range"
          min="500"
          max="5000"
          step="500"
          value={settings.autoSaveDelay}
          onChange={(e) => onUpdateSettings('autoSaveDelay', parseInt(e.target.value))}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
        />
      </div>
    )}
  </div>
);

// 备份设置面板
const BackupSettingsPanel: React.FC<{
  settings: UserSettings['backup'];
  onUpdateSettings: (path: string, value: any) => void;
}> = ({ settings, onUpdateSettings }) => (
  <div className="space-y-6">
    <div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">备份设置</h3>
      <p className="text-gray-600">配置数据备份和恢复选项</p>
    </div>

    {/* 自动备份 */}
    <div className="flex items-center justify-between">
      <div>
        <h4 className="font-medium text-gray-900">自动备份</h4>
        <p className="text-sm text-gray-600">定期自动备份项目数据</p>
      </div>
      <label className="relative inline-flex items-center cursor-pointer">
        <input
          type="checkbox"
          checked={settings.autoBackup}
          onChange={(e) => onUpdateSettings('autoBackup', e.target.checked)}
          className="sr-only peer"
        />
        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
      </label>
    </div>

    {settings.autoBackup && (
      <>
        {/* 备份频率 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">备份频率</label>
          <select
            value={settings.backupInterval}
            onChange={(e) => onUpdateSettings('backupInterval', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="hourly">每小时</option>
            <option value="daily">每天</option>
            <option value="weekly">每周</option>
          </select>
        </div>

        {/* 保留天数 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            保留天数: {settings.retentionDays}天
          </label>
          <input
            type="range"
            min="7"
            max="365"
            value={settings.retentionDays}
            onChange={(e) => onUpdateSettings('retentionDays', parseInt(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
        </div>

        {/* 压缩选项 */}
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-gray-900">启用压缩</h4>
            <p className="text-sm text-gray-600">压缩备份文件以节省空间</p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={settings.compressionEnabled}
              onChange={(e) => onUpdateSettings('compressionEnabled', e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
          </label>
        </div>
      </>
    )}
  </div>
);

// 高级设置面板
const AdvancedSettingsPanel: React.FC<{
  settings: UserSettings['ui'];
  onUpdateSettings: (path: string, value: any) => void;
}> = ({ settings, onUpdateSettings }) => (
  <div className="space-y-6">
    <div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">高级设置</h3>
      <p className="text-gray-600">高级用户界面和系统选项</p>
    </div>

    {/* 侧边栏宽度 */}
    <div>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        侧边栏宽度: {settings.sidebarWidth}px
      </label>
      <input
        type="range"
        min="200"
        max="400"
        step="20"
        value={settings.sidebarWidth}
        onChange={(e) => onUpdateSettings('sidebarWidth', parseInt(e.target.value))}
        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
      />
    </div>

    {/* UI选项 */}
    <div className="space-y-4">
      {[
        { key: 'showMinimap', label: '显示小地图', desc: '在编辑器右侧显示代码小地图' },
        { key: 'compactMode', label: '紧凑模式', desc: '使用更紧凑的界面布局' },
        { key: 'animationsEnabled', label: '启用动画', desc: '界面切换和交互动画效果' }
      ].map((option) => (
        <div key={option.key} className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-gray-900">{option.label}</h4>
            <p className="text-sm text-gray-600">{option.desc}</p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={settings[option.key as keyof typeof settings] as boolean}
              onChange={(e) => onUpdateSettings(option.key, e.target.checked)}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
          </label>
        </div>
      ))}
    </div>

    {/* 系统信息 */}
    <div className="bg-gray-50 p-4 rounded-lg">
      <h4 className="font-medium text-gray-900 mb-2">系统信息</h4>
      <div className="space-y-1 text-sm text-gray-600">
        <div>版本: dev-daily v2.0.0</div>
        <div>构建时间: {new Date().toLocaleDateString()}</div>
        <div>浏览器: {navigator.userAgent.split(' ')[0]}</div>
      </div>
    </div>
  </div>
);

// API测试按钮组件
const TestAPIButton: React.FC<{
  apiKey: string;
  model: string;
  temperature: number;
  maxTokens: number;
}> = ({ apiKey, model, temperature, maxTokens }) => {
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);

  const handleTest = async () => {
    if (!apiKey.trim()) {
      setTestResult({ success: false, message: '请先输入API密钥' });
      return;
    }

    setTesting(true);
    setTestResult(null);

    try {
      const service = new OpenAIService({
        apiKey,
        model,
        temperature,
        maxTokens
      });

      const result = await service.testConnection();
      setTestResult(result);
    } catch (error) {
      setTestResult({ success: false, message: `测试失败: ${error}` });
    } finally {
      setTesting(false);
    }
  };

  return (
    <div className="space-y-3">
      <button
        onClick={handleTest}
        disabled={testing}
        className={clsx(
          'px-4 py-2 rounded-md transition-colors',
          testing
            ? 'bg-gray-400 text-white cursor-not-allowed'
            : 'bg-blue-600 text-white hover:bg-blue-700'
        )}
      >
        {testing ? '测试中...' : '测试连接'}
      </button>

      {testResult && (
        <div className={clsx(
          'p-3 rounded-md text-sm',
          testResult.success
            ? 'bg-green-50 text-green-800 border border-green-200'
            : 'bg-red-50 text-red-800 border border-red-200'
        )}>
          <div className="flex items-center gap-2">
            {testResult.success ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <AlertCircle className="h-4 w-4" />
            )}
            {testResult.message}
          </div>
        </div>
      )}
    </div>
  );
};
