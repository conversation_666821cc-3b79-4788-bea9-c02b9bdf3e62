import React, { useState, useEffect, useCallback } from 'react';
import { Search, Filter, Plus, RotateCcw, Save, Download, Upload } from 'lucide-react';
import { 
  useTreeManager, 
  EditNodeDialog, 
  DraggableTreeNode 
} from './DynamicProjectTree';
import { ProjectTreeNode, Document } from '../../types/index';
import { DevDailyAdapter } from '../../services/DevDailyAdapter';
import clsx from 'clsx';

interface ProjectTreeContainerProps {
  documents: Document[];
  projectTree?: ProjectTreeNode;
  onNodeSelect?: (node: ProjectTreeNode) => void;
  onDocumentLink?: (nodeId: string, documentId: string) => void;
  className?: string;
}

// 新增节点对话框
const AddNodeDialog: React.FC<{
  isOpen: boolean;
  parentNode: ProjectTreeNode | null;
  onClose: () => void;
  onAdd: (parentId: string, node: Omit<ProjectTreeNode, 'id'>) => void;
}> = ({ isOpen, parentNode, onClose, onAdd }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'task' as ProjectTreeNode['type'],
    status: 'planned' as ProjectTreeNode['status'],
    priority: 'medium' as ProjectTreeNode['priority'],
    progress: 0,
    estimatedHours: 0,
    tags: ''
  });

  const handleAdd = () => {
    if (!parentNode || !formData.name.trim()) return;

    const newNode: Omit<ProjectTreeNode, 'id'> = {
      name: formData.name.trim(),
      description: formData.description.trim(),
      type: formData.type,
      status: formData.status,
      priority: formData.priority,
      progress: formData.progress,
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        estimatedHours: formData.estimatedHours || undefined,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
        author: 'dev-daily-user'
      }
    };

    onAdd(parentNode.id, newNode);
    
    // 重置表单
    setFormData({
      name: '',
      description: '',
      type: 'task',
      status: 'planned',
      priority: 'medium',
      progress: 0,
      estimatedHours: 0,
      tags: ''
    });
    
    onClose();
  };

  if (!isOpen || !parentNode) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-96">
        <h3 className="text-lg font-bold mb-4">添加子节点到: {parentNode.name}</h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">节点名称</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="输入节点名称"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">描述</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={2}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="输入节点描述"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">类型</label>
              <select
                value={formData.type}
                onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as ProjectTreeNode['type'] }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="module">模块</option>
                <option value="feature">功能</option>
                <option value="task">任务</option>
                <option value="document">文档</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">优先级</label>
              <select
                value={formData.priority}
                onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as ProjectTreeNode['priority'] }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="high">高</option>
                <option value="medium">中</option>
                <option value="low">低</option>
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">标签 (用逗号分隔)</label>
            <input
              type="text"
              value={formData.tags}
              onChange={(e) => setFormData(prev => ({ ...prev, tags: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="例如: frontend, react, urgent"
            />
          </div>
        </div>

        <div className="flex justify-end gap-2 mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            取消
          </button>
          <button
            onClick={handleAdd}
            disabled={!formData.name.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            <Plus size={16} />
            添加
          </button>
        </div>
      </div>
    </div>
  );
};

export const ProjectTreeContainer: React.FC<ProjectTreeContainerProps> = ({
  documents,
  projectTree: initialProjectTree,
  onNodeSelect,
  onDocumentLink,
  className
}) => {
  const [projectTree, setProjectTree] = useState<ProjectTreeNode | null>(initialProjectTree || null);
  const [isLoading, setIsLoading] = useState(!initialProjectTree);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<ProjectTreeNode['status'] | 'all'>('all');
  const [selectedNode, setSelectedNode] = useState<ProjectTreeNode | null>(null);
  const [editingNode, setEditingNode] = useState<ProjectTreeNode | null>(null);
  const [addingToNode, setAddingToNode] = useState<ProjectTreeNode | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  // 监听传入的项目树数据变化
  useEffect(() => {
    if (initialProjectTree) {
      setProjectTree(initialProjectTree);
      setIsLoading(false);
    }
  }, [initialProjectTree]);

  // 初始化项目树（仅在没有传入数据时使用）
  useEffect(() => {
    if (!initialProjectTree && documents.length > 0) {
      const initializeProjectTree = async () => {
        setIsLoading(true);
        try {
          // 从现有文档生成项目树
          const generatedTree = await DevDailyAdapter.generateProjectTreeFromDocuments(documents);
          setProjectTree(generatedTree);
        } catch (error) {
          console.error('Failed to generate project tree:', error);
          // 创建默认的项目树
          const defaultTree: ProjectTreeNode = {
            id: 'root',
            name: 'dev-daily项目',
            description: '开发日志管理系统',
            type: 'project',
            status: 'active',
            priority: 'high',
            progress: 0,
            children: [],
            metadata: {
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              author: 'dev-daily-user'
            }
          };
          setProjectTree(defaultTree);
        } finally {
          setIsLoading(false);
        }
      };

      initializeProjectTree();
    } else if (!initialProjectTree) {
      setIsLoading(false);
    }
  }, [documents, initialProjectTree]);

  const treeManager = useTreeManager(projectTree || {
    id: 'root',
    name: 'dev-daily项目',
    description: '开发日志管理系统',
    type: 'project',
    status: 'active',
    priority: 'high',
    progress: 0,
    children: [],
    metadata: {
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      author: 'dev-daily-user'
    }
  });

  // 节点点击处理
  const handleNodeClick = useCallback((node: ProjectTreeNode) => {
    setSelectedNode(node);
    onNodeSelect?.(node);
  }, [onNodeSelect]);

  // 编辑节点
  const handleEditNode = useCallback((node: ProjectTreeNode) => {
    setEditingNode(node);
    setIsEditDialogOpen(true);
  }, []);

  // 保存节点编辑
  const handleSaveNode = useCallback((updates: Partial<ProjectTreeNode>) => {
    if (editingNode) {
      treeManager.updateNode(editingNode.id, updates);
      setIsEditDialogOpen(false);
      setEditingNode(null);
    }
  }, [editingNode, treeManager]);

  // 添加子节点
  const handleAddChild = useCallback((parentId: string) => {
    const parentNode = treeManager.findNodeById(parentId);
    if (parentNode) {
      setAddingToNode(parentNode);
      setIsAddDialogOpen(true);
    }
  }, [treeManager]);

  // 保存新节点
  const handleSaveNewNode = useCallback((parentId: string, newNode: Omit<ProjectTreeNode, 'id'>) => {
    treeManager.addNode(parentId, newNode);
    setIsAddDialogOpen(false);
    setAddingToNode(null);
  }, [treeManager]);

  // 删除节点
  const handleDeleteNode = useCallback((nodeId: string) => {
    if (window.confirm('确定要删除这个节点吗？这将同时删除所有子节点。')) {
      treeManager.deleteNode(nodeId);
    }
  }, [treeManager]);

  // 重新生成项目树
  const handleRegenerateTree = useCallback(async () => {
    if (window.confirm('确定要重新生成项目树吗？这将覆盖当前的所有修改。')) {
      setIsLoading(true);
      try {
        const newTree = await DevDailyAdapter.generateProjectTreeFromDocuments(documents);
        setProjectTree(newTree);
      } catch (error) {
        console.error('Failed to regenerate project tree:', error);
      } finally {
        setIsLoading(false);
      }
    }
  }, [documents]);

  if (isLoading) {
    return (
      <div className={clsx('flex items-center justify-center h-64', className)}>
        <div className="text-gray-500">正在生成项目树...</div>
      </div>
    );
  }

  if (!projectTree) {
    return (
      <div className={clsx('flex items-center justify-center h-64', className)}>
        <div className="text-gray-500">无法生成项目树</div>
      </div>
    );
  }

  return (
    <div className={clsx('flex flex-col h-full', className)}>
      {/* 工具栏 */}
      <div className="flex items-center gap-4 p-4 border-b border-gray-200">
        <div className="flex-1 relative">
          <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="搜索节点..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value as ProjectTreeNode['status'] | 'all')}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">所有状态</option>
          <option value="planned">计划中</option>
          <option value="active">进行中</option>
          <option value="testing">测试中</option>
          <option value="completed">已完成</option>
          <option value="blocked">阻塞</option>
          <option value="paused">暂停</option>
        </select>

        <button
          onClick={handleRegenerateTree}
          className="p-2 text-gray-600 hover:bg-gray-100 rounded-md"
          title="重新生成项目树"
        >
          <RotateCcw size={20} />
        </button>
      </div>

      {/* 项目树 */}
      <div className="flex-1 overflow-auto p-4">
        <DraggableTreeNode
          node={treeManager.treeData}
          level={0}
          onNodeClick={handleNodeClick}
          onEditNode={handleEditNode}
          onDeleteNode={handleDeleteNode}
          onAddChild={handleAddChild}
          draggedNode={treeManager.draggedNode}
          setDraggedNode={treeManager.setDraggedNode}
          onMoveNode={treeManager.moveNode}
          linkedDocuments={documents.map(d => d.filename)}
        />
      </div>

      {/* 编辑对话框 */}
      <EditNodeDialog
        node={editingNode}
        isOpen={isEditDialogOpen}
        onClose={() => {
          setIsEditDialogOpen(false);
          setEditingNode(null);
        }}
        onSave={handleSaveNode}
      />

      {/* 添加对话框 */}
      <AddNodeDialog
        isOpen={isAddDialogOpen}
        parentNode={addingToNode}
        onClose={() => {
          setIsAddDialogOpen(false);
          setAddingToNode(null);
        }}
        onAdd={handleSaveNewNode}
      />
    </div>
  );
};
