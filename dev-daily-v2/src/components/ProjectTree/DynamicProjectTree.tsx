import React, { useState, useCallback } from 'react';
import { 
  ChevronDown, 
  ChevronRight, 
  Circle, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Edit3,
  Plus,
  Trash2,
  Save,
  X,
  FileText,
  Folder,
  Target
} from 'lucide-react';
import clsx from 'clsx';
import { ProjectTreeNode, ChangeRecord } from '../../types/index';

// 动态树管理Hook - 适配dev-daily
const useTreeManager = (initialData: ProjectTreeNode) => {
  const [treeData, setTreeData] = useState<ProjectTreeNode>(initialData);
  const [changeHistory, setChangeHistory] = useState<ChangeRecord[]>([]);
  const [draggedNode, setDraggedNode] = useState<string | null>(null);

  const addChangeRecord = useCallback((
    action: ChangeRecord['action'], 
    entityId: string, 
    details: string, 
    oldValue?: any, 
    newValue?: any
  ) => {
    const record: ChangeRecord = {
      id: `change-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      action,
      entityType: 'node',
      entityId,
      details,
      oldValue,
      newValue,
      author: 'dev-daily-user'
    };
    setChangeHistory(prev => [record, ...prev.slice(0, 49)]); // 保留最近50条记录
  }, []);

  const findNodeById = useCallback((id: string, node: ProjectTreeNode = treeData): ProjectTreeNode | null => {
    if (node.id === id) return node;
    if (node.children) {
      for (const child of node.children) {
        const found = findNodeById(id, child);
        if (found) return found;
      }
    }
    return null;
  }, [treeData]);

  const updateNode = useCallback((id: string, updates: Partial<ProjectTreeNode>) => {
    const updateNodeRecursive = (node: ProjectTreeNode): ProjectTreeNode => {
      if (node.id === id) {
        const oldValue = { ...node };
        const newValue = { 
          ...node, 
          ...updates,
          metadata: {
            ...node.metadata,
            ...updates.metadata,
            updatedAt: new Date().toISOString()
          }
        };
        addChangeRecord('update', id, `Updated node: ${Object.keys(updates).join(', ')}`, oldValue, newValue);
        return newValue;
      }
      if (node.children) {
        return {
          ...node,
          children: node.children.map(updateNodeRecursive)
        };
      }
      return node;
    };
    setTreeData(updateNodeRecursive);
  }, [addChangeRecord]);

  const addNode = useCallback((parentId: string, newNode: Omit<ProjectTreeNode, 'id'>) => {
    const nodeWithId: ProjectTreeNode = {
      ...newNode,
      id: `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      parent: parentId,
      metadata: {
        ...newNode.metadata,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        author: 'dev-daily-user'
      }
    };

    const addToNode = (node: ProjectTreeNode): ProjectTreeNode => {
      if (node.id === parentId) {
        const updatedNode = {
          ...node,
          children: [...(node.children || []), nodeWithId]
        };
        addChangeRecord('create', nodeWithId.id, `Created new node under ${node.name}`, null, nodeWithId);
        return updatedNode;
      }
      if (node.children) {
        return {
          ...node,
          children: node.children.map(addToNode)
        };
      }
      return node;
    };
    setTreeData(addToNode);
    return nodeWithId.id;
  }, [addChangeRecord]);

  const deleteNode = useCallback((id: string) => {
    const nodeToDelete = findNodeById(id);
    if (!nodeToDelete) return;

    const deleteFromNode = (node: ProjectTreeNode): ProjectTreeNode => {
      if (node.children) {
        const filteredChildren = node.children.filter(child => child.id !== id);
        if (filteredChildren.length !== node.children.length) {
          addChangeRecord('delete', id, `Deleted node: ${nodeToDelete.name}`, nodeToDelete, null);
        }
        return {
          ...node,
          children: filteredChildren.map(deleteFromNode)
        };
      }
      return node;
    };
    setTreeData(deleteFromNode);
  }, [findNodeById, addChangeRecord]);

  const moveNode = useCallback((nodeId: string, newParentId: string) => {
    const nodeToMove = findNodeById(nodeId);
    if (!nodeToMove || nodeId === newParentId) return;

    // 检查是否会创建循环引用
    const wouldCreateCycle = (checkId: string, targetId: string): boolean => {
      const node = findNodeById(checkId);
      if (!node) return false;
      if (node.id === targetId) return true;
      return node.children?.some(child => wouldCreateCycle(child.id, targetId)) || false;
    };

    if (wouldCreateCycle(newParentId, nodeId)) {
      console.warn('Cannot move node: would create cycle');
      return;
    }

    // 首先从原位置删除
    const removeFromNode = (node: ProjectTreeNode): ProjectTreeNode => {
      if (node.children) {
        return {
          ...node,
          children: node.children.filter(child => child.id !== nodeId).map(removeFromNode)
        };
      }
      return node;
    };

    // 然后添加到新位置
    const addToNode = (node: ProjectTreeNode): ProjectTreeNode => {
      if (node.id === newParentId) {
        return {
          ...node,
          children: [...(node.children || []), { 
            ...nodeToMove, 
            parent: newParentId,
            metadata: {
              ...nodeToMove.metadata,
              updatedAt: new Date().toISOString()
            }
          }]
        };
      }
      if (node.children) {
        return {
          ...node,
          children: node.children.map(addToNode)
        };
      }
      return node;
    };

    const tempTree = removeFromNode(treeData);
    const finalTree = addToNode(tempTree);
    
    setTreeData(finalTree);
    addChangeRecord('move', nodeId, `Moved ${nodeToMove.name} to new parent`, nodeToMove.parent, newParentId);
  }, [findNodeById, treeData, addChangeRecord]);

  return {
    treeData,
    changeHistory,
    draggedNode,
    setDraggedNode,
    updateNode,
    addNode,
    deleteNode,
    moveNode,
    findNodeById
  };
};

// 编辑节点对话框组件
const EditNodeDialog: React.FC<{
  node: ProjectTreeNode | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (updates: Partial<ProjectTreeNode>) => void;
}> = ({ node, isOpen, onClose, onSave }) => {
  const [formData, setFormData] = useState<Partial<ProjectTreeNode>>({});

  React.useEffect(() => {
    if (node) {
      setFormData({
        name: node.name,
        description: node.description,
        type: node.type,
        status: node.status,
        priority: node.priority,
        progress: node.progress,
        metadata: {
          ...node.metadata,
          estimatedHours: node.metadata.estimatedHours,
          actualHours: node.metadata.actualHours,
          tags: node.metadata.tags || []
        }
      });
    }
  }, [node]);

  if (!isOpen || !node) return null;

  const handleSave = () => {
    onSave(formData);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-96 max-h-[80vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-bold">编辑节点</h3>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={20} />
          </button>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">节点名称</label>
            <input
              type="text"
              value={formData.name || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">描述</label>
            <textarea
              value={formData.description || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">类型</label>
              <select
                value={formData.type || 'task'}
                onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as ProjectTreeNode['type'] }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="project">项目</option>
                <option value="module">模块</option>
                <option value="feature">功能</option>
                <option value="task">任务</option>
                <option value="document">文档</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">状态</label>
              <select
                value={formData.status || 'planned'}
                onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as ProjectTreeNode['status'] }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="planned">计划中</option>
                <option value="active">进行中</option>
                <option value="testing">测试中</option>
                <option value="completed">已完成</option>
                <option value="blocked">阻塞</option>
                <option value="paused">暂停</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">优先级</label>
              <select
                value={formData.priority || 'medium'}
                onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as ProjectTreeNode['priority'] }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="high">高</option>
                <option value="medium">中</option>
                <option value="low">低</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">进度 (%)</label>
              <input
                type="number"
                min="0"
                max="100"
                value={formData.progress || 0}
                onChange={(e) => setFormData(prev => ({ ...prev, progress: parseInt(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">预估工时</label>
              <input
                type="number"
                min="0"
                value={formData.metadata?.estimatedHours || 0}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  metadata: {
                    createdAt: prev.metadata?.createdAt || new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                    ...prev.metadata,
                    estimatedHours: parseInt(e.target.value) || 0
                  }
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">实际工时</label>
              <input
                type="number"
                min="0"
                value={formData.metadata?.actualHours || 0}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  metadata: {
                    createdAt: prev.metadata?.createdAt || new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                    ...prev.metadata,
                    actualHours: parseInt(e.target.value) || 0
                  }
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">标签 (用逗号分隔)</label>
            <input
              type="text"
              value={formData.metadata?.tags?.join(', ') || ''}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                metadata: {
                  createdAt: prev.metadata?.createdAt || new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                  ...prev.metadata,
                  tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
                }
              }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="例如: frontend, react, urgent"
            />
          </div>
        </div>

        <div className="flex justify-end gap-2 mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            取消
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center gap-2"
          >
            <Save size={16} />
            保存
          </button>
        </div>
      </div>
    </div>
  );
};

// 状态图标组件
const StatusIcon: React.FC<{ status: ProjectTreeNode['status'] }> = ({ status }) => {
  const iconProps = { size: 16 };

  switch (status) {
    case 'completed':
      return <CheckCircle {...iconProps} className="text-green-500" />;
    case 'active':
      return <Clock {...iconProps} className="text-blue-500" />;
    case 'testing':
      return <Target {...iconProps} className="text-purple-500" />;
    case 'blocked':
      return <AlertCircle {...iconProps} className="text-red-500" />;
    case 'paused':
      return <Circle {...iconProps} className="text-yellow-500" />;
    default:
      return <Circle {...iconProps} className="text-gray-400" />;
  }
};

// 类型图标组件
const TypeIcon: React.FC<{ type: ProjectTreeNode['type'] }> = ({ type }) => {
  const iconProps = { size: 16 };

  switch (type) {
    case 'project':
      return <Folder {...iconProps} className="text-blue-600" />;
    case 'module':
      return <Folder {...iconProps} className="text-green-600" />;
    case 'feature':
      return <Target {...iconProps} className="text-purple-600" />;
    case 'document':
      return <FileText {...iconProps} className="text-orange-600" />;
    default:
      return <Circle {...iconProps} className="text-gray-600" />;
  }
};

// 优先级标签组件
const PriorityBadge: React.FC<{ priority: ProjectTreeNode['priority'] }> = ({ priority }) => {
  const colors = {
    high: 'bg-red-100 text-red-800 border-red-200',
    medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    low: 'bg-gray-100 text-gray-800 border-gray-200'
  };

  const labels = {
    high: '高',
    medium: '中',
    low: '低'
  };

  return (
    <span className={clsx(
      'px-2 py-1 text-xs font-medium rounded-full border',
      colors[priority]
    )}>
      {labels[priority]}
    </span>
  );
};

// 进度条组件
const ProgressBar: React.FC<{ progress: number; className?: string }> = ({ progress, className }) => {
  const getProgressColor = (progress: number) => {
    if (progress >= 100) return 'bg-green-500';
    if (progress >= 75) return 'bg-blue-500';
    if (progress >= 50) return 'bg-yellow-500';
    if (progress >= 25) return 'bg-orange-500';
    return 'bg-red-500';
  };

  return (
    <div className={clsx('w-full bg-gray-200 rounded-full h-2', className)}>
      <div
        className={clsx('h-2 rounded-full transition-all duration-300', getProgressColor(progress))}
        style={{ width: `${Math.min(progress, 100)}%` }}
      />
    </div>
  );
};

// 可拖拽的树节点组件
const DraggableTreeNode: React.FC<{
  node: ProjectTreeNode;
  level: number;
  onNodeClick: (node: ProjectTreeNode) => void;
  onEditNode: (node: ProjectTreeNode) => void;
  onDeleteNode: (id: string) => void;
  onAddChild: (parentId: string) => void;
  draggedNode: string | null;
  setDraggedNode: (id: string | null) => void;
  onMoveNode: (nodeId: string, newParentId: string) => void;
  linkedDocuments?: string[];
}> = ({
  node,
  level,
  onNodeClick,
  onEditNode,
  onDeleteNode,
  onAddChild,
  draggedNode,
  setDraggedNode,
  onMoveNode,
  linkedDocuments = []
}) => {
  const [isExpanded, setIsExpanded] = useState(level < 2);
  const [isDragOver, setIsDragOver] = useState(false);
  const hasChildren = node.children && node.children.length > 0;
  const hasLinkedDocs = node.metadata.linkedDocuments && node.metadata.linkedDocuments.length > 0;

  const handleDragStart = (e: React.DragEvent) => {
    setDraggedNode(node.id);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setIsDragOver(true);
  };

  const handleDragLeave = () => {
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    if (draggedNode && draggedNode !== node.id) {
      onMoveNode(draggedNode, node.id);
    }
    setDraggedNode(null);
  };

  const handleDragEnd = () => {
    setDraggedNode(null);
  };

  return (
    <div className="select-none">
      <div
        draggable
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onDragEnd={handleDragEnd}
        className={clsx(
          'flex items-center py-2 px-3 rounded-lg cursor-pointer transition-all group',
          level === 0 && 'bg-blue-50 border border-blue-200',
          isDragOver && 'bg-green-100 border-green-300',
          draggedNode === node.id && 'opacity-50',
          'hover:bg-gray-50'
        )}
        style={{ marginLeft: `${level * 20}px` }}
        onClick={() => onNodeClick(node)}
      >
        {/* 展开/收起按钮 */}
        <button
          className="mr-2 p-1 hover:bg-gray-200 rounded"
          onClick={(e) => {
            e.stopPropagation();
            setIsExpanded(!isExpanded);
          }}
        >
          {hasChildren ? (
            isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />
          ) : (
            <div className="w-4 h-4" />
          )}
        </button>

        {/* 类型图标 */}
        <TypeIcon type={node.type} />

        {/* 状态图标 */}
        <div className="ml-2">
          <StatusIcon status={node.status} />
        </div>

        {/* 节点信息 */}
        <div className="flex-1 ml-3">
          <div className="flex items-center gap-2">
            <span className="font-medium text-gray-900">{node.name}</span>
            <PriorityBadge priority={node.priority} />
            {hasLinkedDocs && (
              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                {node.metadata.linkedDocuments!.length} 文档
              </span>
            )}
          </div>
          <p className="text-sm text-gray-600 mt-1">{node.description}</p>

          {/* 进度信息 */}
          <div className="flex items-center gap-4 mt-2">
            <div className="flex-1">
              <ProgressBar progress={node.progress} />
            </div>
            <span className="text-sm text-gray-500">{node.progress}%</span>
            {node.metadata.estimatedHours && (
              <span className="text-sm text-gray-500">
                {node.metadata.actualHours || 0}h / {node.metadata.estimatedHours}h
              </span>
            )}
          </div>

          {/* 标签 */}
          {node.metadata.tags && node.metadata.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {node.metadata.tags.slice(0, 3).map((tag, index) => (
                <span key={index} className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                  {tag}
                </span>
              ))}
              {node.metadata.tags.length > 3 && (
                <span className="text-xs text-gray-500">+{node.metadata.tags.length - 3}</span>
              )}
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onAddChild(node.id);
            }}
            className="p-1 text-green-600 hover:bg-green-100 rounded"
            title="添加子节点"
          >
            <Plus size={16} />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onEditNode(node);
            }}
            className="p-1 text-blue-600 hover:bg-blue-100 rounded"
            title="编辑节点"
          >
            <Edit3 size={16} />
          </button>
          {level > 0 && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDeleteNode(node.id);
              }}
              className="p-1 text-red-600 hover:bg-red-100 rounded"
              title="删除节点"
            >
              <Trash2 size={16} />
            </button>
          )}
        </div>
      </div>

      {/* 子节点 */}
      {hasChildren && isExpanded && (
        <div className="mt-1">
          {node.children!.map((child) => (
            <DraggableTreeNode
              key={child.id}
              node={child}
              level={level + 1}
              onNodeClick={onNodeClick}
              onEditNode={onEditNode}
              onDeleteNode={onDeleteNode}
              onAddChild={onAddChild}
              draggedNode={draggedNode}
              setDraggedNode={setDraggedNode}
              onMoveNode={onMoveNode}
              linkedDocuments={linkedDocuments}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export {
  useTreeManager,
  EditNodeDialog,
  DraggableTreeNode,
  StatusIcon,
  TypeIcon,
  PriorityBadge,
  ProgressBar
};
export type { ProjectTreeNode, ChangeRecord };
