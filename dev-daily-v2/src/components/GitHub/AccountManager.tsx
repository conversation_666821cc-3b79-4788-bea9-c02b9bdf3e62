/**
 * GitHub 账号管理组件 - 多账号绑定和管理
 */

import React, { useState, useEffect } from 'react';
import { 
  Plus, 
  User, 
  Building, 
  Star, 
  GitFork, 
  Eye, 
  EyeOff,
  Settings,
  Trash2,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Crown,
  Shield,
  Calendar,
  BarChart3
} from 'lucide-react';
import { 
  GitHubAccountManager, 
  GitHubAccount, 
  AccountStats 
} from '../../services/GitHubAccountManager';

export const AccountManager: React.FC = () => {
  const [accounts, setAccounts] = useState<GitHubAccount[]>([]);
  const [activeAccount, setActiveAccount] = useState<GitHubAccount | null>(null);
  const [accountStats, setAccountStats] = useState<Record<string, AccountStats>>({});
  const [showAddForm, setShowAddForm] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadAccounts();
  }, []);

  const loadAccounts = async () => {
    const allAccounts = GitHubAccountManager.getAllAccounts();
    const active = GitHubAccountManager.getActiveAccount();
    
    setAccounts(allAccounts);
    setActiveAccount(active);

    // 加载账号统计信息
    const stats: Record<string, AccountStats> = {};
    for (const account of allAccounts) {
      const accountStat = await GitHubAccountManager.getAccountStats(account.id);
      if (accountStat) {
        stats[account.id] = accountStat;
      }
    }
    setAccountStats(stats);
  };

  const handleAddAccount = async (token: string, tokenName: string) => {
    setIsLoading(true);
    try {
      const result = await GitHubAccountManager.addAccount(token, tokenName);
      if (result.success) {
        await loadAccounts();
        setShowAddForm(false);
      } else {
        alert(`添加账号失败: ${result.error}`);
      }
    } catch (error) {
      alert(`添加账号失败: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSetActiveAccount = async (accountId: string) => {
    const success = GitHubAccountManager.setActiveAccount(accountId);
    if (success) {
      await loadAccounts();
    }
  };

  const handleRemoveAccount = async (accountId: string) => {
    if (confirm('确定要删除这个账号吗？')) {
      const success = GitHubAccountManager.removeAccount(accountId);
      if (success) {
        await loadAccounts();
      }
    }
  };

  const handleUpdateAccount = async (accountId: string) => {
    setIsLoading(true);
    try {
      const result = await GitHubAccountManager.updateAccount(accountId);
      if (result.success) {
        await loadAccounts();
      } else {
        alert(`更新账号失败: ${result.error}`);
      }
    } catch (error) {
      alert(`更新账号失败: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const getAccountTypeIcon = (type: string) => {
    return type === 'Organization' ? <Building className="w-4 h-4" /> : <User className="w-4 h-4" />;
  };

  const getAccountTypeBadge = (type: string) => {
    return type === 'Organization' ? (
      <span className="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full">组织</span>
    ) : (
      <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">个人</span>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">GitHub 账号管理</h1>
          <p className="text-gray-600">
            管理多个 GitHub 账号，统一发现和同步项目
          </p>
        </div>
        
        <button
          onClick={() => setShowAddForm(true)}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          <Plus className="w-4 h-4" />
          <span>添加账号</span>
        </button>
      </div>

      {/* 账号概览 */}
      {accounts.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-blue-600">{accounts.length}</div>
            <div className="text-sm text-blue-800">已绑定账号</div>
          </div>
          
          <div className="bg-green-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-green-600">
              {Object.values(accountStats).reduce((sum, stats) => sum + stats.totalRepos, 0)}
            </div>
            <div className="text-sm text-green-800">总仓库数</div>
          </div>
          
          <div className="bg-yellow-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-yellow-600">
              {Object.values(accountStats).reduce((sum, stats) => sum + stats.totalStars, 0)}
            </div>
            <div className="text-sm text-yellow-800">总 Stars 数</div>
          </div>
        </div>
      )}

      {/* 账号列表 */}
      {accounts.length === 0 ? (
        <div className="text-center py-12">
          <User className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 mb-4">还没有绑定任何 GitHub 账号</p>
          <button
            onClick={() => setShowAddForm(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            添加第一个账号
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          {accounts.map((account) => {
            const stats = accountStats[account.id];
            return (
              <div
                key={account.id}
                className={`bg-white rounded-lg border p-6 transition-colors ${
                  account.isActive ? 'border-blue-300 bg-blue-50' : 'border-gray-200'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    {/* 头像 */}
                    <img
                      src={account.avatar_url}
                      alt={account.name}
                      className="w-12 h-12 rounded-full"
                    />
                    
                    <div className="flex-1">
                      {/* 基本信息 */}
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {account.name}
                        </h3>
                        <span className="text-gray-500">@{account.login}</span>
                        {getAccountTypeBadge(account.type)}
                        {account.isActive && (
                          <div className="flex items-center space-x-1 text-blue-600">
                            <Crown className="w-4 h-4" />
                            <span className="text-xs font-medium">活跃账号</span>
                          </div>
                        )}
                      </div>
                      
                      {/* Token 信息 */}
                      <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                        <div className="flex items-center space-x-1">
                          <Shield className="w-4 h-4" />
                          <span>Token: {account.tokenName}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4" />
                          <span>添加于: {formatDate(account.addedAt)}</span>
                        </div>
                        {account.lastUsed && (
                          <div className="flex items-center space-x-1">
                            <RefreshCw className="w-4 h-4" />
                            <span>最后使用: {formatDate(account.lastUsed)}</span>
                          </div>
                        )}
                      </div>

                      {/* 统计信息 */}
                      {stats && (
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                          <div className="text-center">
                            <div className="text-lg font-semibold text-gray-900">{stats.totalRepos}</div>
                            <div className="text-xs text-gray-500">仓库</div>
                          </div>
                          <div className="text-center">
                            <div className="text-lg font-semibold text-yellow-600">{stats.totalStars}</div>
                            <div className="text-xs text-gray-500">Stars</div>
                          </div>
                          <div className="text-center">
                            <div className="text-lg font-semibold text-blue-600">{stats.totalForks}</div>
                            <div className="text-xs text-gray-500">Forks</div>
                          </div>
                          <div className="text-center">
                            <div className="text-lg font-semibold text-red-600">{stats.totalIssues}</div>
                            <div className="text-xs text-gray-500">Issues</div>
                          </div>
                        </div>
                      )}

                      {/* 编程语言 */}
                      {stats && Object.keys(stats.languages).length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-3">
                          {Object.entries(stats.languages)
                            .sort(([,a], [,b]) => b - a)
                            .slice(0, 5)
                            .map(([lang, count]) => (
                              <span
                                key={lang}
                                className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full"
                              >
                                {lang} ({count})
                              </span>
                            ))}
                        </div>
                      )}

                      {/* 组织信息 */}
                      {account.organizations && account.organizations.length > 0 && (
                        <div className="mb-3">
                          <div className="text-sm text-gray-600 mb-1">组织成员:</div>
                          <div className="flex flex-wrap gap-2">
                            {account.organizations.slice(0, 3).map((org) => (
                              <div key={org.id} className="flex items-center space-x-1 text-xs text-gray-600">
                                <img src={org.avatar_url} alt={org.login} className="w-4 h-4 rounded-full" />
                                <span>{org.login}</span>
                              </div>
                            ))}
                            {account.organizations.length > 3 && (
                              <span className="text-xs text-gray-500">
                                +{account.organizations.length - 3} 个组织
                              </span>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Token 权限 */}
                      <div className="text-xs text-gray-500">
                        权限: {account.permissions.join(', ') || '未知'}
                      </div>
                    </div>
                  </div>
                  
                  {/* 操作按钮 */}
                  <div className="flex items-center space-x-2">
                    {!account.isActive && (
                      <button
                        onClick={() => handleSetActiveAccount(account.id)}
                        className="flex items-center space-x-1 px-3 py-1 text-sm bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-md"
                      >
                        <Crown className="w-4 h-4" />
                        <span>设为活跃</span>
                      </button>
                    )}
                    
                    <button
                      onClick={() => handleUpdateAccount(account.id)}
                      disabled={isLoading}
                      className="flex items-center space-x-1 px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md disabled:opacity-50"
                    >
                      <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
                      <span>更新</span>
                    </button>
                    
                    <button
                      onClick={() => handleRemoveAccount(account.id)}
                      className="p-1 text-red-400 hover:text-red-600"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* 添加账号表单 */}
      {showAddForm && (
        <AddAccountForm
          onSave={handleAddAccount}
          onCancel={() => setShowAddForm(false)}
          isLoading={isLoading}
        />
      )}
    </div>
  );
};

// 添加账号表单组件
const AddAccountForm: React.FC<{
  onSave: (token: string, tokenName: string) => void;
  onCancel: () => void;
  isLoading: boolean;
}> = ({ onSave, onCancel, isLoading }) => {
  const [token, setToken] = useState('');
  const [tokenName, setTokenName] = useState('');
  const [showToken, setShowToken] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (token && tokenName) {
      onSave(token, tokenName);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 className="text-lg font-semibold mb-4">添加 GitHub 账号</h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Token 名称
            </label>
            <input
              type="text"
              value={tokenName}
              onChange={(e) => setTokenName(e.target.value)}
              placeholder="例如：个人账号、工作账号"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              GitHub Personal Access Token
            </label>
            <div className="relative">
              <input
                type={showToken ? 'text' : 'password'}
                value={token}
                onChange={(e) => setToken(e.target.value)}
                placeholder="ghp_xxxxxxxxxxxxxxxxxxxx"
                className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
              <button
                type="button"
                onClick={() => setShowToken(!showToken)}
                className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
              >
                {showToken ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              需要 repo、user、read:org 权限
            </p>
          </div>

          <div className="bg-blue-50 rounded-lg p-3">
            <h4 className="text-sm font-medium text-blue-900 mb-2">如何获取 Token？</h4>
            <ol className="text-xs text-blue-800 space-y-1">
              <li>1. 访问 GitHub Settings → Developer settings → Personal access tokens</li>
              <li>2. 点击 "Generate new token (classic)"</li>
              <li>3. 选择权限：repo, user, read:org</li>
              <li>4. 复制生成的 Token</li>
            </ol>
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="submit"
              disabled={isLoading || !token || !tokenName}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? '验证中...' : '添加账号'}
            </button>
            <button
              type="button"
              onClick={onCancel}
              className="flex-1 px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
            >
              取消
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
