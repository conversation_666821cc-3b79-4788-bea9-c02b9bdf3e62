/**
 * GitHub CLI 状态检查和配置组件
 */

import React, { useState, useEffect } from 'react';
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Download, 
  Terminal, 
  RefreshCw,
  ExternalLink,
  User,
  Settings
} from 'lucide-react';
import { GitHubCLIService } from '../../services/GitHubCLIService';

interface CLIStatus {
  installed: boolean;
  authenticated: boolean;
  user?: string;
  version?: string;
}

export const GitHubCLIStatus: React.FC = () => {
  const [status, setStatus] = useState<CLIStatus>({
    installed: false,
    authenticated: false
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const checkStatus = async () => {
    setIsRefreshing(true);
    try {
      const cliStatus = await GitHubCLIService.checkCLIStatus();
      setStatus(cliStatus);
    } catch (error) {
      console.error('检查 GitHub CLI 状态失败:', error);
      setStatus({
        installed: false,
        authenticated: false
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    checkStatus();
  }, []);

  const getInstallationGuide = () => {
    return GitHubCLIService.getInstallationGuide();
  };

  const renderStatusIcon = (condition: boolean, loading: boolean = false) => {
    if (loading) {
      return <RefreshCw className="w-5 h-5 text-blue-500 animate-spin" />;
    }
    return condition ? (
      <CheckCircle className="w-5 h-5 text-green-500" />
    ) : (
      <XCircle className="w-5 h-5 text-red-500" />
    );
  };

  const installationGuide = getInstallationGuide();

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-center">
          <RefreshCw className="w-6 h-6 text-blue-500 animate-spin mr-2" />
          <span className="text-gray-600">检查 GitHub CLI 状态...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 状态概览 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">GitHub CLI 状态</h2>
          <button
            onClick={checkStatus}
            disabled={isRefreshing}
            className="flex items-center space-x-2 px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            <span>刷新</span>
          </button>
        </div>

        <div className="space-y-4">
          {/* 安装状态 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {renderStatusIcon(status.installed, isRefreshing)}
              <div>
                <p className="font-medium text-gray-900">GitHub CLI 安装</p>
                <p className="text-sm text-gray-500">
                  {status.installed ? `已安装 ${status.version || ''}` : '未安装'}
                </p>
              </div>
            </div>
            {!status.installed && (
              <a
                href={installationGuide.downloadUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <Download className="w-4 h-4" />
                <span>下载</span>
                <ExternalLink className="w-3 h-3" />
              </a>
            )}
          </div>

          {/* 认证状态 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {renderStatusIcon(status.authenticated, isRefreshing)}
              <div>
                <p className="font-medium text-gray-900">GitHub 认证</p>
                <p className="text-sm text-gray-500">
                  {status.authenticated ? `已登录为 ${status.user}` : '未认证'}
                </p>
              </div>
            </div>
            {status.installed && !status.authenticated && (
              <div className="flex items-center space-x-2 text-sm text-blue-600">
                <Terminal className="w-4 h-4" />
                <span>运行 "gh auth login"</span>
              </div>
            )}
          </div>
        </div>

        {/* 整体状态指示 */}
        <div className="mt-6 p-4 rounded-lg border">
          {status.installed && status.authenticated ? (
            <div className="flex items-center space-x-2 text-green-700">
              <CheckCircle className="w-5 h-5" />
              <span className="font-medium">GitHub CLI 已就绪</span>
            </div>
          ) : (
            <div className="flex items-center space-x-2 text-amber-700">
              <AlertCircle className="w-5 h-5" />
              <span className="font-medium">需要完成 GitHub CLI 设置</span>
            </div>
          )}
        </div>
      </div>

      {/* 安装指南 */}
      {!status.installed && (
        <div className="bg-blue-50 rounded-lg border border-blue-200 p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-4">
            {installationGuide.platform} 安装指南
          </h3>
          <ol className="space-y-2 text-sm text-blue-800">
            {installationGuide.instructions.map((instruction, index) => (
              <li key={index} className="flex items-start space-x-2">
                <span className="flex-shrink-0 w-5 h-5 bg-blue-200 text-blue-800 rounded-full flex items-center justify-center text-xs font-medium">
                  {index + 1}
                </span>
                <span>{instruction}</span>
              </li>
            ))}
          </ol>
          <div className="mt-4 pt-4 border-t border-blue-200">
            <a
              href={installationGuide.downloadUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Download className="w-4 h-4" />
              <span>下载 GitHub CLI</span>
              <ExternalLink className="w-3 h-3" />
            </a>
          </div>
        </div>
      )}

      {/* 认证指南 */}
      {status.installed && !status.authenticated && (
        <div className="bg-amber-50 rounded-lg border border-amber-200 p-6">
          <h3 className="text-lg font-semibold text-amber-900 mb-4">
            GitHub 认证设置
          </h3>
          <div className="space-y-3 text-sm text-amber-800">
            <p>请在终端中运行以下命令完成 GitHub 认证：</p>
            <div className="bg-gray-900 text-green-400 p-3 rounded-md font-mono">
              gh auth login
            </div>
            <p>认证完成后，点击上方的"刷新"按钮更新状态。</p>
          </div>
        </div>
      )}

      {/* 功能说明 */}
      {status.installed && status.authenticated && (
        <div className="bg-green-50 rounded-lg border border-green-200 p-6">
          <h3 className="text-lg font-semibold text-green-900 mb-4">
            可用功能
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <h4 className="font-medium text-green-800">仓库管理</h4>
              <ul className="space-y-1 text-green-700">
                <li>• 克隆和初始化仓库</li>
                <li>• 推送和拉取代码</li>
                <li>• 分支管理</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-green-800">协作功能</h4>
              <ul className="space-y-1 text-green-700">
                <li>• 创建 Pull Request</li>
                <li>• 设置 Webhook</li>
                <li>• 自动化工作流</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
