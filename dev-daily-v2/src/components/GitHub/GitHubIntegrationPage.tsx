/**
 * GitHub 集成管理页面
 */

import React, { useState } from 'react';
import {
  Github,
  Settings,
  Activity,
  Terminal,
  BookOpen,
  Search,
  User
} from 'lucide-react';
import { GitHubCLIStatus } from './GitHubCLIStatus';
import { GitHubProjectConfig } from './GitHubProjectConfig';
import { ProjectDiscovery } from './ProjectDiscovery';
import { AccountManager } from './AccountManager';

type TabType = 'accounts' | 'status' | 'config' | 'discovery' | 'activity' | 'docs';

export const GitHubIntegrationPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('status');

  const tabs = [
    { id: 'accounts' as TabType, name: '账号管理', icon: User },
    { id: 'status' as TabType, name: 'CLI 状态', icon: Terminal },
    { id: 'config' as TabType, name: '项目配置', icon: Settings },
    { id: 'discovery' as TabType, name: '项目发现', icon: Search },
    { id: 'activity' as TabType, name: '同步活动', icon: Activity },
    { id: 'docs' as TabType, name: '使用文档', icon: BookOpen }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'accounts':
        return <AccountManager />;

      case 'status':
        return <GitHubCLIStatus />;

      case 'config':
        return <GitHubProjectConfig />;

      case 'discovery':
        return <ProjectDiscovery />;

      case 'activity':
        return <SyncActivityView />;

      case 'docs':
        return <DocumentationView />;

      default:
        return <AccountManager />;
    }
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="max-w-6xl mx-auto p-6">
        {/* 页面头部 */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Github className="w-8 h-8 text-gray-900" />
            <h1 className="text-3xl font-bold text-gray-900">GitHub 集成</h1>
          </div>
          <p className="text-gray-600">
            配置和管理项目的 GitHub 集成，实现本地与远程的无缝同步
          </p>
        </div>

        {/* 标签页导航 */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* 标签页内容 */}
        <div className="min-h-96 pb-8">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
};

// 同步活动视图组件
const SyncActivityView: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">同步活动</h2>
        <div className="text-center py-12">
          <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">暂无同步活动记录</p>
          <p className="text-sm text-gray-400 mt-2">
            配置 GitHub 集成后，这里将显示同步历史和状态
          </p>
        </div>
      </div>
    </div>
  );
};

// 文档视图组件
const DocumentationView: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-6">GitHub CLI 集成文档</h2>
        
        <div className="prose max-w-none">
          <h3 className="text-lg font-medium text-gray-900 mb-3">快速开始</h3>
          <div className="space-y-4 text-sm text-gray-700">
            <div>
              <h4 className="font-medium mb-2">1. 安装 GitHub CLI</h4>
              <p>访问 <a href="https://cli.github.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">GitHub CLI 官网</a> 下载并安装适合您操作系统的版本。</p>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">2. 认证 GitHub 账户</h4>
              <p>在终端中运行 <code className="bg-gray-100 px-2 py-1 rounded">gh auth login</code> 并按照提示完成认证。</p>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">3. 配置项目</h4>
              <p>在"项目配置"标签页中设置您的仓库 URL 和本地路径。</p>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">4. 开始同步</h4>
              <p>配置完成后，系统将自动处理文件变更的推送和拉取。</p>
            </div>
          </div>

          <h3 className="text-lg font-medium text-gray-900 mb-3 mt-8">功能特性</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">自动同步</h4>
              <ul className="text-sm text-gray-700 space-y-1">
                <li>• 检测本地文件变更</li>
                <li>• 自动提交和推送</li>
                <li>• 定期拉取远程更新</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">冲突处理</h4>
              <ul className="text-sm text-gray-700 space-y-1">
                <li>• 智能冲突检测</li>
                <li>• 多种解决策略</li>
                <li>• 手动合并支持</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">协作功能</h4>
              <ul className="text-sm text-gray-700 space-y-1">
                <li>• 自动创建 Pull Request</li>
                <li>• Webhook 集成</li>
                <li>• 团队通知</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">安全保障</h4>
              <ul className="text-sm text-gray-700 space-y-1">
                <li>• 本地优先策略</li>
                <li>• 数据备份机制</li>
                <li>• 权限验证</li>
              </ul>
            </div>
          </div>

          <h3 className="text-lg font-medium text-gray-900 mb-3 mt-8">常见问题</h3>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Q: 如何处理合并冲突？</h4>
              <p className="text-sm text-gray-700">
                系统会自动检测冲突并提供解决选项。您可以选择本地优先、远程优先或手动合并。
              </p>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Q: 支持哪些文件类型？</h4>
              <p className="text-sm text-gray-700">
                支持所有文本文件类型，包括代码、文档、配置文件等。二进制文件也支持，但不提供内容比较功能。
              </p>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Q: 如何设置自动同步间隔？</h4>
              <p className="text-sm text-gray-700">
                在工作流配置中可以设置同步间隔，建议设置为 15-30 分钟以平衡性能和实时性。
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
