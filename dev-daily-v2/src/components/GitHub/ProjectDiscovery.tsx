/**
 * GitHub 项目发现组件 - 自动发现和同步 GitHub 项目到管理平台
 */

import React, { useState, useEffect } from 'react';
import {
  Search,
  Download,
  RefreshCw as Sync,
  Filter,
  FileText,
  BarChart3,
  Settings,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Eye,
  EyeOff,
  Star,
  GitFork,
  AlertTriangle,
  User,
  Grid3X3,
  List,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import {
  GitHubProjectDiscovery,
  ProjectSyncConfig,
  DiscoveredProject
} from '../../services/GitHubProjectDiscovery';
import {
  GitHubAccountManager,
  GitHubAccount,
  ProjectWithAccount
} from '../../services/GitHubAccountManager';

export const ProjectDiscovery: React.FC = () => {
  const [config, setConfig] = useState<ProjectSyncConfig>({
    githubToken: '',
    includePrivateRepos: false,
    syncIssues: true,
    syncPullRequests: true,
    syncMilestones: false,
    filterByTopics: [],
    filterByLanguage: [],
    excludeArchived: true,
    excludeForked: true
  });

  const [accounts, setAccounts] = useState<GitHubAccount[]>([]);
  const [selectedAccountIds, setSelectedAccountIds] = useState<Set<string>>(new Set());
  const [projectsByAccount, setProjectsByAccount] = useState<Record<string, ProjectWithAccount[]>>({});
  const [isDiscovering, setIsDiscovering] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [activeTab, setActiveTab] = useState<'accounts' | 'discover' | 'sync' | 'report'>('accounts');
  const [selectedProjects, setSelectedProjects] = useState<Set<string>>(new Set());
  const [discoveryResult, setDiscoveryResult] = useState<{
    success: boolean;
    errors: string[];
  } | null>(null);

  // 新增状态：显示模式和分页
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(6);

  // 获取所有项目的扁平列表
  const allProjects = Object.values(projectsByAccount).flat();

  // 分页逻辑
  const totalPages = Math.ceil(allProjects.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentProjects = allProjects.slice(startIndex, endIndex);

  useEffect(() => {
    loadAccounts();
  }, []);

  const loadAccounts = () => {
    const allAccounts = GitHubAccountManager.getAllAccounts();
    setAccounts(allAccounts);

    // 默认选择活跃账号
    const activeAccount = GitHubAccountManager.getActiveAccount();
    if (activeAccount) {
      setSelectedAccountIds(new Set([activeAccount.id]));
    }
  };

  const handleDiscoverProjects = async () => {
    if (selectedAccountIds.size === 0) {
      alert('请先选择要发现项目的账号');
      return;
    }

    setIsDiscovering(true);
    setDiscoveryResult(null);
    setProjectsByAccount({});

    try {
      const result = await GitHubAccountManager.discoverAllAccountsProjects(config);

      console.log('Discovery result:', result);
      console.log('Selected account IDs:', Array.from(selectedAccountIds));

      // 只保留选中账号的项目
      const filteredProjects: Record<string, ProjectWithAccount[]> = {};
      for (const accountId of selectedAccountIds) {
        if (result.projectsByAccount[accountId]) {
          filteredProjects[accountId] = result.projectsByAccount[accountId];
          console.log(`Account ${accountId} has ${result.projectsByAccount[accountId].length} projects`);
        }
      }

      console.log('Filtered projects:', filteredProjects);

      setProjectsByAccount(filteredProjects);
      setDiscoveryResult({
        success: result.success,
        errors: result.errors
      });

      if (result.success) {
        setActiveTab('discover');
      }
    } catch (error) {
      setDiscoveryResult({
        success: false,
        errors: [error instanceof Error ? error.message : '发现项目失败']
      });
    } finally {
      setIsDiscovering(false);
    }
  };

  const handleSyncProjects = async (platformType: 'notion' | 'airtable') => {
    const selectedProjectList = allProjects.filter(p =>
      selectedProjects.has(p.repository.full_name)
    );

    if (selectedProjectList.length === 0) {
      alert('请先选择要同步的项目');
      return;
    }

    setIsSyncing(true);
    try {
      // 这里需要获取平台配置
      const platformConfig = {}; // 从设置中获取

      // 转换为 DiscoveredProject 格式
      const discoveredProjects = selectedProjectList.map(p => ({
        source: 'github' as const,
        repository: p.repository,
        issues: [],
        pullRequests: [],
        milestones: [],
        projectStructure: {
          hasReadme: false,
          hasDocumentation: false,
          hasTests: false,
          hasCICD: false,
          packageManager: null,
          framework: null,
          directories: [],
          fileTypes: {}
        },
        estimatedComplexity: 'medium' as const,
        suggestedPlatform: 'both' as const
      }));

      const result = await GitHubProjectDiscovery.syncProjectsToPlatform(
        discoveredProjects,
        platformType,
        platformConfig
      );

      alert(`同步完成！成功同步 ${result.syncedProjects} 个项目`);
    } catch (error) {
      alert(`同步失败: ${error}`);
    } finally {
      setIsSyncing(false);
    }
  };

  const handleGenerateReport = () => {
    if (allProjects.length === 0) {
      alert('请先发现项目');
      return;
    }

    // 生成多账号项目报告
    let report = `# GitHub 多账号项目清单报告\n\n`;
    report += `**生成时间**: ${new Date().toLocaleString()}\n`;
    report += `**账号数量**: ${Object.keys(projectsByAccount).length}\n`;
    report += `**项目总数**: ${allProjects.length}\n\n`;

    // 按账号分组显示
    Object.entries(projectsByAccount).forEach(([accountId, accountProjects]) => {
      const account = accounts.find(acc => acc.id === accountId);
      if (!account) return;

      report += `## 📁 ${account.name} (@${account.login})\n\n`;
      report += `**账号类型**: ${account.type === 'Organization' ? '组织' : '个人'}\n`;
      report += `**项目数量**: ${accountProjects.length}\n\n`;

      accountProjects.forEach((project, index) => {
        const repo = project.repository;
        report += `### ${index + 1}. ${repo.name}\n\n`;
        report += `**描述**: ${repo.description || '无描述'}\n`;
        report += `**语言**: ${repo.language || '未知'}\n`;
        report += `**Stars**: ${repo.stargazers_count} | **Forks**: ${repo.forks_count} | **Issues**: ${repo.open_issues_count}\n`;
        report += `**最后更新**: ${new Date(repo.updated_at).toLocaleDateString()}\n`;
        report += `**仓库地址**: [${repo.full_name}](${repo.html_url})\n\n`;
        report += `---\n\n`;
      });
    });

    // 下载报告
    const blob = new Blob([report], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `github-multi-account-projects-${new Date().toISOString().split('T')[0]}.md`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const toggleProjectSelection = (projectName: string) => {
    const newSelection = new Set(selectedProjects);
    if (newSelection.has(projectName)) {
      newSelection.delete(projectName);
    } else {
      newSelection.add(projectName);
    }
    setSelectedProjects(newSelection);
  };

  const selectAllProjects = () => {
    setSelectedProjects(new Set(allProjects.map(p => p.repository.full_name)));
  };

  const clearSelection = () => {
    setSelectedProjects(new Set());
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'notion': return '📝';
      case 'airtable': return '📊';
      case 'both': return '🔄';
      default: return '❓';
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* 页面头部 */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">GitHub 项目发现</h1>
        <p className="text-gray-600">
          自动发现您的 GitHub 项目并同步到项目管理平台
        </p>
      </div>

      {/* 标签页导航 */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'accounts', name: '选择账号', icon: User },
            { id: 'discover', name: '发现项目', icon: Search },
            { id: 'sync', name: '同步管理', icon: Sync },
            { id: 'report', name: '生成报告', icon: FileText }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* 账号选择页面 */}
      {activeTab === 'accounts' && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold mb-4">选择 GitHub 账号</h2>

            {accounts.length === 0 ? (
              <div className="text-center py-8">
                <User className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">还没有绑定任何 GitHub 账号</p>
                <p className="text-sm text-gray-400 mb-4">
                  请先在"GitHub 集成"页面的"CLI 状态"标签中添加账号
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                <p className="text-gray-600 mb-4">
                  选择要发现项目的 GitHub 账号（可多选）
                </p>

                <div className="space-y-3">
                  {accounts.map((account) => (
                    <label
                      key={account.id}
                      className={`flex items-center space-x-3 p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedAccountIds.has(account.id)
                          ? 'border-blue-300 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <input
                        type="checkbox"
                        checked={selectedAccountIds.has(account.id)}
                        onChange={(e) => {
                          const newSelection = new Set(selectedAccountIds);
                          if (e.target.checked) {
                            newSelection.add(account.id);
                          } else {
                            newSelection.delete(account.id);
                          }
                          setSelectedAccountIds(newSelection);
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />

                      <img
                        src={account.avatar_url}
                        alt={account.name}
                        className="w-8 h-8 rounded-full"
                      />

                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-gray-900">{account.name}</span>
                          <span className="text-gray-500">@{account.login}</span>
                          {account.type === 'Organization' ? (
                            <span className="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full">组织</span>
                          ) : (
                            <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">个人</span>
                          )}
                          {account.isActive && (
                            <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">活跃</span>
                          )}
                        </div>
                        <div className="text-sm text-gray-500">
                          Token: {account.tokenName}
                        </div>
                      </div>
                    </label>
                  ))}
                </div>

                <div className="pt-4">
                  <button
                    type="button"
                    onClick={handleDiscoverProjects}
                    disabled={isDiscovering || selectedAccountIds.size === 0}
                    className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isDiscovering ? (
                      <RefreshCw className="w-4 h-4 animate-spin" />
                    ) : (
                      <Search className="w-4 h-4" />
                    )}
                    <span>{isDiscovering ? '发现中...' : `发现选中账号的项目 (${selectedAccountIds.size})`}</span>
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* 发现结果 */}
          {discoveryResult && (
            <div className={`rounded-lg border p-4 ${
              discoveryResult.success
                ? 'bg-green-50 border-green-200'
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-center space-x-2">
                {discoveryResult.success ? (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                ) : (
                  <AlertCircle className="w-5 h-5 text-red-500" />
                )}
                <span className={`font-medium ${
                  discoveryResult.success ? 'text-green-800' : 'text-red-800'
                }`}>
                  {discoveryResult.success
                    ? `成功发现 ${allProjects.length} 个项目`
                    : '发现项目失败'
                  }
                </span>
              </div>

              {discoveryResult.errors.length > 0 && (
                <div className="mt-2">
                  <p className="text-sm text-red-700">错误信息:</p>
                  <ul className="text-sm text-red-600 mt-1 space-y-1">
                    {discoveryResult.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* 这个配置页面已经移除，因为配置现在在账号选择页面 */}
      {false && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold mb-4">GitHub 配置</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  GitHub Personal Access Token
                </label>
                <div className="relative">
                  <input
                    type={config.githubToken ? 'password' : 'text'}
                    value={config.githubToken}
                    onChange={(e) => setConfig(prev => ({ ...prev, githubToken: e.target.value }))}
                    placeholder="ghp_xxxxxxxxxxxxxxxxxxxx"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <button
                    type="button"
                    onClick={() => {
                      // 切换显示/隐藏
                    }}
                    className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
                  >
                    {config.githubToken ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  需要 repo 权限来读取仓库信息
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <h3 className="font-medium text-gray-900">包含选项</h3>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.includePrivateRepos}
                      onChange={(e) => setConfig(prev => ({ ...prev, includePrivateRepos: e.target.checked }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">包含私有仓库</span>
                  </label>

                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.syncIssues}
                      onChange={(e) => setConfig(prev => ({ ...prev, syncIssues: e.target.checked }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">同步 Issues</span>
                  </label>

                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.syncPullRequests}
                      onChange={(e) => setConfig(prev => ({ ...prev, syncPullRequests: e.target.checked }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">同步 Pull Requests</span>
                  </label>
                </div>

                <div className="space-y-3">
                  <h3 className="font-medium text-gray-900">排除选项</h3>
                  
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.excludeArchived}
                      onChange={(e) => setConfig(prev => ({ ...prev, excludeArchived: e.target.checked }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">排除已归档仓库</span>
                  </label>

                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={config.excludeForked}
                      onChange={(e) => setConfig(prev => ({ ...prev, excludeForked: e.target.checked }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">排除 Fork 仓库</span>
                  </label>
                </div>
              </div>

              <div className="pt-4">
                <button
                  onClick={handleDiscoverProjects}
                  disabled={isDiscovering || !config.githubToken}
                  className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isDiscovering ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    <Search className="w-4 h-4" />
                  )}
                  <span>{isDiscovering ? '发现中...' : '开始发现项目'}</span>
                </button>
              </div>
            </div>
          </div>

          {/* 发现结果 */}
          {discoveryResult && (
            <DiscoveryResultDisplay
              result={discoveryResult}
              projectCount={allProjects.length}
            />
          )}
        </div>
      )}

      {/* 发现项目页面 */}
      {activeTab === 'discover' && (
        <div className="space-y-6">
          {allProjects.length === 0 ? (
            <div className="text-center py-12">
              <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">还没有发现任何项目</p>
              <button
                onClick={() => setActiveTab('accounts')}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                开始配置
              </button>
            </div>
          ) : (
            <>
              {/* 操作栏 */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-600">
                    共发现 {allProjects.length} 个项目，已选择 {selectedProjects.size} 个
                  </span>
                  <button
                    type="button"
                    onClick={selectAllProjects}
                    className="text-sm text-blue-600 hover:text-blue-700"
                  >
                    全选
                  </button>
                  <button
                    type="button"
                    onClick={clearSelection}
                    className="text-sm text-gray-600 hover:text-gray-700"
                  >
                    清除选择
                  </button>
                </div>

                <div className="flex items-center space-x-2">
                  {/* 视图模式切换 */}
                  <div className="flex items-center border rounded-md">
                    <button
                      type="button"
                      onClick={() => setViewMode('grid')}
                      className={`p-2 ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'}`}
                      title="网格视图"
                    >
                      <Grid3X3 className="w-4 h-4" />
                    </button>
                    <button
                      type="button"
                      onClick={() => setViewMode('list')}
                      className={`p-2 ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'}`}
                      title="列表视图"
                    >
                      <List className="w-4 h-4" />
                    </button>
                  </div>

                  {/* 每页显示数量 */}
                  <select
                    value={itemsPerPage}
                    onChange={(e) => {
                      setItemsPerPage(Number(e.target.value));
                      setCurrentPage(1);
                    }}
                    className="text-sm border rounded-md px-2 py-1"
                  >
                    <option value={6}>6 项/页</option>
                    <option value={12}>12 项/页</option>
                    <option value={24}>24 项/页</option>
                    <option value={allProjects.length}>全部显示</option>
                  </select>

                  <button
                    type="button"
                    onClick={handleGenerateReport}
                    className="flex items-center space-x-1 px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-md"
                  >
                    <FileText className="w-4 h-4" />
                    <span>生成报告</span>
                  </button>
                </div>
              </div>

              {/* 项目显示区域 */}
              <div className={`${viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}`}>
                {currentProjects.map((project) => (
                  <div
                    key={project.repository.id}
                    className={`bg-white rounded-lg border transition-colors ${
                      viewMode === 'list' ? 'p-4' : 'p-6'
                    } ${
                      selectedProjects.has(project.repository.full_name)
                        ? 'border-blue-300 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className={`${viewMode === 'list' ? 'flex items-center justify-between' : 'flex items-start justify-between'}`}>
                      <div className={`flex items-start space-x-4 flex-1 ${viewMode === 'list' ? 'items-center' : ''}`}>
                        <input
                          type="checkbox"
                          checked={selectedProjects.has(project.repository.full_name)}
                          onChange={() => toggleProjectSelection(project.repository.full_name)}
                          className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          aria-label={`选择项目 ${project.repository.name}`}
                        />

                        <div className="flex-1">
                          <div className={`flex items-center space-x-2 ${viewMode === 'list' ? 'mb-1' : 'mb-2'}`}>
                            <h3 className={`font-semibold text-gray-900 ${viewMode === 'list' ? 'text-base' : 'text-lg'}`}>
                              {project.repository.name}
                            </h3>
                            {project.repository.private && (
                              <span className="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">
                                私有
                              </span>
                            )}
                            {/* 账号标识 */}
                            <div className="flex items-center space-x-1 text-xs text-gray-500">
                              <img
                                src={project.accountAvatar}
                                alt={project.accountName}
                                className="w-4 h-4 rounded-full"
                              />
                              <span>@{project.accountLogin}</span>
                            </div>
                          </div>
                          
                          <p className="text-gray-600 mb-3">
                            {project.repository.description || '无描述'}
                          </p>
                          
                          <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                            <div className="flex items-center space-x-1">
                              <Star className="w-4 h-4" />
                              <span>{project.repository.stargazers_count}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <GitFork className="w-4 h-4" />
                              <span>{project.repository.forks_count}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <AlertTriangle className="w-4 h-4" />
                              <span>{project.repository.open_issues_count} issues</span>
                            </div>
                            {project.repository.language && (
                              <span className="px-2 py-1 bg-gray-100 rounded-full">
                                {project.repository.language}
                              </span>
                            )}
                          </div>
                          
                          <div className="flex items-center space-x-3">
                            <span className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full">
                              来源: {project.accountType === 'Organization' ? '组织' : '个人'}
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <a
                        href={project.repository.html_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-700"
                      >
                        <span className="sr-only">查看仓库</span>
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </a>
                    </div>
                  </div>
                ))}
              </div>

              {/* 分页控件 */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-6">
                  <div className="text-sm text-gray-600">
                    显示第 {startIndex + 1}-{Math.min(endIndex, allProjects.length)} 项，共 {allProjects.length} 项
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      type="button"
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="flex items-center px-3 py-2 text-sm border rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                      <ChevronLeft className="w-4 h-4 mr-1" />
                      上一页
                    </button>

                    <div className="flex items-center space-x-1">
                      {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                        <button
                          key={page}
                          type="button"
                          onClick={() => setCurrentPage(page)}
                          className={`px-3 py-2 text-sm border rounded-md ${
                            currentPage === page
                              ? 'bg-blue-600 text-white border-blue-600'
                              : 'hover:bg-gray-50'
                          }`}
                        >
                          {page}
                        </button>
                      ))}
                    </div>

                    <button
                      type="button"
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="flex items-center px-3 py-2 text-sm border rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                    >
                      下一页
                      <ChevronRight className="w-4 h-4 ml-1" />
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      )}

      {/* 同步管理页面 */}
      {activeTab === 'sync' && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold mb-4">同步到项目管理平台</h2>
            
            {selectedProjects.size === 0 ? (
              <div className="text-center py-8">
                <Sync className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">请先在"发现项目"页面选择要同步的项目</p>
                <button
                  onClick={() => setActiveTab('discover')}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  选择项目
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                <p className="text-gray-600">
                  已选择 {selectedProjects.size} 个项目进行同步
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <button
                    onClick={() => handleSyncProjects('notion')}
                    disabled={isSyncing}
                    className="flex items-center justify-center space-x-2 p-4 border-2 border-dashed border-blue-300 rounded-lg hover:border-blue-400 disabled:opacity-50"
                  >
                    <span className="text-2xl">📝</span>
                    <span className="text-blue-700 font-medium">同步到 Notion</span>
                  </button>
                  
                  <button
                    onClick={() => handleSyncProjects('airtable')}
                    disabled={isSyncing}
                    className="flex items-center justify-center space-x-2 p-4 border-2 border-dashed border-green-300 rounded-lg hover:border-green-400 disabled:opacity-50"
                  >
                    <span className="text-2xl">📊</span>
                    <span className="text-green-700 font-medium">同步到 Airtable</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* 报告页面 */}
      {activeTab === 'report' && (
        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold mb-4">项目清单报告</h2>
            
            {allProjects.length === 0 ? (
              <div className="text-center py-8">
                <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">请先发现项目以生成报告</p>
                <button
                  type="button"
                  onClick={() => setActiveTab('accounts')}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  开始发现
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="text-2xl font-bold text-blue-600">{allProjects.length}</div>
                    <div className="text-sm text-blue-800">发现的项目</div>
                  </div>

                  <div className="bg-green-50 rounded-lg p-4">
                    <div className="text-2xl font-bold text-green-600">
                      {Object.keys(projectsByAccount).length}
                    </div>
                    <div className="text-sm text-green-800">关联账号</div>
                  </div>

                  <div className="bg-yellow-50 rounded-lg p-4">
                    <div className="text-2xl font-bold text-yellow-600">
                      {allProjects.reduce((sum, p) => sum + p.repository.open_issues_count, 0)}
                    </div>
                    <div className="text-sm text-yellow-800">待处理 Issues</div>
                  </div>
                </div>
                
                <button
                  onClick={handleGenerateReport}
                  className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  <Download className="w-4 h-4" />
                  <span>下载详细报告 (Markdown)</span>
                </button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// 发现结果显示组件
const DiscoveryResultDisplay: React.FC<{
  result: { success: boolean; errors: string[] } | null;
  projectCount: number;
}> = ({ result, projectCount }) => {
  if (!result) return null;
  return (
    <div className={`rounded-lg border p-4 ${
      result.success
        ? 'bg-green-50 border-green-200'
        : 'bg-red-50 border-red-200'
    }`}>
      <div className="flex items-center space-x-2">
        {result.success ? (
          <CheckCircle className="w-5 h-5 text-green-500" />
        ) : (
          <AlertCircle className="w-5 h-5 text-red-500" />
        )}
        <span className={`font-medium ${
          result.success ? 'text-green-800' : 'text-red-800'
        }`}>
          {result.success
            ? `成功发现 ${projectCount} 个项目`
            : '发现项目失败'
          }
        </span>
      </div>

      {result.errors && result.errors.length > 0 && (
        <div className="mt-2">
          <p className="text-sm text-red-700">错误信息:</p>
          <ul className="text-sm text-red-600 mt-1 space-y-1">
            {result.errors.map((error, index) => (
              <li key={index}>• {error}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};
