/**
 * GitHub 项目配置组件
 */

import React, { useState, useEffect } from 'react';
import { 
  Save, 
  TestTube, 
  GitBranch, 
  Folder, 
  Link, 
  AlertCircle,
  CheckCircle,
  RefreshCw
} from 'lucide-react';
import { GitHubCLIService, GitHubCLIConfig } from '../../services/GitHubCLIService';
import { useProjectContext } from '../../hooks/useProjectContext';

export const GitHubProjectConfig: React.FC = () => {
  const { currentProject } = useProjectContext();
  const [config, setConfig] = useState<GitHubCLIConfig>({
    projectId: '',
    repoUrl: '',
    branch: 'main',
    localPath: '',
    autoAuth: true
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  useEffect(() => {
    if (currentProject) {
      // 加载现有配置
      const existingConfig = GitHubCLIService.getConfig(currentProject.id);
      if (existingConfig) {
        setConfig(existingConfig);
      } else {
        setConfig(prev => ({
          ...prev,
          projectId: currentProject.id
        }));
      }
    }
  }, [currentProject]);

  const handleSave = async () => {
    if (!currentProject) return;

    setIsLoading(true);
    try {
      // 保存配置
      GitHubCLIService.saveConfig(config);
      
      // 初始化项目
      const result = await GitHubCLIService.initializeProject(config);
      
      if (result.success) {
        setTestResult({
          success: true,
          message: '配置保存成功，GitHub 集成已就绪'
        });
      } else {
        setTestResult({
          success: false,
          message: result.error || '初始化失败'
        });
      }
    } catch (error) {
      setTestResult({
        success: false,
        message: error instanceof Error ? error.message : '保存配置失败'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTest = async () => {
    if (!currentProject) return;

    setIsTesting(true);
    setTestResult(null);
    
    try {
      // 测试连接
      const repoInfo = await GitHubCLIService.getRepositoryInfo(currentProject.id);
      
      if (repoInfo) {
        setTestResult({
          success: true,
          message: `连接成功！仓库: ${repoInfo.owner}/${repoInfo.name}`
        });
      } else {
        setTestResult({
          success: false,
          message: '无法获取仓库信息，请检查配置'
        });
      }
    } catch (error) {
      setTestResult({
        success: false,
        message: error instanceof Error ? error.message : '测试连接失败'
      });
    } finally {
      setIsTesting(false);
    }
  };

  const handleSelectDirectory = async () => {
    try {
      // 使用 File System Access API 选择目录
      if ('showDirectoryPicker' in window) {
        const dirHandle = await (window as any).showDirectoryPicker();
        setConfig(prev => ({
          ...prev,
          localPath: dirHandle.name
        }));
      } else {
        alert('您的浏览器不支持目录选择功能，请手动输入路径');
      }
    } catch (error) {
      console.log('用户取消了目录选择');
    }
  };

  if (!currentProject) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">请先选择一个项目</p>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-6">
          GitHub 项目配置
        </h2>

        <div className="space-y-6">
          {/* 项目信息 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              当前项目
            </label>
            <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-md">
              <Folder className="w-4 h-4 text-gray-500" />
              <span className="font-medium">{currentProject.name}</span>
            </div>
          </div>

          {/* 仓库 URL */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              GitHub 仓库 URL
            </label>
            <div className="relative">
              <input
                type="url"
                value={config.repoUrl}
                onChange={(e) => setConfig(prev => ({ ...prev, repoUrl: e.target.value }))}
                placeholder="https://github.com/username/repository"
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <Link className="absolute left-3 top-2.5 w-4 h-4 text-gray-400" />
            </div>
            <p className="mt-1 text-xs text-gray-500">
              留空将创建新仓库，或输入现有仓库 URL 进行克隆
            </p>
          </div>

          {/* 分支名称 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              默认分支
            </label>
            <div className="relative">
              <input
                type="text"
                value={config.branch}
                onChange={(e) => setConfig(prev => ({ ...prev, branch: e.target.value }))}
                placeholder="main"
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <GitBranch className="absolute left-3 top-2.5 w-4 h-4 text-gray-400" />
            </div>
          </div>

          {/* 本地路径 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              本地项目路径
            </label>
            <div className="flex space-x-2">
              <div className="relative flex-1">
                <input
                  type="text"
                  value={config.localPath}
                  onChange={(e) => setConfig(prev => ({ ...prev, localPath: e.target.value }))}
                  placeholder="/path/to/your/project"
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <Folder className="absolute left-3 top-2.5 w-4 h-4 text-gray-400" />
              </div>
              <button
                type="button"
                onClick={handleSelectDirectory}
                className="px-4 py-2 bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded-md transition-colors"
              >
                选择
              </button>
            </div>
            <p className="mt-1 text-xs text-gray-500">
              选择或输入项目的本地存储路径
            </p>
          </div>

          {/* 自动认证 */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700">自动认证</label>
              <p className="text-xs text-gray-500">使用已保存的 GitHub CLI 认证</p>
            </div>
            <button
              type="button"
              onClick={() => setConfig(prev => ({ ...prev, autoAuth: !prev.autoAuth }))}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                config.autoAuth ? 'bg-blue-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  config.autoAuth ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        </div>

        {/* 测试结果 */}
        {testResult && (
          <div className={`mt-6 p-4 rounded-lg border ${
            testResult.success 
              ? 'bg-green-50 border-green-200 text-green-800' 
              : 'bg-red-50 border-red-200 text-red-800'
          }`}>
            <div className="flex items-center space-x-2">
              {testResult.success ? (
                <CheckCircle className="w-5 h-5" />
              ) : (
                <AlertCircle className="w-5 h-5" />
              )}
              <span className="font-medium">{testResult.message}</span>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="mt-6 flex space-x-3">
          <button
            onClick={handleSave}
            disabled={isLoading || !config.localPath}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <Save className="w-4 h-4" />
            )}
            <span>{isLoading ? '保存中...' : '保存配置'}</span>
          </button>

          <button
            onClick={handleTest}
            disabled={isTesting || !config.repoUrl || !config.localPath}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isTesting ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <TestTube className="w-4 h-4" />
            )}
            <span>{isTesting ? '测试中...' : '测试连接'}</span>
          </button>
        </div>
      </div>

      {/* 配置说明 */}
      <div className="bg-blue-50 rounded-lg border border-blue-200 p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-4">配置说明</h3>
        <div className="space-y-3 text-sm text-blue-800">
          <div>
            <strong>仓库 URL:</strong> 如果留空，系统将在指定路径创建新的 Git 仓库。如果提供 URL，将克隆现有仓库。
          </div>
          <div>
            <strong>本地路径:</strong> 项目文件的本地存储位置。确保您有读写权限。
          </div>
          <div>
            <strong>分支:</strong> 默认工作分支，通常为 "main" 或 "master"。
          </div>
        </div>
      </div>
    </div>
  );
};
