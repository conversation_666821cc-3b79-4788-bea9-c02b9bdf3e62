import React, { useEffect } from 'react';
import { MainDashboard } from './components/Dashboard/MainDashboard';
import { SimpleDashboard } from './components/Dashboard/SimpleDashboard';
import { DebugInfo } from './components/DebugInfo';
import { ProjectProvider } from './hooks/useProjectContext';
import { MultiProjectService } from './services/MultiProjectService';
import { WorkflowAutomationService } from './services/WorkflowAutomationService';
import { ServiceWorkerService } from './services/ServiceWorkerService';
import { OfflineBanner } from './components/Offline/OfflineIndicator';
import './App.css';

// 简单的样式测试组件
const StyleTest = () => (
  <div className="min-h-screen bg-blue-500 flex items-center justify-center">
    <div className="bg-white p-8 rounded-lg shadow-lg">
      <h1 className="text-2xl font-bold text-gray-900 mb-4">样式测试</h1>
      <p className="text-gray-600">如果你能看到这个蓝色背景和白色卡片，说明Tailwind CSS正常工作</p>
      <button
        onClick={() => window.location.href = '?app=true'}
        className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
      >
        进入主应用
      </button>
    </div>
  </div>
);

function App() {
  // 初始化多项目系统和工作流自动化
  useEffect(() => {
    MultiProjectService.initialize();
    WorkflowAutomationService.initialize();

    // 初始化 Service Worker
    ServiceWorkerService.register().then((success) => {
      if (success) {
        console.log('✅ Service Worker 初始化成功');
      } else {
        console.warn('⚠️ Service Worker 初始化失败');
      }
    });
  }, []);

  return (
    <div className="App">
      <OfflineBanner />
      <ProjectProvider>
        <MainDashboard />
      </ProjectProvider>
    </div>
  );
}

export default App;
