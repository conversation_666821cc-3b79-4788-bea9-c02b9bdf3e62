/**
 * 多项目管理数据模型
 * 支持工作空间、项目、用户的完整管理
 */

export interface Workspace {
  id: string;
  name: string;
  description: string;
  projects: Project[];
  members: WorkspaceMember[];
  settings: WorkspaceSettings;
  createdAt: string;
  updatedAt: string;
}

export interface Project {
  id: string;
  name: string;
  description: string;
  workspaceId: string;
  status: ProjectStatus;
  priority: ProjectPriority;
  progress: number;
  startDate: string;
  endDate?: string;
  tags: string[];
  color: string;
  
  // 项目数据
  projectTree: any; // ProjectTreeNode
  roadmap: any; // RoadmapData
  calendar: any; // CalendarData
  backups: any[]; // BackupData[]
  logs: any[]; // ProjectLog[]
  
  // 成员和设置
  members: ProjectMember[];
  settings: ProjectSettings;
  
  // 统计信息
  stats: ProjectStats;
  
  createdAt: string;
  updatedAt: string;
}

export type ProjectStatus = 'planning' | 'active' | 'paused' | 'completed' | 'archived';
export type ProjectPriority = 'low' | 'medium' | 'high' | 'urgent';

export interface WorkspaceMember {
  userId: string;
  name: string;
  email: string;
  role: WorkspaceRole;
  joinedAt: string;
}

export interface ProjectMember {
  userId: string;
  name: string;
  role: ProjectRole;
  permissions: ProjectPermission[];
  joinedAt: string;
}

export type WorkspaceRole = 'owner' | 'admin' | 'member' | 'viewer';
export type ProjectRole = 'lead' | 'developer' | 'designer' | 'tester' | 'viewer';
export type ProjectPermission = 'read' | 'write' | 'delete' | 'manage_members' | 'manage_settings';

export interface WorkspaceSettings {
  defaultProjectTemplate: string;
  autoBackup: boolean;
  backupRetention: number;
  timezone: string;
  language: string;
  theme: 'light' | 'dark' | 'auto';
}

export interface ProjectSettings {
  autoBackup: boolean;
  backupOnNodeComplete: boolean;
  gitIntegration: boolean;
  fileWatching: boolean;
  notifications: NotificationSettings;
  customFields: CustomField[];
}

export interface NotificationSettings {
  email: boolean;
  browser: boolean;
  milestones: boolean;
  deadlines: boolean;
  mentions: boolean;
}

export interface CustomField {
  id: string;
  name: string;
  type: 'text' | 'number' | 'date' | 'select' | 'multiselect';
  required: boolean;
  options?: string[];
}

export interface ProjectStats {
  totalTasks: number;
  completedTasks: number;
  totalHours: number;
  actualHours: number;
  issuesCount: number;
  milestonesCount: number;
  lastActivity: string;
  teamSize: number;
}

// 用户个人数据
export interface UserProfile {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  workspaces: string[];
  preferences: UserPreferences;
  createdAt: string;
  updatedAt: string;
}

export interface UserPreferences {
  defaultWorkspace: string;
  theme: 'light' | 'dark' | 'auto';
  language: string;
  timezone: string;
  notifications: NotificationSettings;
  dashboardLayout: DashboardLayout;
}

export interface DashboardLayout {
  widgets: DashboardWidget[];
  columns: number;
}

export interface DashboardWidget {
  id: string;
  type: 'project_overview' | 'recent_activity' | 'tasks' | 'calendar' | 'stats';
  position: { x: number; y: number; w: number; h: number };
  config: any;
}

// 跨项目任务管理
export interface CrossProjectTask {
  id: string;
  title: string;
  description: string;
  projectId: string;
  projectName: string;
  nodeId?: string;
  priority: TaskPriority;
  status: TaskStatus;
  assignee?: string;
  dueDate?: string;
  estimatedHours?: number;
  actualHours?: number;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

export type TaskPriority = 'low' | 'medium' | 'high' | 'urgent';
export type TaskStatus = 'todo' | 'in_progress' | 'review' | 'done' | 'blocked';

// 全局统计和分析
export interface GlobalStats {
  totalProjects: number;
  activeProjects: number;
  completedProjects: number;
  totalTasks: number;
  completedTasks: number;
  totalHours: number;
  teamMembers: number;
  recentActivity: ActivityItem[];
}

export interface ActivityItem {
  id: string;
  type: 'project_created' | 'task_completed' | 'milestone_reached' | 'member_joined';
  projectId: string;
  projectName: string;
  userId: string;
  userName: string;
  description: string;
  timestamp: string;
}

// 项目模板
export interface ProjectTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  projectTree: any;
  defaultSettings: ProjectSettings;
  estimatedDuration: number;
  tags: string[];
  isPublic: boolean;
  createdBy: string;
  createdAt: string;
}

// 导入导出
export interface ExportData {
  workspace: Workspace;
  projects: Project[];
  userProfile: UserProfile;
  exportDate: string;
  version: string;
}

export interface ImportOptions {
  mergeStrategy: 'replace' | 'merge' | 'skip_existing';
  includeMembers: boolean;
  includeSettings: boolean;
  includeHistory: boolean;
}
