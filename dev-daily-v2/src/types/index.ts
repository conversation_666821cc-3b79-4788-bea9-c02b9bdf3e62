// dev-daily v2 核心类型定义

// 项目树节点类型
export interface ProjectTreeNode {
  id: string;
  name: string;
  description: string;
  type: 'project' | 'module' | 'task' | 'document' | 'feature';
  status: 'planned' | 'active' | 'testing' | 'completed' | 'blocked' | 'paused';
  priority: 'high' | 'medium' | 'low';
  progress: number;
  parent?: string;
  children?: ProjectTreeNode[];
  metadata: {
    createdAt: string;
    updatedAt: string;
    estimatedHours?: number;
    actualHours?: number;
    tags?: string[];
    linkedDocuments?: string[];
    author?: string;
  };
}

// 文档类型
export interface Document {
  id: string;
  filename: string;
  title: string;
  content: string;
  type: 'daily-log' | 'issue-report' | 'deployment' | 'summary' | 'guideline' | 'template';
  status: 'draft' | 'published' | 'archived';
  metadata: {
    createdAt: string;
    updatedAt: string;
    author: string;
    tags: string[];
    linkedNodes?: string[];
    fileSize?: number;
    wordCount?: number;
    aiAnalysis?: {
      summary: string;
      keywords: string[];
      sentiment: 'positive' | 'neutral' | 'negative';
      topics: string[];
      complexity: 'low' | 'medium' | 'high';
    };
  };
}

// AI建议类型
export interface AISuggestion {
  id: string;
  type: 'optimization' | 'refactor' | 'split' | 'merge' | 'dependency' | 'performance' | 'security' | 'documentation';
  title: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  confidence: number;
  targetType: 'node' | 'document' | 'project';
  targetId?: string;
  actionable: boolean;
  estimatedTime?: string;
  benefits?: string[];
  risks?: string[];
  status: 'pending' | 'applied' | 'dismissed';
  createdAt: string;
  appliedAt?: string;
}

// AI聊天消息类型
export interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: string;
  context?: {
    currentNode?: string;
    currentDocument?: string;
    recentActivity?: string[];
  };
  suggestions?: AISuggestion[];
  metadata?: {
    model?: string;
    tokens?: number;
    responseTime?: number;
  };
}

// 代码生成模板类型
export interface CodeTemplate {
  id: string;
  name: string;
  description: string;
  category: 'component' | 'api' | 'database' | 'config' | 'test' | 'documentation';
  framework: 'react' | 'node' | 'workers' | 'general';
  complexity: 'basic' | 'intermediate' | 'advanced';
  estimatedTime: string;
  variables: TemplateVariable[];
  files: TemplateFile[];
}

export interface TemplateVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description: string;
  required: boolean;
  defaultValue?: any;
  options?: string[];
}

export interface TemplateFile {
  filename: string;
  content: string;
  language: string;
  description: string;
}

// 变更记录类型
export interface ChangeRecord {
  id: string;
  timestamp: string;
  action: 'create' | 'update' | 'delete' | 'move' | 'split' | 'merge';
  entityType: 'node' | 'document' | 'project';
  entityId: string;
  details: string;
  oldValue?: any;
  newValue?: any;
  author: string;
}

// 备份类型
export interface BackupPoint {
  id: string;
  type: 'manual' | 'auto' | 'milestone' | 'emergency';
  description: string;
  createdAt: string;
  size: number;
  path: string;
  compressed: boolean;
  metadata: {
    version: string;
    nodeCount: number;
    documentCount: number;
    author: string;
    checksum: string;
  };
}

// 用户设置类型
export interface UserSettings {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  editor: {
    fontSize: number;
    tabSize: number;
    wordWrap: boolean;
    showLineNumbers: boolean;
    autoSave: boolean;
    autoSaveDelay: number;
  };
  ai: {
    enabled: boolean;
    provider: 'openai' | 'local' | 'mock';
    model: string;
    temperature: number;
    maxTokens: number;
    autoSuggestions: boolean;
  };
  backup: {
    autoBackup: boolean;
    backupInterval: 'hourly' | 'daily' | 'weekly';
    retentionDays: number;
    compressionEnabled: boolean;
  };
  ui: {
    sidebarWidth: number;
    showMinimap: boolean;
    compactMode: boolean;
    animationsEnabled: boolean;
  };
}

// 项目统计类型
export interface ProjectStats {
  totalNodes: number;
  nodesByStatus: Record<ProjectTreeNode['status'], number>;
  nodesByType: Record<ProjectTreeNode['type'], number>;
  totalDocuments: number;
  documentsByType: Record<Document['type'], number>;
  totalProgress: number;
  estimatedHours: number;
  actualHours: number;
  activeNodes: number;
  completedNodes: number;
  recentActivity: ChangeRecord[];
  aiSuggestions: {
    total: number;
    pending: number;
    applied: number;
    dismissed: number;
  };
}

// 搜索结果类型
export interface SearchResult {
  id: string;
  type: 'node' | 'document';
  title: string;
  description: string;
  content?: string;
  score: number;
  highlights: string[];
  metadata: {
    path: string;
    lastModified: string;
    tags: string[];
  };
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata?: {
    timestamp: string;
    requestId: string;
    version: string;
  };
}

// 导出/导入类型
export interface ExportData {
  version: string;
  exportedAt: string;
  projectTree: ProjectTreeNode;
  documents: Document[];
  settings: UserSettings;
  metadata: {
    totalNodes: number;
    totalDocuments: number;
    exportType: 'full' | 'partial';
    checksum: string;
  };
}

// 文件系统类型
export interface FileSystemItem {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  lastModified: string;
  children?: FileSystemItem[];
}

// 同步状态类型
export interface SyncStatus {
  isOnline: boolean;
  lastSync: string;
  pendingChanges: number;
  conflictCount: number;
  syncInProgress: boolean;
  error?: string;
}

// 主题类型
export interface Theme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    success: string;
    warning: string;
    error: string;
  };
  fonts: {
    sans: string;
    mono: string;
  };
}

// 键盘快捷键类型
export interface KeyboardShortcut {
  id: string;
  name: string;
  description: string;
  keys: string[];
  action: string;
  context?: 'global' | 'editor' | 'tree' | 'chat';
}

// 插件类型
export interface Plugin {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  enabled: boolean;
  config?: Record<string, any>;
  hooks: {
    onNodeCreate?: (node: ProjectTreeNode) => void;
    onDocumentSave?: (document: Document) => void;
    onAISuggestion?: (suggestion: AISuggestion) => void;
  };
}
