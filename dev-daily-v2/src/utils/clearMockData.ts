/**
 * 清理模拟数据工具
 * 用于清理本地存储中的所有模拟数据，为真实数据让路
 */

export class MockDataCleaner {
  /**
   * 清理所有模拟数据
   */
  static clearAllMockData(): void {
    console.log('🧹 开始清理模拟数据...');
    
    // 清理本地项目数据
    this.clearLocalProjects();
    
    // 清理多项目数据
    this.clearMultiProjectData();
    
    // 清理其他缓存数据
    this.clearCacheData();
    
    console.log('✅ 模拟数据清理完成');
  }

  /**
   * 清理本地项目数据
   */
  static clearLocalProjects(): void {
    const keys = [
      'dev-daily-v2-local-projects',
      'dev-daily-v2-scan-history',
      'dev-daily-v2-project-links'
    ];
    
    keys.forEach(key => {
      localStorage.removeItem(key);
      console.log(`🗑️ 已清理: ${key}`);
    });
  }

  /**
   * 清理多项目数据
   */
  static clearMultiProjectData(): void {
    const keys = [
      'dev-daily-v2-workspaces',
      'dev-daily-v2-current-workspace',
      'dev-daily-v2-projects'
    ];
    
    keys.forEach(key => {
      localStorage.removeItem(key);
      console.log(`🗑️ 已清理: ${key}`);
    });
  }

  /**
   * 清理缓存数据
   */
  static clearCacheData(): void {
    const keys = [
      'dev-daily-v2-settings',
      'dev-daily-v2-user-preferences',
      'dev-daily-v2-temp-data'
    ];
    
    keys.forEach(key => {
      localStorage.removeItem(key);
      console.log(`🗑️ 已清理: ${key}`);
    });
  }

  /**
   * 重置为干净状态
   */
  static resetToCleanState(): void {
    this.clearAllMockData();
    
    // 重新初始化基础数据结构
    console.log('🔄 重新初始化基础数据结构...');
    
    // 这里可以添加重新初始化的逻辑
    // 例如创建默认工作空间等
    
    console.log('✅ 系统已重置为干净状态');
  }

  /**
   * 检查是否存在模拟数据
   */
  static hasMockData(): boolean {
    const mockDataKeys = [
      'dev-daily-v2-local-projects',
      'dev-daily-v2-workspaces',
      'dev-daily-v2-projects'
    ];
    
    return mockDataKeys.some(key => {
      const data = localStorage.getItem(key);
      if (!data) return false;
      
      try {
        const parsed = JSON.parse(data);
        return Array.isArray(parsed) ? parsed.length > 0 : Object.keys(parsed).length > 0;
      } catch {
        return false;
      }
    });
  }

  /**
   * 获取当前数据状态
   */
  static getDataStatus(): {
    hasLocalProjects: boolean;
    hasWorkspaces: boolean;
    hasProjects: boolean;
    totalItems: number;
  } {
    const getItemCount = (key: string): number => {
      try {
        const data = localStorage.getItem(key);
        if (!data) return 0;
        const parsed = JSON.parse(data);
        return Array.isArray(parsed) ? parsed.length : Object.keys(parsed).length;
      } catch {
        return 0;
      }
    };

    const localProjectsCount = getItemCount('dev-daily-v2-local-projects');
    const workspacesCount = getItemCount('dev-daily-v2-workspaces');
    const projectsCount = getItemCount('dev-daily-v2-projects');

    return {
      hasLocalProjects: localProjectsCount > 0,
      hasWorkspaces: workspacesCount > 0,
      hasProjects: projectsCount > 0,
      totalItems: localProjectsCount + workspacesCount + projectsCount
    };
  }
}
