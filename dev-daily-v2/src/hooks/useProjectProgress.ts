import { useState, useEffect, useCallback } from 'react';
import { ProjectTreeNode, Document, UserSettings } from '../types/index';
import { BackupService } from '../services/BackupService';
import { ProjectLogService } from '../services/ProjectLogService';

export interface ProgressControlOptions {
  autoBackupOnComplete: boolean;
  autoLogProgress: boolean;
  showCompletionDialog: boolean;
  requireProgressNotes: boolean;
}

export interface NodeCompletionData {
  nodeId: string;
  nodeName: string;
  hoursWorked?: number;
  notes?: string;
  issues?: string[];
  achievements?: string[];
}

export const useProjectProgress = (
  projectTree: ProjectTreeNode | null,
  documents: Document[],
  userSettings: UserSettings
) => {
  const [progressOptions, setProgressOptions] = useState<ProgressControlOptions>({
    autoBackupOnComplete: true,
    autoLogProgress: true,
    showCompletionDialog: true,
    requireProgressNotes: false
  });

  const [isProcessingCompletion, setIsProcessingCompletion] = useState(false);

  // 从本地存储加载进度控制选项
  useEffect(() => {
    const stored = localStorage.getItem('dev-daily-v2-progress-options');
    if (stored) {
      try {
        const options = JSON.parse(stored) as ProgressControlOptions;
        setProgressOptions(options);
      } catch (error) {
        console.error('Failed to load progress options:', error);
      }
    }
  }, []);

  // 保存进度控制选项
  const updateProgressOptions = useCallback((options: Partial<ProgressControlOptions>) => {
    const newOptions = { ...progressOptions, ...options };
    setProgressOptions(newOptions);
    localStorage.setItem('dev-daily-v2-progress-options', JSON.stringify(newOptions));
  }, [progressOptions]);

  // 显示节点完成对话框
  const showNodeCompletionDialog = useCallback((
    nodeId: string,
    nodeName: string
  ): Promise<NodeCompletionData | null> => {
    return new Promise((resolve) => {
      if (!progressOptions.showCompletionDialog) {
        resolve({
          nodeId,
          nodeName,
          hoursWorked: 0,
          notes: '',
          issues: [],
          achievements: []
        });
        return;
      }

      // 创建模态对话框
      const modal = document.createElement('div');
      modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
      
      modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">节点完成确认</h3>
          <p class="text-gray-600 mb-4">节点 "${nodeName}" 已标记为完成</p>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">工作时长 (小时)</label>
              <input type="number" id="hoursWorked" min="0" step="0.5" 
                     class="w-full px-3 py-2 border border-gray-300 rounded-md" placeholder="0">
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">完成笔记</label>
              <textarea id="notes" rows="3" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md" 
                        placeholder="记录完成过程中的重要信息..."></textarea>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">遇到的问题</label>
              <textarea id="issues" rows="2" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md" 
                        placeholder="记录遇到的问题和解决方案..."></textarea>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">主要成就</label>
              <textarea id="achievements" rows="2" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md" 
                        placeholder="记录完成的主要成就..."></textarea>
            </div>
          </div>
          
          <div class="flex justify-end space-x-3 mt-6">
            <button id="cancelBtn" class="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-md">
              取消
            </button>
            <button id="confirmBtn" class="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 rounded-md">
              确认完成
            </button>
          </div>
        </div>
      `;

      document.body.appendChild(modal);

      const hoursInput = modal.querySelector('#hoursWorked') as HTMLInputElement;
      const notesInput = modal.querySelector('#notes') as HTMLTextAreaElement;
      const issuesInput = modal.querySelector('#issues') as HTMLTextAreaElement;
      const achievementsInput = modal.querySelector('#achievements') as HTMLTextAreaElement;
      const cancelBtn = modal.querySelector('#cancelBtn') as HTMLButtonElement;
      const confirmBtn = modal.querySelector('#confirmBtn') as HTMLButtonElement;

      const cleanup = () => {
        document.body.removeChild(modal);
      };

      cancelBtn.onclick = () => {
        cleanup();
        resolve(null);
      };

      confirmBtn.onclick = () => {
        const hoursWorked = parseFloat(hoursInput.value) || 0;
        const notes = notesInput.value.trim();
        const issues = issuesInput.value.trim().split('\n').filter(i => i.trim());
        const achievements = achievementsInput.value.trim().split('\n').filter(a => a.trim());

        if (progressOptions.requireProgressNotes && !notes) {
          alert('请填写完成笔记');
          return;
        }

        cleanup();
        resolve({
          nodeId,
          nodeName,
          hoursWorked,
          notes,
          issues,
          achievements
        });
      };

      // ESC键关闭
      const handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          cleanup();
          resolve(null);
          document.removeEventListener('keydown', handleKeyDown);
        }
      };
      document.addEventListener('keydown', handleKeyDown);
    });
  }, [progressOptions]);

  // 处理节点完成
  const handleNodeCompletion = useCallback(async (
    nodeId: string,
    nodeName: string,
    projectId: string = 'root',
    projectName: string = 'dev-daily v2 项目'
  ): Promise<boolean> => {
    if (isProcessingCompletion) {
      return false;
    }

    setIsProcessingCompletion(true);

    try {
      // 显示完成对话框
      const completionData = await showNodeCompletionDialog(nodeId, nodeName);
      
      if (!completionData) {
        return false; // 用户取消
      }

      // 记录项目日志
      if (progressOptions.autoLogProgress) {
        ProjectLogService.logTaskCompleted(
          projectId,
          projectName,
          nodeId,
          nodeName,
          completionData.hoursWorked
        );

        // 记录问题
        if (completionData.issues && completionData.issues.length > 0) {
          completionData.issues.forEach(issue => {
            ProjectLogService.logIssueReported(
              projectId,
              projectName,
              issue,
              nodeId,
              nodeName
            );
          });
        }

        // 记录笔记
        if (completionData.notes) {
          ProjectLogService.addProjectNote(
            projectId,
            projectName,
            completionData.notes,
            nodeId,
            nodeName
          );
        }
      }

      // 自动备份
      if (progressOptions.autoBackupOnComplete && projectTree) {
        const shouldBackup = await BackupService.checkAutoBackupOnNodeComplete(
          nodeId,
          nodeName,
          projectTree,
          documents,
          userSettings
        );

        if (shouldBackup) {
          console.log(`已为节点 "${nodeName}" 创建备份`);
        }
      }

      // 检查是否达成里程碑
      await checkMilestoneCompletion(nodeId, nodeName, projectId, projectName);

      return true;
    } catch (error) {
      console.error('Failed to process node completion:', error);
      alert('处理节点完成时发生错误: ' + (error as Error).message);
      return false;
    } finally {
      setIsProcessingCompletion(false);
    }
  }, [
    isProcessingCompletion,
    progressOptions,
    showNodeCompletionDialog,
    projectTree,
    documents,
    userSettings
  ]);

  // 检查里程碑完成
  const checkMilestoneCompletion = useCallback(async (
    nodeId: string,
    nodeName: string,
    projectId: string,
    projectName: string
  ) => {
    if (!projectTree) return;

    // 检查是否是模块完成（A、B、C、D模块）
    const isModuleNode = /^[ABCD]$/.test(nodeId);
    
    if (isModuleNode) {
      // 记录里程碑
      ProjectLogService.logMilestoneReached(
        projectId,
        projectName,
        `${nodeName} 模块完成`,
        nodeId
      );

      // 创建里程碑备份
      if (projectTree) {
        await BackupService.checkMilestoneBackup(
          nodeId,
          `${nodeName} 模块完成`,
          projectTree,
          documents,
          userSettings
        );
      }
    }

    // 检查是否是重要节点（如思维模型系统、AI功能等）
    const importantNodes = ['B4', 'B3', 'C3', 'D3'];
    if (importantNodes.includes(nodeId)) {
      ProjectLogService.logMilestoneReached(
        projectId,
        projectName,
        `重要功能完成: ${nodeName}`,
        nodeId
      );
    }
  }, [projectTree, documents, userSettings]);

  // 手动创建备份
  const createManualBackup = useCallback(async (description?: string) => {
    if (!projectTree) {
      throw new Error('项目树数据不可用');
    }

    return await BackupService.createBackup(
      projectTree,
      documents,
      userSettings,
      {
        description: description || `手动备份 - ${new Date().toLocaleString()}`,
        triggerType: 'manual'
      }
    );
  }, [projectTree, documents, userSettings]);

  // 获取项目统计
  const getProjectStats = useCallback(() => {
    if (!projectTree) return null;

    const analytics = ProjectLogService.generateAnalytics('root', 30);
    return {
      ...analytics,
      lastBackupTime: localStorage.getItem('dev-daily-v2-last-backup-time'),
      totalBackups: JSON.parse(localStorage.getItem('dev-daily-v2-backups') || '[]').length
    };
  }, [projectTree]);

  return {
    progressOptions,
    updateProgressOptions,
    handleNodeCompletion,
    createManualBackup,
    getProjectStats,
    isProcessingCompletion
  };
};
