/**
 * 项目上下文管理 Hook - 确保项目间完全独立
 */

import React, { useState, useEffect, useCallback, createContext, useContext } from 'react';
import { ProjectDataManager, ProjectData } from '../services/ProjectDataManager';
import { MultiProjectService } from '../services/MultiProjectService';
import { Project } from '../types/MultiProject';
import { Document, ProjectTreeNode } from '../types';

interface ProjectContextValue {
  // 当前项目信息
  currentProject: Project | null;
  projectData: ProjectData | null;
  
  // 项目操作
  switchProject: (projectId: string) => Promise<void>;
  updateProjectData: (data: Partial<ProjectData>) => void;
  reloadProjectData: () => Promise<void>;
  
  // 文档操作
  documents: Document[];
  addDocument: (document: Document) => void;
  updateDocument: (documentId: string, updates: Partial<Document>) => void;
  deleteDocument: (documentId: string) => void;
  
  // 项目树操作
  projectTree: ProjectTreeNode | null;
  updateProjectTree: (tree: ProjectTreeNode) => void;
  
  // 状态
  isLoading: boolean;
  error: string | null;
}

const ProjectContext = createContext<ProjectContextValue | null>(null);

export const useProjectContext = () => {
  const context = useContext(ProjectContext);
  if (!context) {
    throw new Error('useProjectContext must be used within a ProjectProvider');
  }
  return context;
};

export const useProjectContextProvider = () => {
  const [currentProject, setCurrentProject] = useState<Project | null>(null);
  const [projectData, setProjectData] = useState<ProjectData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 切换项目
  const switchProject = useCallback(async (projectId: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // 获取项目信息
      const project = MultiProjectService.getProject(projectId);
      if (!project) {
        throw new Error(`项目不存在: ${projectId}`);
      }

      // 获取项目数据
      let data = ProjectDataManager.getProjectData(projectId);
      if (!data) {
        // 初始化项目数据
        data = ProjectDataManager.initializeProjectData(project);
      }

      // 验证数据完整性
      if (!ProjectDataManager.validateProjectData(projectId)) {
        console.warn(`项目数据完整性检查失败: ${projectId}`);
        // 可以选择重新初始化或修复数据
      }

      setCurrentProject(project);
      setProjectData(data);
      
      // 设置为当前项目
      MultiProjectService.setCurrentProject(projectId);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '切换项目失败';
      setError(errorMessage);
      console.error('切换项目失败:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 更新项目数据
  const updateProjectData = useCallback((updates: Partial<ProjectData>) => {
    if (!currentProject || !projectData) return;

    const newData = { ...projectData, ...updates };
    setProjectData(newData);
    ProjectDataManager.saveProjectData(currentProject.id, newData);
  }, [currentProject, projectData]);

  // 重新加载项目数据
  const reloadProjectData = useCallback(async () => {
    if (!currentProject) return;
    await switchProject(currentProject.id);
  }, [currentProject, switchProject]);

  // 文档操作
  const addDocument = useCallback((document: Document) => {
    if (!projectData) return;
    
    const newDocuments = [...projectData.documents, document];
    updateProjectData({ documents: newDocuments });
  }, [projectData, updateProjectData]);

  const updateDocument = useCallback((documentId: string, updates: Partial<Document>) => {
    if (!projectData) return;
    
    const newDocuments = projectData.documents.map(doc =>
      doc.id === documentId ? { ...doc, ...updates } : doc
    );
    updateProjectData({ documents: newDocuments });
  }, [projectData, updateProjectData]);

  const deleteDocument = useCallback((documentId: string) => {
    if (!projectData) return;
    
    const newDocuments = projectData.documents.filter(doc => doc.id !== documentId);
    updateProjectData({ documents: newDocuments });
  }, [projectData, updateProjectData]);

  // 项目树操作
  const updateProjectTree = useCallback((tree: ProjectTreeNode) => {
    updateProjectData({ projectTree: tree });
  }, [updateProjectData]);

  // 初始化当前项目
  useEffect(() => {
    const initializeCurrentProject = async () => {
      const currentProjectId = MultiProjectService.getCurrentProject()?.id;
      if (currentProjectId) {
        await switchProject(currentProjectId);
      }
    };

    initializeCurrentProject();
  }, [switchProject]);

  return {
    currentProject,
    projectData,
    switchProject,
    updateProjectData,
    reloadProjectData,
    
    // 便捷访问器
    documents: projectData?.documents || [],
    addDocument,
    updateDocument,
    deleteDocument,
    
    projectTree: projectData?.projectTree || null,
    updateProjectTree,
    
    isLoading,
    error
  };
};

export const ProjectProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const value = useProjectContextProvider();
  return (
    <ProjectContext.Provider value={value}>
      {children}
    </ProjectContext.Provider>
  );
};

// 项目隔离验证工具
export const useProjectIsolation = () => {
  const { currentProject } = useProjectContext();
  
  return {
    // 验证当前操作是否在正确的项目上下文中
    validateProjectContext: (expectedProjectId?: string) => {
      if (!currentProject) {
        throw new Error('没有活动的项目上下文');
      }
      
      if (expectedProjectId && currentProject.id !== expectedProjectId) {
        throw new Error(`项目上下文不匹配: 期望 ${expectedProjectId}, 实际 ${currentProject.id}`);
      }
      
      return true;
    },
    
    // 获取项目特定的存储键
    getProjectStorageKey: (key: string) => {
      if (!currentProject) {
        throw new Error('没有活动的项目上下文');
      }
      return `project-${currentProject.id}-${key}`;
    },
    
    // 项目级别的事件发射器
    emitProjectEvent: (event: string, data: any) => {
      if (!currentProject) return;
      
      const customEvent = new CustomEvent(`project-${currentProject.id}-${event}`, {
        detail: data
      });
      window.dispatchEvent(customEvent);
    }
  };
};
