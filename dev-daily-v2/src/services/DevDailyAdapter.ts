import { ProjectTreeNode, Document, ChangeRecord } from '../types/index';
import { marked } from 'marked';

// dev-daily文档适配器
export class DevDailyAdapter {
  
  /**
   * 将现有的dev-daily文档转换为新的Document格式
   */
  static async convertMarkdownToDocument(
    filename: string, 
    content: string, 
    stats: { createdAt: string; updatedAt: string; size: number }
  ): Promise<Document> {
    const id = this.generateDocumentId(filename);
    const title = this.extractTitle(content);
    const type = this.determineDocumentType(filename);
    const tags = this.extractTags(content);
    const wordCount = this.countWords(content);
    
    return {
      id,
      filename,
      title,
      content,
      type,
      status: 'published',
      metadata: {
        createdAt: stats.createdAt,
        updatedAt: stats.updatedAt,
        author: 'dev-daily-user',
        tags,
        fileSize: stats.size,
        wordCount,
        aiAnalysis: await this.generateAIAnalysis(content)
      }
    };
  }

  /**
   * 从现有文档生成项目树结构
   */
  static async generateProjectTreeFromDocuments(documents: Document[]): Promise<ProjectTreeNode> {
    // 分析文档内容，提取项目结构
    const analysis = await this.analyzeDocumentStructure(documents);
    
    // 创建根节点
    const root: ProjectTreeNode = {
      id: 'root',
      name: 'dev-daily项目',
      description: '基于历史记录生成的项目结构',
      type: 'project',
      status: 'active',
      priority: 'high',
      progress: this.calculateOverallProgress(analysis),
      children: [],
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        tags: ['dev-daily', 'migration'],
        linkedDocuments: documents.map(d => d.filename)
      }
    };

    // 基于文档分析创建子节点
    root.children = await this.createChildNodes(analysis, documents);
    
    return root;
  }

  /**
   * 分析文档结构，提取项目信息
   */
  private static async analyzeDocumentStructure(documents: Document[]) {
    const analysis = {
      topics: new Map<string, { count: number; documents: string[]; keywords: string[] }>(),
      timeline: this.buildTimeline(documents),
      features: new Map<string, { status: string; documents: string[] }>(),
      issues: new Map<string, { severity: string; documents: string[] }>()
    };

    for (const doc of documents) {
      // 提取主题
      const topics = this.extractTopicsFromContent(doc.content);
      for (const topic of topics) {
        if (analysis.topics.has(topic.name)) {
          const existing = analysis.topics.get(topic.name)!;
          existing.count += topic.weight;
          existing.documents.push(doc.filename);
          existing.keywords.push(...topic.keywords);
        } else {
          analysis.topics.set(topic.name, {
            count: topic.weight,
            documents: [doc.filename],
            keywords: topic.keywords
          });
        }
      }

      // 提取功能特性
      const features = this.extractFeaturesFromContent(doc.content);
      for (const feature of features) {
        if (analysis.features.has(feature.name)) {
          analysis.features.get(feature.name)!.documents.push(doc.filename);
        } else {
          analysis.features.set(feature.name, {
            status: feature.status,
            documents: [doc.filename]
          });
        }
      }

      // 提取问题和bug
      const issues = this.extractIssuesFromContent(doc.content);
      for (const issue of issues) {
        if (analysis.issues.has(issue.name)) {
          analysis.issues.get(issue.name)!.documents.push(doc.filename);
        } else {
          analysis.issues.set(issue.name, {
            severity: issue.severity,
            documents: [doc.filename]
          });
        }
      }
    }

    return analysis;
  }

  /**
   * 基于分析结果创建子节点
   */
  private static async createChildNodes(analysis: any, documents: Document[]): Promise<ProjectTreeNode[]> {
    const nodes: ProjectTreeNode[] = [];

    // 1. 基于主要主题创建模块节点
    const topTopics = Array.from(analysis.topics.entries()) as [string, any][]
    topTopics.sort(([,a], [,b]) => b.count - a.count);
    const selectedTopics = topTopics.slice(0, 8); // 取前8个主要主题

    for (const [topicName, topicData] of selectedTopics) {
      const moduleNode: ProjectTreeNode = {
        id: this.generateNodeId(topicName),
        name: this.formatTopicName(topicName),
        description: `基于${topicData.documents.length}个文档识别的功能模块`,
        type: 'module',
        status: this.inferModuleStatus(topicData, analysis.features),
        priority: this.inferModulePriority(topicData.count),
        progress: this.calculateModuleProgress(topicName, analysis.features),
        children: [],
        metadata: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          tags: topicData.keywords.slice(0, 5),
          linkedDocuments: topicData.documents
        }
      };

      // 为模块创建任务子节点
      moduleNode.children = this.createTaskNodes(topicName, topicData, analysis);
      nodes.push(moduleNode);
    }

    // 2. 创建问题跟踪节点
    if (analysis.issues.size > 0) {
      const issuesNode: ProjectTreeNode = {
        id: 'issues-tracking',
        name: '问题跟踪',
        description: '项目中发现的问题和bug',
        type: 'module',
        status: 'active',
        priority: 'high',
        progress: this.calculateIssuesProgress(analysis.issues),
        children: this.createIssueNodes(analysis.issues),
        metadata: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          tags: ['issues', 'bugs', 'tracking'],
          linkedDocuments: Array.from(analysis.issues.values()).flatMap((i: any) => i.documents)
        }
      };
      nodes.push(issuesNode);
    }

    // 3. 创建文档管理节点
    const docsNode: ProjectTreeNode = {
      id: 'documentation',
      name: '文档管理',
      description: '项目文档和指南',
      type: 'module',
      status: 'active',
      priority: 'medium',
      progress: 100, // 文档已存在
      children: this.createDocumentNodes(documents),
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        tags: ['documentation', 'guides', 'templates'],
        linkedDocuments: documents.map(d => d.filename)
      }
    };
    nodes.push(docsNode);

    return nodes;
  }

  /**
   * 创建任务节点
   */
  private static createTaskNodes(topicName: string, topicData: any, analysis: any): ProjectTreeNode[] {
    const tasks: ProjectTreeNode[] = [];
    
    // 基于相关功能创建任务
    const allFeatures = Array.from(analysis.features.entries()) as [string, any][];
    const relatedFeatures = allFeatures
      .filter(([name]) => name.toLowerCase().includes(topicName.toLowerCase()) ||
                          topicData.keywords.some((k: string) => name.toLowerCase().includes(k.toLowerCase())))
      .slice(0, 5);

    for (const [featureName, featureData] of relatedFeatures) {
      tasks.push({
        id: this.generateNodeId(`${topicName}-${featureName}`),
        name: featureName,
        description: `${featureName}功能的实现`,
        type: 'task',
        status: this.mapFeatureStatusToNodeStatus(featureData.status),
        priority: 'medium',
        progress: this.calculateFeatureProgress(featureData.status),
        metadata: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          linkedDocuments: featureData.documents
        }
      });
    }

    return tasks;
  }

  /**
   * 从文档内容提取主题
   */
  private static extractTopicsFromContent(content: string) {
    const topics = [];
    
    // 提取标题作为主题
    const headings = content.match(/^#+\s+(.+)$/gm) || [];
    for (const heading of headings) {
      const title = heading.replace(/^#+\s+/, '').trim();
      if (title.length > 3 && title.length < 50) {
        topics.push({
          name: title,
          weight: heading.startsWith('##') ? 2 : 1,
          keywords: this.extractKeywords(title)
        });
      }
    }

    // 提取代码块中的技术栈
    const codeBlocks = content.match(/```(\w+)?\n([\s\S]*?)```/g) || [];
    for (const block of codeBlocks) {
      const language = block.match(/```(\w+)/)?.[1];
      if (language) {
        topics.push({
          name: `${language}开发`,
          weight: 1,
          keywords: [language, 'code', 'development']
        });
      }
    }

    // 提取常见的技术关键词
    const techKeywords = [
      'React', 'Vue', 'Angular', 'Node.js', 'TypeScript', 'JavaScript',
      'API', 'Database', 'Frontend', 'Backend', 'UI', 'UX',
      'Testing', 'Deployment', 'Performance', 'Security'
    ];

    for (const keyword of techKeywords) {
      const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
      const matches = content.match(regex);
      if (matches && matches.length >= 2) {
        topics.push({
          name: `${keyword}相关`,
          weight: matches.length,
          keywords: [keyword.toLowerCase()]
        });
      }
    }

    return topics;
  }

  /**
   * 从内容提取功能特性
   */
  private static extractFeaturesFromContent(content: string) {
    const features = [];
    
    // 查找功能相关的模式
    const featurePatterns = [
      /实现了?\s*([^。\n]+)/g,
      /完成了?\s*([^。\n]+)/g,
      /添加了?\s*([^。\n]+)/g,
      /新增了?\s*([^。\n]+)/g,
      /开发了?\s*([^。\n]+)/g
    ];

    for (const pattern of featurePatterns) {
      const matches = content.matchAll(pattern);
      for (const match of matches) {
        const featureName = match[1].trim();
        if (featureName.length > 5 && featureName.length < 100) {
          features.push({
            name: featureName,
            status: 'completed'
          });
        }
      }
    }

    // 查找计划中的功能
    const plannedPatterns = [
      /计划\s*([^。\n]+)/g,
      /准备\s*([^。\n]+)/g,
      /将要\s*([^。\n]+)/g,
      /下一步\s*([^。\n]+)/g
    ];

    for (const pattern of plannedPatterns) {
      const matches = content.matchAll(pattern);
      for (const match of matches) {
        const featureName = match[1].trim();
        if (featureName.length > 5 && featureName.length < 100) {
          features.push({
            name: featureName,
            status: 'planned'
          });
        }
      }
    }

    return features;
  }

  /**
   * 从内容提取问题
   */
  private static extractIssuesFromContent(content: string) {
    const issues = [];
    
    const issuePatterns = [
      /问题[:：]\s*([^。\n]+)/g,
      /bug[:：]\s*([^。\n]+)/g,
      /错误[:：]\s*([^。\n]+)/g,
      /故障[:：]\s*([^。\n]+)/g,
      /异常[:：]\s*([^。\n]+)/g
    ];

    for (const pattern of issuePatterns) {
      const matches = content.matchAll(pattern);
      for (const match of matches) {
        const issueName = match[1].trim();
        if (issueName.length > 5 && issueName.length < 100) {
          issues.push({
            name: issueName,
            severity: this.inferIssueSeverity(issueName)
          });
        }
      }
    }

    return issues;
  }

  // 辅助方法
  private static generateDocumentId(filename: string): string {
    return filename.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
  }

  private static generateNodeId(name: string): string {
    return name.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '-').toLowerCase() + '-' + Date.now();
  }

  private static extractTitle(content: string): string {
    const firstLine = content.split('\n')[0];
    if (firstLine.startsWith('#')) {
      return firstLine.replace(/^#+\s*/, '').trim();
    }
    return firstLine.trim() || '未命名文档';
  }

  private static determineDocumentType(filename: string): Document['type'] {
    if (filename.includes('template')) return 'template';
    if (filename.includes('guideline')) return 'guideline';
    if (filename.includes('summary')) return 'summary';
    if (filename.includes('issue') || filename.includes('bug')) return 'issue-report';
    if (filename.includes('deploy')) return 'deployment';
    if (/^\d{4}-\d{2}-\d{2}/.test(filename)) return 'daily-log';
    return 'daily-log';
  }

  private static extractTags(content: string): string[] {
    const tags = new Set<string>();
    
    // 从内容中提取标签
    const tagPatterns = [
      /#(\w+)/g,  // #标签
      /标签[:：]\s*([^。\n]+)/g,  // 标签: xxx
      /tags[:：]\s*([^。\n]+)/gi  // tags: xxx
    ];

    for (const pattern of tagPatterns) {
      const matches = content.matchAll(pattern);
      for (const match of matches) {
        const tag = match[1].trim().toLowerCase();
        if (tag.length > 1 && tag.length < 20) {
          tags.add(tag);
        }
      }
    }

    return Array.from(tags).slice(0, 10);
  }

  private static countWords(content: string): number {
    // 简单的中英文字数统计
    const chineseChars = (content.match(/[\u4e00-\u9fa5]/g) || []).length;
    const englishWords = (content.match(/\b[a-zA-Z]+\b/g) || []).length;
    return chineseChars + englishWords;
  }

  private static async generateAIAnalysis(content: string) {
    // 简化的AI分析，实际实现中可以调用真实的AI服务
    const keywords = this.extractKeywords(content);
    const sentiment = this.analyzeSentiment(content);
    const topics = this.extractTopicsFromContent(content).map(t => t.name);
    const complexity = this.assessComplexity(content);

    return {
      summary: this.generateSummary(content),
      keywords: keywords.slice(0, 10),
      sentiment,
      topics: topics.slice(0, 5),
      complexity
    };
  }

  private static extractKeywords(text: string): string[] {
    // 简单的关键词提取
    const words = text.toLowerCase()
      .replace(/[^\w\s\u4e00-\u9fa5]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2);
    
    const frequency = new Map<string, number>();
    for (const word of words) {
      frequency.set(word, (frequency.get(word) || 0) + 1);
    }

    return Array.from(frequency.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 20)
      .map(([word]) => word);
  }

  private static analyzeSentiment(content: string): 'positive' | 'neutral' | 'negative' {
    const positiveWords = ['完成', '成功', '优化', '改进', '解决', '实现'];
    const negativeWords = ['问题', '错误', '失败', '故障', 'bug', '异常'];
    
    let positiveCount = 0;
    let negativeCount = 0;

    for (const word of positiveWords) {
      positiveCount += (content.match(new RegExp(word, 'g')) || []).length;
    }

    for (const word of negativeWords) {
      negativeCount += (content.match(new RegExp(word, 'g')) || []).length;
    }

    if (positiveCount > negativeCount * 1.5) return 'positive';
    if (negativeCount > positiveCount * 1.5) return 'negative';
    return 'neutral';
  }

  private static assessComplexity(content: string): 'low' | 'medium' | 'high' {
    const codeBlocks = (content.match(/```/g) || []).length / 2;
    const technicalTerms = (content.match(/\b(API|database|framework|algorithm|architecture)\b/gi) || []).length;
    const wordCount = this.countWords(content);

    const complexityScore = codeBlocks * 2 + technicalTerms + wordCount / 100;

    if (complexityScore < 5) return 'low';
    if (complexityScore < 15) return 'medium';
    return 'high';
  }

  private static generateSummary(content: string): string {
    const sentences = content.split(/[。！？.!?]/).filter(s => s.trim().length > 10);
    return sentences.slice(0, 2).join('。') + '。';
  }

  // 其他辅助方法...
  private static buildTimeline(documents: Document[]) {
    return documents
      .sort((a, b) => new Date(a.metadata.createdAt).getTime() - new Date(b.metadata.createdAt).getTime())
      .map(doc => ({
        date: doc.metadata.createdAt,
        title: doc.title,
        filename: doc.filename
      }));
  }

  private static calculateOverallProgress(analysis: any): number {
    const completedFeatures = Array.from(analysis.features.values())
      .filter((f: any) => f.status === 'completed').length;
    const totalFeatures = analysis.features.size;
    return totalFeatures > 0 ? Math.round((completedFeatures / totalFeatures) * 100) : 0;
  }

  private static formatTopicName(name: string): string {
    return name.charAt(0).toUpperCase() + name.slice(1);
  }

  private static inferModuleStatus(topicData: any, features: Map<string, any>): ProjectTreeNode['status'] {
    // 基于相关功能的状态推断模块状态
    const relatedFeatures = Array.from(features.entries())
      .filter(([name]) => topicData.keywords.some((k: string) => name.toLowerCase().includes(k.toLowerCase())));
    
    if (relatedFeatures.length === 0) return 'planned';
    
    const completedCount = relatedFeatures.filter(([,f]) => f.status === 'completed').length;
    const totalCount = relatedFeatures.length;
    
    if (completedCount === totalCount) return 'completed';
    if (completedCount > 0) return 'active';
    return 'planned';
  }

  private static inferModulePriority(count: number): ProjectTreeNode['priority'] {
    if (count >= 5) return 'high';
    if (count >= 2) return 'medium';
    return 'low';
  }

  private static calculateModuleProgress(topicName: string, features: Map<string, any>): number {
    const relatedFeatures = Array.from(features.entries())
      .filter(([name]) => name.toLowerCase().includes(topicName.toLowerCase()));
    
    if (relatedFeatures.length === 0) return 0;
    
    const completedCount = relatedFeatures.filter(([,f]) => f.status === 'completed').length;
    return Math.round((completedCount / relatedFeatures.length) * 100);
  }

  private static calculateIssuesProgress(issues: Map<string, any>): number {
    // 假设所有问题都需要解决，这里返回0表示还有工作要做
    return 0;
  }

  private static createIssueNodes(issues: Map<string, any>): ProjectTreeNode[] {
    return Array.from(issues.entries()).map(([issueName, issueData]) => ({
      id: this.generateNodeId(`issue-${issueName}`),
      name: issueName,
      description: `需要解决的问题`,
      type: 'task' as const,
      status: 'blocked' as const,
      priority: issueData.severity === 'high' ? 'high' as const : 'medium' as const,
      progress: 0,
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        tags: ['issue', issueData.severity],
        linkedDocuments: issueData.documents
      }
    }));
  }

  private static createDocumentNodes(documents: Document[]): ProjectTreeNode[] {
    const docTypes = new Map<string, Document[]>();
    
    for (const doc of documents) {
      if (!docTypes.has(doc.type)) {
        docTypes.set(doc.type, []);
      }
      docTypes.get(doc.type)!.push(doc);
    }

    return Array.from(docTypes.entries()).map(([type, docs]) => ({
      id: this.generateNodeId(`docs-${type}`),
      name: this.getDocumentTypeName(type),
      description: `${docs.length}个${this.getDocumentTypeName(type)}`,
      type: 'document' as const,
      status: 'completed' as const,
      priority: 'medium' as const,
      progress: 100,
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        tags: [type, 'documentation'],
        linkedDocuments: docs.map(d => d.filename)
      }
    }));
  }

  private static getDocumentTypeName(type: string): string {
    const typeNames: Record<string, string> = {
      'daily-log': '日常记录',
      'issue-report': '问题报告',
      'deployment': '部署记录',
      'summary': '总结报告',
      'guideline': '开发指南',
      'template': '文档模板'
    };
    return typeNames[type] || type;
  }

  private static mapFeatureStatusToNodeStatus(status: string): ProjectTreeNode['status'] {
    switch (status) {
      case 'completed': return 'completed';
      case 'planned': return 'planned';
      case 'in-progress': return 'active';
      default: return 'planned';
    }
  }

  private static calculateFeatureProgress(status: string): number {
    switch (status) {
      case 'completed': return 100;
      case 'in-progress': return 50;
      case 'planned': return 0;
      default: return 0;
    }
  }

  private static inferIssueSeverity(issueName: string): string {
    const highSeverityKeywords = ['崩溃', '无法', '失败', '错误', '严重'];
    const lowSeverityKeywords = ['优化', '改进', '建议', '小'];
    
    const name = issueName.toLowerCase();
    
    if (highSeverityKeywords.some(keyword => name.includes(keyword))) {
      return 'high';
    }
    
    if (lowSeverityKeywords.some(keyword => name.includes(keyword))) {
      return 'low';
    }
    
    return 'medium';
  }
}
