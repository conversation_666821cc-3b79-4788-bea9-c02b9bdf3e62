/**
 * 增量同步服务 - 智能差异检测和同步
 */

export interface Change {
  type: 'added' | 'modified' | 'deleted';
  path: string;
  oldValue?: any;
  newValue?: any;
  timestamp: number;
}

export interface SyncPatch {
  id: string;
  projectId: string;
  changes: Change[];
  timestamp: number;
  checksum: string;
}

export interface SyncResult {
  success: boolean;
  appliedChanges: number;
  conflicts: Conflict[];
  errors: string[];
}

export interface Conflict {
  path: string;
  localValue: any;
  remoteValue: any;
  type: 'content' | 'structure' | 'metadata';
}

export type SyncStrategy = 'local-wins' | 'remote-wins' | 'merge' | 'manual';

export class IncrementalSyncService {
  private static readonly SYNC_HISTORY_KEY = 'dev-daily-v2-sync-history';
  private static readonly LAST_SYNC_KEY = 'dev-daily-v2-last-sync';
  private static readonly MAX_HISTORY_SIZE = 1000;

  /**
   * 检测数据变化
   */
  static detectChanges(oldData: any, newData: any, basePath = ''): Change[] {
    const changes: Change[] = [];
    const timestamp = Date.now();

    // 深度比较对象
    this.deepCompare(oldData, newData, basePath, changes, timestamp);

    return changes;
  }

  /**
   * 生成同步补丁
   */
  static generatePatch(projectId: string, changes: Change[]): SyncPatch {
    const patch: SyncPatch = {
      id: this.generatePatchId(),
      projectId,
      changes,
      timestamp: Date.now(),
      checksum: this.calculateChecksum(changes)
    };

    // 保存到历史记录
    this.savePatchToHistory(patch);

    return patch;
  }

  /**
   * 应用同步补丁
   */
  static applyPatch(data: any, patch: SyncPatch, strategy: SyncStrategy = 'merge'): SyncResult {
    const result: SyncResult = {
      success: true,
      appliedChanges: 0,
      conflicts: [],
      errors: []
    };

    try {
      for (const change of patch.changes) {
        const applyResult = this.applyChange(data, change, strategy);
        
        if (applyResult.success) {
          result.appliedChanges++;
        } else if (applyResult.conflict) {
          result.conflicts.push(applyResult.conflict);
        } else if (applyResult.error) {
          result.errors.push(applyResult.error);
        }
      }

      // 更新最后同步时间
      this.updateLastSyncTime(patch.projectId, patch.timestamp);

    } catch (error) {
      result.success = false;
      result.errors.push(error instanceof Error ? error.message : '应用补丁失败');
    }

    return result;
  }

  /**
   * 检测冲突
   */
  static detectConflicts(localChanges: Change[], remoteChanges: Change[]): Conflict[] {
    const conflicts: Conflict[] = [];
    
    // 检查路径冲突
    for (const localChange of localChanges) {
      for (const remoteChange of remoteChanges) {
        if (localChange.path === remoteChange.path) {
          // 同一路径的不同修改
          if (localChange.type === 'modified' && remoteChange.type === 'modified') {
            if (!this.deepEqual(localChange.newValue, remoteChange.newValue)) {
              conflicts.push({
                path: localChange.path,
                localValue: localChange.newValue,
                remoteValue: remoteChange.newValue,
                type: 'content'
              });
            }
          }
          // 删除与修改冲突
          else if (
            (localChange.type === 'deleted' && remoteChange.type === 'modified') ||
            (localChange.type === 'modified' && remoteChange.type === 'deleted')
          ) {
            conflicts.push({
              path: localChange.path,
              localValue: localChange.type === 'deleted' ? null : localChange.newValue,
              remoteValue: remoteChange.type === 'deleted' ? null : remoteChange.newValue,
              type: 'structure'
            });
          }
        }
      }
    }

    return conflicts;
  }

  /**
   * 解决冲突
   */
  static resolveConflicts(conflicts: Conflict[], strategy: SyncStrategy): Change[] {
    const resolutions: Change[] = [];

    for (const conflict of conflicts) {
      let resolvedValue: any;

      switch (strategy) {
        case 'local-wins':
          resolvedValue = conflict.localValue;
          break;
        case 'remote-wins':
          resolvedValue = conflict.remoteValue;
          break;
        case 'merge':
          resolvedValue = this.mergeValues(conflict.localValue, conflict.remoteValue);
          break;
        case 'manual':
          // 手动解决，跳过自动处理
          continue;
      }

      resolutions.push({
        type: resolvedValue === null ? 'deleted' : 'modified',
        path: conflict.path,
        newValue: resolvedValue,
        timestamp: Date.now()
      });
    }

    return resolutions;
  }

  /**
   * 获取同步历史
   */
  static getSyncHistory(projectId?: string, limit?: number): SyncPatch[] {
    const history = this.getAllSyncHistory();
    
    let filtered = history;
    if (projectId) {
      filtered = history.filter(patch => patch.projectId === projectId);
    }

    if (limit) {
      filtered = filtered.slice(0, limit);
    }

    return filtered.sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * 获取最后同步时间
   */
  static getLastSyncTime(projectId: string): number {
    const lastSyncData = localStorage.getItem(this.LAST_SYNC_KEY);
    if (lastSyncData) {
      const parsed = JSON.parse(lastSyncData);
      return parsed[projectId] || 0;
    }
    return 0;
  }

  /**
   * 清理同步历史
   */
  static cleanupHistory(olderThan?: number): void {
    const history = this.getAllSyncHistory();
    const cutoff = olderThan || (Date.now() - 30 * 24 * 60 * 60 * 1000); // 30天前
    
    const filtered = history.filter(patch => patch.timestamp > cutoff);
    localStorage.setItem(this.SYNC_HISTORY_KEY, JSON.stringify(filtered));
  }

  // ==================== 私有方法 ====================

  private static deepCompare(
    oldObj: any, 
    newObj: any, 
    path: string, 
    changes: Change[], 
    timestamp: number
  ): void {
    // 处理 null/undefined
    if (oldObj === null || oldObj === undefined) {
      if (newObj !== null && newObj !== undefined) {
        changes.push({
          type: 'added',
          path,
          newValue: newObj,
          timestamp
        });
      }
      return;
    }

    if (newObj === null || newObj === undefined) {
      changes.push({
        type: 'deleted',
        path,
        oldValue: oldObj,
        timestamp
      });
      return;
    }

    // 处理基本类型
    if (typeof oldObj !== 'object' || typeof newObj !== 'object') {
      if (oldObj !== newObj) {
        changes.push({
          type: 'modified',
          path,
          oldValue: oldObj,
          newValue: newObj,
          timestamp
        });
      }
      return;
    }

    // 处理数组
    if (Array.isArray(oldObj) && Array.isArray(newObj)) {
      this.compareArrays(oldObj, newObj, path, changes, timestamp);
      return;
    }

    // 处理对象
    const allKeys = new Set([...Object.keys(oldObj), ...Object.keys(newObj)]);
    
    for (const key of allKeys) {
      const newPath = path ? `${path}.${key}` : key;
      this.deepCompare(oldObj[key], newObj[key], newPath, changes, timestamp);
    }
  }

  private static compareArrays(
    oldArray: any[], 
    newArray: any[], 
    path: string, 
    changes: Change[], 
    timestamp: number
  ): void {
    const maxLength = Math.max(oldArray.length, newArray.length);
    
    for (let i = 0; i < maxLength; i++) {
      const itemPath = `${path}[${i}]`;
      
      if (i >= oldArray.length) {
        // 新增项
        changes.push({
          type: 'added',
          path: itemPath,
          newValue: newArray[i],
          timestamp
        });
      } else if (i >= newArray.length) {
        // 删除项
        changes.push({
          type: 'deleted',
          path: itemPath,
          oldValue: oldArray[i],
          timestamp
        });
      } else {
        // 比较项
        this.deepCompare(oldArray[i], newArray[i], itemPath, changes, timestamp);
      }
    }
  }

  private static applyChange(
    data: any, 
    change: Change, 
    strategy: SyncStrategy
  ): { success: boolean; conflict?: Conflict; error?: string } {
    try {
      const pathParts = change.path.split('.');
      let current = data;
      
      // 导航到父对象
      for (let i = 0; i < pathParts.length - 1; i++) {
        const part = pathParts[i];
        if (!(part in current)) {
          current[part] = {};
        }
        current = current[part];
      }

      const lastPart = pathParts[pathParts.length - 1];

      switch (change.type) {
        case 'added':
        case 'modified':
          current[lastPart] = change.newValue;
          break;
        case 'deleted':
          delete current[lastPart];
          break;
      }

      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : '应用变更失败' 
      };
    }
  }

  private static mergeValues(localValue: any, remoteValue: any): any {
    // 简单的合并策略，可以根据需要扩展
    if (typeof localValue === 'object' && typeof remoteValue === 'object') {
      return { ...localValue, ...remoteValue };
    }
    // 对于非对象类型，优先使用远程值
    return remoteValue;
  }

  private static deepEqual(obj1: any, obj2: any): boolean {
    return JSON.stringify(obj1) === JSON.stringify(obj2);
  }

  private static generatePatchId(): string {
    return `patch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private static calculateChecksum(changes: Change[]): string {
    const content = JSON.stringify(changes);
    // 简单的校验和计算
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(16);
  }

  private static savePatchToHistory(patch: SyncPatch): void {
    const history = this.getAllSyncHistory();
    history.unshift(patch);
    
    // 限制历史记录大小
    if (history.length > this.MAX_HISTORY_SIZE) {
      history.splice(this.MAX_HISTORY_SIZE);
    }
    
    localStorage.setItem(this.SYNC_HISTORY_KEY, JSON.stringify(history));
  }

  private static getAllSyncHistory(): SyncPatch[] {
    const stored = localStorage.getItem(this.SYNC_HISTORY_KEY);
    return stored ? JSON.parse(stored) : [];
  }

  private static updateLastSyncTime(projectId: string, timestamp: number): void {
    const lastSyncData = localStorage.getItem(this.LAST_SYNC_KEY);
    const parsed = lastSyncData ? JSON.parse(lastSyncData) : {};
    
    parsed[projectId] = timestamp;
    localStorage.setItem(this.LAST_SYNC_KEY, JSON.stringify(parsed));
  }
}
