import { ProjectTreeNode, Document, UserSettings } from '../types/index';

export interface BackupData {
  id: string;
  timestamp: string;
  version: string;
  description: string;
  projectTree: ProjectTreeNode;
  documents: Document[];
  userSettings: UserSettings;
  metadata: {
    nodeId?: string;
    nodeName?: string;
    triggerType: 'manual' | 'auto' | 'milestone';
    size: number;
    checksum: string;
  };
}

export interface BackupStrategy {
  autoBackupOnNodeComplete: boolean;
  autoBackupOnMilestone: boolean;
  maxBackupCount: number;
  compressionEnabled: boolean;
  incrementalBackup: boolean;
}

export class BackupService {
  private static readonly STORAGE_KEY = 'dev-daily-v2-backups';
  private static readonly STRATEGY_KEY = 'dev-daily-v2-backup-strategy';
  
  /**
   * 获取备份策略
   */
  static getBackupStrategy(): BackupStrategy {
    const stored = localStorage.getItem(this.STRATEGY_KEY);
    if (stored) {
      return JSON.parse(stored);
    }
    
    // 默认策略
    return {
      autoBackupOnNodeComplete: true,
      autoBackupOnMilestone: true,
      maxBackupCount: 50,
      compressionEnabled: true,
      incrementalBackup: true
    };
  }

  /**
   * 更新备份策略
   */
  static updateBackupStrategy(strategy: Partial<BackupStrategy>): void {
    const current = this.getBackupStrategy();
    const updated = { ...current, ...strategy };
    localStorage.setItem(this.STRATEGY_KEY, JSON.stringify(updated));
  }

  /**
   * 创建备份
   */
  static async createBackup(
    projectTree: ProjectTreeNode,
    documents: Document[],
    userSettings: UserSettings,
    options: {
      description?: string;
      nodeId?: string;
      nodeName?: string;
      triggerType?: 'manual' | 'auto' | 'milestone';
    } = {}
  ): Promise<BackupData> {
    const timestamp = new Date().toISOString();
    const backupId = `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const backupData: BackupData = {
      id: backupId,
      timestamp,
      version: '2.0.0',
      description: options.description || `自动备份 - ${new Date().toLocaleString()}`,
      projectTree: JSON.parse(JSON.stringify(projectTree)), // 深拷贝
      documents: JSON.parse(JSON.stringify(documents)),
      userSettings: JSON.parse(JSON.stringify(userSettings)),
      metadata: {
        nodeId: options.nodeId,
        nodeName: options.nodeName,
        triggerType: options.triggerType || 'manual',
        size: 0,
        checksum: ''
      }
    };

    // 计算备份大小和校验和
    const dataString = JSON.stringify(backupData);
    backupData.metadata.size = new Blob([dataString]).size;
    backupData.metadata.checksum = await this.calculateChecksum(dataString);

    // 保存备份
    await this.saveBackup(backupData);
    
    // 清理旧备份
    await this.cleanupOldBackups();

    return backupData;
  }

  /**
   * 节点完成时的自动备份检查
   */
  static async checkAutoBackupOnNodeComplete(
    nodeId: string,
    nodeName: string,
    projectTree: ProjectTreeNode,
    documents: Document[],
    userSettings: UserSettings
  ): Promise<boolean> {
    const strategy = this.getBackupStrategy();
    
    if (!strategy.autoBackupOnNodeComplete) {
      return false;
    }

    // 显示备份确认对话框
    const shouldBackup = await this.showBackupConfirmDialog(
      `节点 "${nodeName}" 已完成`,
      '是否创建备份点以保存当前进度？'
    );

    if (shouldBackup) {
      await this.createBackup(projectTree, documents, userSettings, {
        description: `节点完成备份: ${nodeName}`,
        nodeId,
        nodeName,
        triggerType: 'auto'
      });
      return true;
    }

    return false;
  }

  /**
   * 里程碑备份检查
   */
  static async checkMilestoneBackup(
    milestoneId: string,
    milestoneName: string,
    projectTree: ProjectTreeNode,
    documents: Document[],
    userSettings: UserSettings
  ): Promise<boolean> {
    const strategy = this.getBackupStrategy();
    
    if (!strategy.autoBackupOnMilestone) {
      return false;
    }

    const shouldBackup = await this.showBackupConfirmDialog(
      `里程碑 "${milestoneName}" 已达成`,
      '是否创建里程碑备份？'
    );

    if (shouldBackup) {
      await this.createBackup(projectTree, documents, userSettings, {
        description: `里程碑备份: ${milestoneName}`,
        nodeId: milestoneId,
        nodeName: milestoneName,
        triggerType: 'milestone'
      });
      return true;
    }

    return false;
  }

  /**
   * 获取所有备份
   */
  static async getAllBackups(): Promise<BackupData[]> {
    const stored = localStorage.getItem(this.STORAGE_KEY);
    if (!stored) {
      return [];
    }

    try {
      const backups = JSON.parse(stored) as BackupData[];
      return backups.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    } catch (error) {
      console.error('Failed to parse backups:', error);
      return [];
    }
  }

  /**
   * 恢复备份
   */
  static async restoreBackup(backupId: string): Promise<{
    projectTree: ProjectTreeNode;
    documents: Document[];
    userSettings: UserSettings;
  } | null> {
    const backups = await this.getAllBackups();
    const backup = backups.find(b => b.id === backupId);
    
    if (!backup) {
      throw new Error(`备份 ${backupId} 不存在`);
    }

    // 验证备份完整性
    const isValid = await this.validateBackup(backup);
    if (!isValid) {
      throw new Error('备份文件已损坏，无法恢复');
    }

    return {
      projectTree: backup.projectTree,
      documents: backup.documents,
      userSettings: backup.userSettings
    };
  }

  /**
   * 删除备份
   */
  static async deleteBackup(backupId: string): Promise<void> {
    const backups = await this.getAllBackups();
    const filteredBackups = backups.filter(b => b.id !== backupId);
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredBackups));
  }

  /**
   * 导出备份到文件
   */
  static async exportBackup(backupId: string): Promise<void> {
    const backup = (await this.getAllBackups()).find(b => b.id === backupId);
    if (!backup) {
      throw new Error('备份不存在');
    }

    const dataStr = JSON.stringify(backup, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `dev-daily-v2-backup-${backup.timestamp.split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  /**
   * 从文件导入备份
   */
  static async importBackup(file: File): Promise<BackupData> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const backup = JSON.parse(e.target?.result as string) as BackupData;
          
          // 验证备份格式
          if (!backup.id || !backup.projectTree || !backup.documents) {
            throw new Error('无效的备份文件格式');
          }

          // 生成新的ID避免冲突
          backup.id = `imported_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          backup.description = `导入备份: ${backup.description}`;

          await this.saveBackup(backup);
          resolve(backup);
        } catch (error) {
          reject(new Error('备份文件解析失败: ' + (error as Error).message));
        }
      };
      reader.readAsText(file);
    });
  }

  /**
   * 保存备份到本地存储
   */
  private static async saveBackup(backup: BackupData): Promise<void> {
    const backups = await this.getAllBackups();
    backups.unshift(backup);
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(backups));
  }

  /**
   * 清理旧备份
   */
  private static async cleanupOldBackups(): Promise<void> {
    const strategy = this.getBackupStrategy();
    const backups = await this.getAllBackups();
    
    if (backups.length > strategy.maxBackupCount) {
      const toKeep = backups.slice(0, strategy.maxBackupCount);
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(toKeep));
    }
  }

  /**
   * 计算校验和
   */
  private static async calculateChecksum(data: string): Promise<string> {
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  /**
   * 验证备份完整性
   */
  private static async validateBackup(backup: BackupData): Promise<boolean> {
    try {
      const dataString = JSON.stringify({
        ...backup,
        metadata: {
          ...backup.metadata,
          checksum: ''
        }
      });
      const calculatedChecksum = await this.calculateChecksum(dataString);
      return calculatedChecksum === backup.metadata.checksum;
    } catch {
      return false;
    }
  }

  /**
   * 显示备份确认对话框
   */
  private static async showBackupConfirmDialog(title: string, message: string): Promise<boolean> {
    return new Promise((resolve) => {
      const confirmed = window.confirm(`${title}\n\n${message}`);
      resolve(confirmed);
    });
  }
}
