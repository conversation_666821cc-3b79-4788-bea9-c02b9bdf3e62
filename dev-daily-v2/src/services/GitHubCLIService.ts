/**
 * GitHub CLI 集成服务 - 使用 gh CLI 进行更稳定的 GitHub 操作
 */

export interface GitHubCLIConfig {
  projectId: string;
  repoUrl: string;
  branch: string;
  localPath: string;
  autoAuth: boolean;
}

export interface CLIResult {
  success: boolean;
  output: string;
  error?: string;
  exitCode: number;
}

export interface RepoInfo {
  name: string;
  owner: string;
  description: string;
  private: boolean;
  defaultBranch: string;
  url: string;
  cloneUrl: string;
}

export class GitHubCLIService {
  private static readonly CONFIG_KEY = 'dev-daily-v2-gh-cli-configs';

  /**
   * 检查 GitHub CLI 是否已安装和认证
   */
  static async checkCLIStatus(): Promise<{
    installed: boolean;
    authenticated: boolean;
    user?: string;
    version?: string;
  }> {
    try {
      // 检查是否安装
      const versionResult = await this.executeCommand(['--version']);
      if (!versionResult.success) {
        return { installed: false, authenticated: false };
      }

      // 检查认证状态
      const authResult = await this.executeCommand(['auth', 'status']);
      const authenticated = authResult.success;
      
      let user: string | undefined;
      if (authenticated) {
        const userResult = await this.executeCommand(['api', 'user', '--jq', '.login']);
        if (userResult.success) {
          user = userResult.output.trim();
        }
      }

      return {
        installed: true,
        authenticated,
        user,
        version: versionResult.output.trim()
      };
    } catch (error) {
      return { installed: false, authenticated: false };
    }
  }

  /**
   * 初始化项目的 GitHub 集成
   */
  static async initializeProject(config: GitHubCLIConfig): Promise<CLIResult> {
    try {
      // 1. 检查本地目录是否存在
      const dirExists = await this.checkDirectory(config.localPath);
      if (!dirExists) {
        return {
          success: false,
          output: '',
          error: `本地目录不存在: ${config.localPath}`,
          exitCode: 1
        };
      }

      // 2. 检查是否已经是 Git 仓库
      const isGitRepo = await this.executeCommand(['repo', 'view'], config.localPath);
      
      if (!isGitRepo.success) {
        // 3. 克隆或初始化仓库
        if (config.repoUrl) {
          return await this.cloneRepository(config.repoUrl, config.localPath);
        } else {
          return await this.initializeRepository(config.localPath);
        }
      }

      return {
        success: true,
        output: '项目已初始化',
        exitCode: 0
      };
    } catch (error) {
      return {
        success: false,
        output: '',
        error: error instanceof Error ? error.message : '初始化失败',
        exitCode: 1
      };
    }
  }

  /**
   * 推送项目数据到 GitHub
   */
  static async pushProjectData(
    projectId: string,
    commitMessage: string,
    files?: string[]
  ): Promise<CLIResult> {
    const config = this.getConfig(projectId);
    if (!config) {
      return {
        success: false,
        output: '',
        error: '未找到项目配置',
        exitCode: 1
      };
    }

    try {
      const commands: string[][] = [];

      // 1. 添加文件
      if (files && files.length > 0) {
        commands.push(['add', ...files]);
      } else {
        commands.push(['add', '.']);
      }

      // 2. 提交
      commands.push(['commit', '-m', commitMessage]);

      // 3. 推送
      commands.push(['push', 'origin', config.branch]);

      // 执行命令序列
      for (const command of commands) {
        const result = await this.executeCommand(command, config.localPath);
        if (!result.success && !result.output.includes('nothing to commit')) {
          return result;
        }
      }

      return {
        success: true,
        output: '推送成功',
        exitCode: 0
      };
    } catch (error) {
      return {
        success: false,
        output: '',
        error: error instanceof Error ? error.message : '推送失败',
        exitCode: 1
      };
    }
  }

  /**
   * 从 GitHub 拉取最新数据
   */
  static async pullProjectData(projectId: string): Promise<CLIResult> {
    const config = this.getConfig(projectId);
    if (!config) {
      return {
        success: false,
        output: '',
        error: '未找到项目配置',
        exitCode: 1
      };
    }

    try {
      // 拉取最新代码
      const result = await this.executeCommand(['pull', 'origin', config.branch], config.localPath);
      return result;
    } catch (error) {
      return {
        success: false,
        output: '',
        error: error instanceof Error ? error.message : '拉取失败',
        exitCode: 1
      };
    }
  }

  /**
   * 获取仓库信息
   */
  static async getRepositoryInfo(projectId: string): Promise<RepoInfo | null> {
    const config = this.getConfig(projectId);
    if (!config) return null;

    try {
      const result = await this.executeCommand([
        'repo', 'view', '--json', 
        'name,owner,description,isPrivate,defaultBranchRef,url,sshUrl'
      ], config.localPath);

      if (result.success) {
        const data = JSON.parse(result.output);
        return {
          name: data.name,
          owner: data.owner.login,
          description: data.description || '',
          private: data.isPrivate,
          defaultBranch: data.defaultBranchRef.name,
          url: data.url,
          cloneUrl: data.sshUrl
        };
      }
      return null;
    } catch (error) {
      console.error('获取仓库信息失败:', error);
      return null;
    }
  }

  /**
   * 创建 Pull Request
   */
  static async createPullRequest(
    projectId: string,
    title: string,
    body: string,
    head: string,
    base: string = 'main'
  ): Promise<CLIResult> {
    const config = this.getConfig(projectId);
    if (!config) {
      return {
        success: false,
        output: '',
        error: '未找到项目配置',
        exitCode: 1
      };
    }

    try {
      const result = await this.executeCommand([
        'pr', 'create',
        '--title', title,
        '--body', body,
        '--head', head,
        '--base', base
      ], config.localPath);

      return result;
    } catch (error) {
      return {
        success: false,
        output: '',
        error: error instanceof Error ? error.message : '创建 PR 失败',
        exitCode: 1
      };
    }
  }

  /**
   * 设置 Webhook
   */
  static async setupWebhook(
    projectId: string,
    webhookUrl: string,
    events: string[] = ['push', 'pull_request']
  ): Promise<CLIResult> {
    const config = this.getConfig(projectId);
    if (!config) {
      return {
        success: false,
        output: '',
        error: '未找到项目配置',
        exitCode: 1
      };
    }

    try {
      // 使用 GitHub CLI 的 API 命令设置 webhook
      const webhookData = {
        name: 'web',
        active: true,
        events: events,
        config: {
          url: webhookUrl,
          content_type: 'json'
        }
      };

      const result = await this.executeCommand([
        'api', 'repos/:owner/:repo/hooks',
        '--method', 'POST',
        '--input', '-'
      ], config.localPath, JSON.stringify(webhookData));

      return result;
    } catch (error) {
      return {
        success: false,
        output: '',
        error: error instanceof Error ? error.message : '设置 Webhook 失败',
        exitCode: 1
      };
    }
  }

  // ==================== 配置管理 ====================

  static saveConfig(config: GitHubCLIConfig): void {
    const configs = this.getAllConfigs();
    configs[config.projectId] = config;
    localStorage.setItem(this.CONFIG_KEY, JSON.stringify(configs));
  }

  static getConfig(projectId: string): GitHubCLIConfig | null {
    const configs = this.getAllConfigs();
    return configs[projectId] || null;
  }

  private static getAllConfigs(): Record<string, GitHubCLIConfig> {
    const stored = localStorage.getItem(this.CONFIG_KEY);
    return stored ? JSON.parse(stored) : {};
  }

  // ==================== 私有方法 ====================

  private static async executeCommand(
    args: string[],
    cwd?: string,
    input?: string
  ): Promise<CLIResult> {
    try {
      // 注意：在实际的 Web 应用中，这需要通过 Electron 或其他方式调用系统命令
      // 这里提供接口设计，实际实现需要根据部署环境调整
      
      if (typeof window !== 'undefined' && (window as any).electronAPI) {
        // Electron 环境
        return await (window as any).electronAPI.executeGitHubCLI(args, cwd, input);
      } else if (false) {
        // Node.js 环境代码已禁用，避免在浏览器中导入 child_process
        // 如果需要在 Node.js 环境中使用，请启用此代码块
        return {
          success: false,
          output: '',
          error: 'Node.js 环境支持已禁用',
          exitCode: 1
        };
      } else {
        // 浏览器环境 - 需要后端 API 支持
        const response = await fetch('/api/github-cli', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ args, cwd, input })
        });
        
        return await response.json();
      }
    } catch (error) {
      return {
        success: false,
        output: '',
        error: error instanceof Error ? error.message : '命令执行失败',
        exitCode: 1
      };
    }
  }

  private static async checkDirectory(path: string): Promise<boolean> {
    try {
      // 检查目录是否存在的实现
      return true; // 简化实现
    } catch {
      return false;
    }
  }

  private static async cloneRepository(repoUrl: string, localPath: string): Promise<CLIResult> {
    return await this.executeCommand(['repo', 'clone', repoUrl, localPath]);
  }

  private static async initializeRepository(localPath: string): Promise<CLIResult> {
    return await this.executeCommand(['repo', 'create', '--source', '.'], localPath);
  }

  /**
   * 获取安装指南
   */
  static getInstallationGuide(): {
    platform: string;
    instructions: string[];
    downloadUrl: string;
  } {
    const platform = this.detectPlatform();
    
    const guides = {
      windows: {
        platform: 'Windows',
        instructions: [
          '1. 下载 GitHub CLI Windows 安装包',
          '2. 运行安装程序并按照提示完成安装',
          '3. 打开命令提示符或 PowerShell',
          '4. 运行 "gh auth login" 进行认证',
          '5. 按照提示完成 GitHub 账户认证'
        ],
        downloadUrl: 'https://github.com/cli/cli/releases/latest'
      },
      macos: {
        platform: 'macOS',
        instructions: [
          '1. 使用 Homebrew 安装: brew install gh',
          '2. 或下载 macOS 安装包手动安装',
          '3. 打开终端',
          '4. 运行 "gh auth login" 进行认证',
          '5. 按照提示完成 GitHub 账户认证'
        ],
        downloadUrl: 'https://github.com/cli/cli/releases/latest'
      },
      linux: {
        platform: 'Linux',
        instructions: [
          '1. Ubuntu/Debian: sudo apt install gh',
          '2. CentOS/RHEL: sudo yum install gh',
          '3. 或下载对应发行版的安装包',
          '4. 打开终端',
          '5. 运行 "gh auth login" 进行认证',
          '6. 按照提示完成 GitHub 账户认证'
        ],
        downloadUrl: 'https://github.com/cli/cli/releases/latest'
      }
    };

    return guides[platform as keyof typeof guides] || guides.linux;
  }

  private static detectPlatform(): string {
    if (typeof navigator !== 'undefined') {
      const userAgent = navigator.userAgent.toLowerCase();
      if (userAgent.includes('win')) return 'windows';
      if (userAgent.includes('mac')) return 'macos';
      return 'linux';
    }
    return 'linux';
  }
}
