import { Document, ProjectTreeNode } from '../types/index';

// 测试数据服务 - 提供模拟数据用于功能验证
export class TestDataService {
  
  /**
   * 生成测试文档数据
   */
  static generateTestDocuments(): Document[] {
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    return [
      {
        id: 'doc-project-overview',
        filename: 'project-overview.md',
        title: 'dev-daily v2 项目概览',
        content: `# dev-daily v2 项目概览

## 项目简介
dev-daily v2 是基于Project Tree概念的智能开发日志管理系统，集成了AI助手和可视化项目管理功能。

## 主要特性
- 🌳 **Project Tree管理**: 可视化的项目结构管理
- 🤖 **AI智能助手**: 智能建议和代码生成
- 📝 **文档管理**: 结构化的文档组织和搜索
- 🔄 **数据迁移**: 从v1无缝升级到v2
- 📊 **可视化仪表板**: 项目进度和统计信息

## 技术栈
- **前端**: React 18 + TypeScript + Tailwind CSS
- **状态管理**: Zustand
- **构建工具**: Vite
- **图标**: Lucide React
- **日期处理**: date-fns

## 项目目标
1. 提升开发效率 30%+
2. 改善项目管理体验
3. 集成AI辅助功能
4. 保持向后兼容性`,
        type: 'guideline',
        status: 'published',
        metadata: {
          createdAt: lastWeek.toISOString(),
          updatedAt: yesterday.toISOString(),
          author: 'dev-daily-team',
          tags: ['overview', 'project', 'v2', 'guideline'],
          wordCount: 280,
          aiAnalysis: {
            summary: '介绍dev-daily v2项目的主要特性和技术栈',
            keywords: ['project-tree', 'ai-assistant', 'react', 'typescript'],
            sentiment: 'positive',
            topics: ['项目管理', '技术栈', 'AI功能'],
            complexity: 'medium'
          }
        }
      },
      {
        id: 'doc-daily-migration',
        filename: `${now.toISOString().split('T')[0]}-migration-progress.md`,
        title: '迁移进度记录',
        content: `# ${now.toISOString().split('T')[0]} - 迁移进度记录

## 今日目标
- ✅ 完成Phase 4.2核心功能迁移
- ✅ 实现Project Tree组件
- ✅ 集成AI功能
- 🔄 测试功能完整性

## 完成情况

### Project Tree功能
- ✅ 动态项目树组件开发完成
- ✅ 拖拽重组功能实现
- ✅ 节点CRUD操作完成
- ✅ 状态管理和历史记录

### AI功能集成
- ✅ DevDailyAdapter适配器完成
- ✅ 智能文档分析实现
- ✅ 项目树自动生成
- ✅ 关键词和主题提取

### 用户界面
- ✅ 主仪表板开发完成
- ✅ 文档管理器实现
- ✅ 响应式设计
- ✅ 搜索和过滤功能

## 遇到的问题
暂无重大问题，开发进展顺利。

## 下一步计划
- 功能完整性测试
- 性能优化
- 用户体验改进
- 准备预览版本

## 技术亮点
1. **智能项目树生成**: 基于文档内容自动生成项目结构
2. **实时双向同步**: Markdown与结构化数据同步
3. **AI增强分析**: 专门优化的智能分析功能`,
        type: 'daily-log',
        status: 'published',
        metadata: {
          createdAt: now.toISOString(),
          updatedAt: now.toISOString(),
          author: 'dev-daily-user',
          tags: ['migration', 'progress', 'v2', 'daily'],
          wordCount: 320,
          aiAnalysis: {
            summary: '记录dev-daily v2迁移的进展情况和技术亮点',
            keywords: ['migration', 'project-tree', 'ai', 'progress'],
            sentiment: 'positive',
            topics: ['迁移进度', '功能开发', '技术实现'],
            complexity: 'medium'
          }
        }
      },
      {
        id: 'doc-feature-analysis',
        filename: `${yesterday.toISOString().split('T')[0]}-feature-analysis.md`,
        title: '功能特性分析',
        content: `# ${yesterday.toISOString().split('T')[0]} - 功能特性分析

## 核心功能对比

### v1 vs v2 功能对比

| 功能 | v1 | v2 | 改进 |
|------|----|----|------|
| 文档管理 | ✅ 基础 | ✅ 增强 | 搜索、过滤、分类 |
| 项目结构 | ❌ 无 | ✅ 完整 | Project Tree可视化 |
| AI功能 | ❌ 无 | ✅ 完整 | 智能分析和建议 |
| 用户界面 | 📝 文档 | 🖥️ Web | 现代化界面 |

## 新增功能详解

### 1. Project Tree管理
- **可视化结构**: 树状图展示项目层次
- **拖拽操作**: 直观的结构调整
- **状态追踪**: 实时进度和状态管理
- **历史记录**: 完整的变更历史

### 2. AI智能助手
- **文档分析**: 自动提取关键信息
- **项目生成**: 基于内容生成项目结构
- **智能建议**: 优化和改进建议
- **情感分析**: 项目情绪和复杂度评估

### 3. 数据迁移
- **无缝升级**: 从v1平滑迁移到v2
- **数据保护**: 完整的备份和恢复
- **格式转换**: Markdown到结构化数据
- **验证机制**: 迁移完整性检查

## 用户体验改进
1. **学习成本**: 从纯文档到可视化界面
2. **操作效率**: 拖拽操作比手动编辑快50%
3. **信息发现**: 智能搜索和过滤
4. **项目理解**: 可视化结构更直观`,
        type: 'summary',
        status: 'published',
        metadata: {
          createdAt: yesterday.toISOString(),
          updatedAt: yesterday.toISOString(),
          author: 'dev-daily-analyst',
          tags: ['analysis', 'features', 'comparison', 'v2'],
          wordCount: 420,
          aiAnalysis: {
            summary: '详细分析dev-daily v2的功能特性和改进点',
            keywords: ['features', 'comparison', 'improvement', 'analysis'],
            sentiment: 'positive',
            topics: ['功能对比', '用户体验', '技术改进'],
            complexity: 'high'
          }
        }
      },
      {
        id: 'doc-issue-tracking',
        filename: `${lastWeek.toISOString().split('T')[0]}-issue-tracking.md`,
        title: '问题跟踪记录',
        content: `# ${lastWeek.toISOString().split('T')[0]} - 问题跟踪记录

## 已解决问题

### 1. TypeScript类型定义
**问题**: 组件间类型不一致导致编译错误
**解决**: 创建统一的类型定义文件 \`types/index.ts\`
**状态**: ✅ 已解决

### 2. 拖拽功能实现
**问题**: 节点拖拽时状态更新不及时
**解决**: 优化状态管理逻辑，使用useCallback优化性能
**状态**: ✅ 已解决

### 3. 数据同步机制
**问题**: Markdown文件与结构化数据同步冲突
**解决**: 实现双向同步机制和冲突解决策略
**状态**: ✅ 已解决

## 待解决问题

### 1. 性能优化
**问题**: 大量节点时渲染性能下降
**优先级**: 中
**计划**: 实现虚拟化和懒加载

### 2. 移动端适配
**问题**: 移动设备上拖拽体验不佳
**优先级**: 低
**计划**: 优化触摸交互

## 技术债务
1. **测试覆盖**: 需要增加单元测试
2. **文档完善**: API文档需要补充
3. **错误处理**: 异常情况处理需要加强

## 经验总结
1. **组件复用**: Personal Dashboard的组件复用率达到95%
2. **类型安全**: TypeScript显著减少了运行时错误
3. **渐进迁移**: 分阶段迁移策略效果良好`,
        type: 'issue-report',
        status: 'published',
        metadata: {
          createdAt: lastWeek.toISOString(),
          updatedAt: lastWeek.toISOString(),
          author: 'dev-daily-developer',
          tags: ['issues', 'tracking', 'bugs', 'solutions'],
          wordCount: 380,
          aiAnalysis: {
            summary: '记录开发过程中遇到的问题和解决方案',
            keywords: ['issues', 'solutions', 'typescript', 'performance'],
            sentiment: 'neutral',
            topics: ['问题解决', '技术债务', '经验总结'],
            complexity: 'medium'
          }
        }
      },
      {
        id: 'doc-deployment-guide',
        filename: 'deployment-guide.md',
        title: '部署指南',
        content: `# dev-daily v2 部署指南

## 环境要求
- Node.js 18+
- npm 或 yarn
- 现代浏览器 (Chrome 90+, Firefox 88+, Safari 14+)

## 本地开发环境

### 1. 安装依赖
\`\`\`bash
cd dev-daily-v2
npm install
\`\`\`

### 2. 启动开发服务器
\`\`\`bash
npm run dev
\`\`\`

### 3. 数据迁移 (可选)
\`\`\`bash
npm run migrate
\`\`\`

## 生产环境部署

### 1. 构建项目
\`\`\`bash
npm run build
\`\`\`

### 2. 预览构建结果
\`\`\`bash
npm run preview
\`\`\`

### 3. 部署到静态托管
构建产物在 \`dist/\` 目录，可以部署到：
- Vercel
- Netlify
- GitHub Pages
- Cloudflare Pages

## 配置选项

### 环境变量
\`\`\`env
VITE_APP_TITLE=dev-daily v2
VITE_API_BASE_URL=http://localhost:3000
VITE_AI_ENABLED=true
\`\`\`

### 功能开关
- AI功能: 可通过配置启用/禁用
- 备份功能: 自动备份间隔设置
- 主题模式: 支持明暗主题切换

## 故障排除

### 常见问题
1. **依赖安装失败**: 清除缓存后重新安装
2. **构建错误**: 检查TypeScript类型错误
3. **运行时错误**: 查看浏览器控制台日志

### 性能优化
1. **代码分割**: 已配置路由级别的代码分割
2. **资源压缩**: 生产构建自动压缩
3. **缓存策略**: 静态资源长期缓存`,
        type: 'guideline',
        status: 'published',
        metadata: {
          createdAt: lastWeek.toISOString(),
          updatedAt: yesterday.toISOString(),
          author: 'dev-daily-ops',
          tags: ['deployment', 'guide', 'production', 'setup'],
          wordCount: 350,
          aiAnalysis: {
            summary: '详细的部署指南和环境配置说明',
            keywords: ['deployment', 'setup', 'configuration', 'production'],
            sentiment: 'neutral',
            topics: ['部署配置', '环境设置', '故障排除'],
            complexity: 'medium'
          }
        }
      }
    ];
  }

  /**
   * 生成测试项目树数据 - 使用ABCD模块化结构
   */
  static generateTestProjectTree(): ProjectTreeNode {
    const now = new Date().toISOString();

    return {
      id: 'ROOT',
      name: 'dev-daily v2 项目',
      description: '基于Project Tree的智能开发日志管理系统',
      type: 'project',
      status: 'active',
      priority: 'high',
      progress: 75,
      children: [
        // A模块: 环境与依赖
        {
          id: 'A',
          name: '🌍 A模块: 环境与依赖',
          description: '项目环境配置和依赖版本管理',
          type: 'module',
          status: 'active',
          priority: 'high',
          progress: 95,
          children: [
            {
              id: 'A1',
              name: 'A1: Node.js 环境',
              description: 'Node.js v18.x LTS 运行环境',
              type: 'task',
              status: 'completed',
              priority: 'high',
              progress: 100,
              metadata: {
                createdAt: '2024-12-01T09:00:00.000Z',
                updatedAt: '2024-12-01T10:00:00.000Z',
                estimatedHours: 2,
                actualHours: 1,
                tags: ['nodejs', 'environment', 'v18']
              }
            },
            {
              id: 'A2',
              name: 'A2: TypeScript 配置',
              description: 'TypeScript 5.x 类型系统配置',
              type: 'task',
              status: 'completed',
              priority: 'high',
              progress: 100,
              metadata: {
                createdAt: '2024-12-02T09:00:00.000Z',
                updatedAt: '2024-12-02T12:00:00.000Z',
                estimatedHours: 3,
                actualHours: 2,
                tags: ['typescript', 'configuration', 'v5']
              }
            },
            {
              id: 'A3',
              name: 'A3: React 生态系统',
              description: 'React 18 + 相关依赖版本管理',
              type: 'task',
              status: 'completed',
              priority: 'high',
              progress: 100,
              metadata: {
                createdAt: now,
                updatedAt: now,
                estimatedHours: 4,
                actualHours: 3,
                tags: ['react', 'ecosystem', 'v18']
              }
            },
            {
              id: 'A4',
              name: 'A4: Vite 构建工具',
              description: 'Vite 5.x 现代化构建配置',
              type: 'task',
              status: 'completed',
              priority: 'medium',
              progress: 100,
              metadata: {
                createdAt: now,
                updatedAt: now,
                estimatedHours: 2,
                actualHours: 2,
                tags: ['vite', 'build', 'v5']
              }
            },
            {
              id: 'A5',
              name: 'A5: Cloudflare 部署',
              description: 'Cloudflare Pages 部署环境配置',
              type: 'task',
              status: 'active',
              priority: 'medium',
              progress: 90,
              metadata: {
                createdAt: now,
                updatedAt: now,
                estimatedHours: 3,
                actualHours: 2,
                tags: ['cloudflare', 'deployment', 'pages']
              }
            }
          ],
          metadata: {
            createdAt: '2024-12-01T09:00:00.000Z',
            updatedAt: '2024-12-05T17:00:00.000Z',
            estimatedHours: 15,
            actualHours: 12,
            tags: ['environment', 'dependencies', 'versions'],
            linkedDocuments: ['package.json', 'tsconfig.json', 'vite.config.ts']
          }
        },
        // B模块: 核心功能
        {
          id: 'B',
          name: '⚡ B模块: 核心功能',
          description: '项目的核心功能实现',
          type: 'module',
          status: 'active',
          priority: 'high',
          progress: 90,
          children: [
            {
              id: 'B1',
              name: 'B1: Project Tree管理',
              description: '可视化的项目结构管理功能',
              type: 'feature',
              status: 'completed',
              priority: 'high',
              progress: 100,
              metadata: {
                createdAt: '2024-12-06T09:00:00.000Z',
                updatedAt: '2024-12-12T18:00:00.000Z',
                estimatedHours: 40,
                actualHours: 35,
                tags: ['project-tree', 'visualization', 'management'],
                linkedDocuments: ['project-overview.md']
              }
            },
            {
              id: 'B2',
              name: 'B2: 文档管理系统',
              description: '结构化的文档组织和搜索功能',
              type: 'feature',
              status: 'completed',
              priority: 'high',
              progress: 95,
              metadata: {
                createdAt: now,
                updatedAt: now,
                estimatedHours: 30,
                actualHours: 28,
                tags: ['documents', 'search', 'management'],
                linkedDocuments: ['deployment-guide.md']
              }
            },
            {
              id: 'B3',
              name: 'B3: AI功能集成',
              description: '智能分析和建议系统',
              type: 'feature',
              status: 'testing',
              priority: 'high',
              progress: 80,
              metadata: {
                createdAt: now,
                updatedAt: now,
                estimatedHours: 50,
                actualHours: 40,
                tags: ['ai', 'analysis', 'suggestions'],
                linkedDocuments: ['feature-analysis.md']
              }
            },
            {
              id: 'B4',
              name: 'B4: 思维模型系统',
              description: '多维度分析框架和AI辅助决策',
              type: 'feature',
              status: 'completed',
              priority: 'high',
              progress: 100,
              metadata: {
                createdAt: now,
                updatedAt: now,
                estimatedHours: 35,
                actualHours: 32,
                tags: ['thinking-models', 'analysis', 'decision-support'],
                linkedDocuments: ['thinking-models-guide.md']
              }
            }
          ],
          metadata: {
            createdAt: '2024-12-06T09:00:00.000Z',
            updatedAt: '2024-12-20T18:00:00.000Z',
            estimatedHours: 120,
            actualHours: 105,
            tags: ['core', 'features'],
            linkedDocuments: ['project-overview.md', 'feature-analysis.md']
          }
        },
        // C模块: 用户界面
        {
          id: 'C',
          name: '🎨 C模块: 用户界面',
          description: '现代化的Web用户界面',
          type: 'module',
          status: 'active',
          priority: 'medium',
          progress: 85,
          children: [
            {
              id: 'C1',
              name: 'C1: 主仪表板',
              description: '项目概览和导航界面',
              type: 'feature',
              status: 'completed',
              priority: 'high',
              progress: 100,
              metadata: {
                createdAt: now,
                updatedAt: now,
                estimatedHours: 20,
                actualHours: 18,
                tags: ['dashboard', 'navigation', 'overview']
              }
            },
            {
              id: 'C2',
              name: 'C2: 响应式设计',
              description: '适配不同设备的界面设计',
              type: 'task',
              status: 'active',
              priority: 'medium',
              progress: 70,
              metadata: {
                createdAt: now,
                updatedAt: now,
                estimatedHours: 15,
                actualHours: 10,
                tags: ['responsive', 'mobile', 'design']
              }
            },
            {
              id: 'C3',
              name: 'C3: 思维模型界面',
              description: '多维度分析框架的用户界面',
              type: 'feature',
              status: 'completed',
              priority: 'high',
              progress: 100,
              metadata: {
                createdAt: now,
                updatedAt: now,
                estimatedHours: 25,
                actualHours: 23,
                tags: ['thinking-models', 'ui', 'analysis']
              }
            }
          ],
          metadata: {
            createdAt: now,
            updatedAt: now,
            tags: ['ui', 'interface'],
            linkedDocuments: []
          }
        },
        // D模块: 数据与迁移
        {
          id: 'D',
          name: '📊 D模块: 数据与迁移',
          description: '从v1到v2的数据迁移功能',
          type: 'module',
          status: 'completed',
          priority: 'high',
          progress: 100,
          children: [
            {
              id: 'D1',
              name: 'D1: 迁移脚本',
              description: '自动化的数据迁移脚本',
              type: 'task',
              status: 'completed',
              priority: 'high',
              progress: 100,
              metadata: {
                createdAt: now,
                updatedAt: now,
                estimatedHours: 25,
                actualHours: 22,
                tags: ['migration', 'script', 'automation'],
                linkedDocuments: ['migration-progress.md']
              }
            },
            {
              id: 'D2',
              name: 'D2: 数据验证',
              description: '迁移数据的完整性验证',
              type: 'task',
              status: 'completed',
              priority: 'high',
              progress: 100,
              metadata: {
                createdAt: now,
                updatedAt: now,
                estimatedHours: 15,
                actualHours: 12,
                tags: ['validation', 'integrity', 'testing']
              }
            },
            {
              id: 'D3',
              name: 'D3: 备份与恢复',
              description: '项目数据备份和灾难恢复机制',
              type: 'task',
              status: 'active',
              priority: 'medium',
              progress: 75,
              metadata: {
                createdAt: now,
                updatedAt: now,
                estimatedHours: 20,
                actualHours: 15,
                tags: ['backup', 'recovery', 'disaster-recovery']
              }
            }
          ],
          metadata: {
            createdAt: now,
            updatedAt: now,
            tags: ['migration', 'data'],
            linkedDocuments: ['migration-progress.md']
          }
        }
      ],
      metadata: {
        createdAt: now,
        updatedAt: now,
        estimatedHours: 200,
        actualHours: 150,
        tags: ['dev-daily', 'v2', 'project-management'],
        linkedDocuments: [
          'project-overview.md',
          'migration-progress.md',
          'feature-analysis.md',
          'deployment-guide.md',
          'issue-tracking.md'
        ]
      }
    };
  }

  /**
   * 获取项目统计信息
   */
  static getProjectStats(projectTree: ProjectTreeNode, documents: Document[]) {
    const countNodes = (node: ProjectTreeNode): number => {
      let count = 1;
      if (node.children) {
        count += node.children.reduce((sum, child) => sum + countNodes(child), 0);
      }
      return count;
    };

    const countNodesByStatus = (node: ProjectTreeNode, status: ProjectTreeNode['status']): number => {
      let count = node.status === status ? 1 : 0;
      if (node.children) {
        count += node.children.reduce((sum, child) => sum + countNodesByStatus(child, status), 0);
      }
      return count;
    };

    return {
      totalNodes: countNodes(projectTree),
      completedNodes: countNodesByStatus(projectTree, 'completed'),
      activeNodes: countNodesByStatus(projectTree, 'active'),
      blockedNodes: countNodesByStatus(projectTree, 'blocked'),
      totalDocuments: documents.length,
      recentDocuments: documents.filter(doc => {
        const daysDiff = (Date.now() - new Date(doc.metadata.createdAt).getTime()) / (1000 * 60 * 60 * 24);
        return daysDiff <= 7;
      }).length,
      totalWords: documents.reduce((sum, doc) => sum + (doc.metadata.wordCount || 0), 0),
      overallProgress: Math.round(
        (countNodesByStatus(projectTree, 'completed') / countNodes(projectTree)) * 100
      )
    };
  }
}
