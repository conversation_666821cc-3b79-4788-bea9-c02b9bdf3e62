/**
 * Service Worker 管理服务
 */

export interface ServiceWorkerStatus {
  supported: boolean;
  registered: boolean;
  active: boolean;
  waiting: boolean;
  version?: string;
}

export interface OfflineStatus {
  isOnline: boolean;
  lastOnline?: Date;
  syncPending: boolean;
}

export class ServiceWorkerService {
  private static registration: ServiceWorkerRegistration | null = null;
  private static listeners: Map<string, Function[]> = new Map();

  /**
   * 注册 Service Worker
   */
  static async register(): Promise<boolean> {
    if (!('serviceWorker' in navigator)) {
      console.warn('当前浏览器不支持 Service Worker');
      return false;
    }

    try {
      this.registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      });

      console.log('Service Worker 注册成功:', this.registration.scope);

      // 监听更新
      this.registration.addEventListener('updatefound', () => {
        this.handleUpdateFound();
      });

      // 监听状态变化
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        this.emit('controllerchange');
      });

      // 检查是否有等待的 Service Worker
      if (this.registration.waiting) {
        this.emit('waiting', this.registration.waiting);
      }

      return true;
    } catch (error) {
      console.error('Service Worker 注册失败:', error);
      return false;
    }
  }

  /**
   * 获取 Service Worker 状态
   */
  static async getStatus(): Promise<ServiceWorkerStatus> {
    const status: ServiceWorkerStatus = {
      supported: 'serviceWorker' in navigator,
      registered: false,
      active: false,
      waiting: false
    };

    if (!status.supported) {
      return status;
    }

    if (this.registration) {
      status.registered = true;
      status.active = !!this.registration.active;
      status.waiting = !!this.registration.waiting;

      // 获取版本信息
      if (this.registration.active) {
        try {
          const version = await this.sendMessage({ type: 'GET_VERSION' });
          status.version = version?.version;
        } catch (error) {
          console.warn('获取 Service Worker 版本失败:', error);
        }
      }
    }

    return status;
  }

  /**
   * 获取离线状态
   */
  static getOfflineStatus(): OfflineStatus {
    const isOnline = navigator.onLine;
    const lastOnlineStr = localStorage.getItem('dev-daily-v2-last-online');
    const lastOnline = lastOnlineStr ? new Date(lastOnlineStr) : undefined;
    const syncPending = localStorage.getItem('dev-daily-v2-sync-pending') === 'true';

    return {
      isOnline,
      lastOnline,
      syncPending
    };
  }

  /**
   * 跳过等待，立即激活新的 Service Worker
   */
  static async skipWaiting(): Promise<void> {
    if (this.registration?.waiting) {
      await this.sendMessage({ type: 'SKIP_WAITING' });
    }
  }

  /**
   * 清理所有缓存
   */
  static async clearCache(): Promise<boolean> {
    try {
      const result = await this.sendMessage({ type: 'CLEAR_CACHE' });
      return result?.success || false;
    } catch (error) {
      console.error('清理缓存失败:', error);
      return false;
    }
  }

  /**
   * 发送消息给 Service Worker
   */
  static async sendMessage(message: any): Promise<any> {
    if (!this.registration?.active) {
      throw new Error('Service Worker 未激活');
    }

    return new Promise((resolve, reject) => {
      const messageChannel = new MessageChannel();
      
      messageChannel.port1.onmessage = (event) => {
        resolve(event.data);
      };

      messageChannel.port1.addEventListener('messageerror', (error) => {
        reject(error);
      });

      this.registration!.active!.postMessage(message, [messageChannel.port2]);
    });
  }

  /**
   * 监听事件
   */
  static addEventListener(event: string, listener: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(listener);
  }

  /**
   * 移除事件监听器
   */
  static removeEventListener(event: string, listener: Function): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index !== -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 初始化离线状态监听
   */
  static initializeOfflineDetection(): void {
    // 监听在线/离线状态变化
    window.addEventListener('online', () => {
      localStorage.setItem('dev-daily-v2-last-online', new Date().toISOString());
      this.emit('online');
    });

    window.addEventListener('offline', () => {
      this.emit('offline');
    });

    // 初始化最后在线时间
    if (navigator.onLine) {
      localStorage.setItem('dev-daily-v2-last-online', new Date().toISOString());
    }
  }

  /**
   * 检查网络连接质量
   */
  static async checkNetworkQuality(): Promise<{
    online: boolean;
    speed: 'fast' | 'slow' | 'offline';
    latency?: number;
  }> {
    if (!navigator.onLine) {
      return { online: false, speed: 'offline' };
    }

    try {
      const start = performance.now();
      const response = await fetch('/favicon.ico', { 
        method: 'HEAD',
        cache: 'no-cache'
      });
      const end = performance.now();
      
      const latency = end - start;
      
      return {
        online: response.ok,
        speed: latency < 100 ? 'fast' : 'slow',
        latency
      };
    } catch (error) {
      return { online: false, speed: 'offline' };
    }
  }

  /**
   * 设置同步待处理状态
   */
  static setSyncPending(pending: boolean): void {
    localStorage.setItem('dev-daily-v2-sync-pending', pending.toString());
    this.emit('syncPendingChange', pending);
  }

  /**
   * 预缓存重要资源
   */
  static async precacheResources(urls: string[]): Promise<void> {
    if (!('caches' in window)) {
      console.warn('当前浏览器不支持 Cache API');
      return;
    }

    try {
      const cache = await caches.open('dev-daily-v2-precache');
      await cache.addAll(urls);
      console.log('预缓存完成:', urls.length, '个资源');
    } catch (error) {
      console.error('预缓存失败:', error);
    }
  }

  /**
   * 获取缓存使用情况
   */
  static async getCacheUsage(): Promise<{
    used: number;
    quota: number;
    percentage: number;
  }> {
    if (!('storage' in navigator) || !('estimate' in navigator.storage)) {
      return { used: 0, quota: 0, percentage: 0 };
    }

    try {
      const estimate = await navigator.storage.estimate();
      const used = estimate.usage || 0;
      const quota = estimate.quota || 0;
      const percentage = quota > 0 ? (used / quota) * 100 : 0;

      return { used, quota, percentage };
    } catch (error) {
      console.error('获取存储使用情况失败:', error);
      return { used: 0, quota: 0, percentage: 0 };
    }
  }

  // ==================== 私有方法 ====================

  private static handleUpdateFound(): void {
    if (!this.registration) return;

    const newWorker = this.registration.installing;
    if (!newWorker) return;

    newWorker.addEventListener('statechange', () => {
      if (newWorker.state === 'installed') {
        if (navigator.serviceWorker.controller) {
          // 有新版本可用
          this.emit('updateAvailable', newWorker);
        } else {
          // 首次安装完成
          this.emit('installed', newWorker);
        }
      }
    });
  }

  private static emit(event: string, data?: any): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error('事件监听器执行失败:', error);
        }
      });
    }
  }
}

// 自动初始化
if (typeof window !== 'undefined') {
  // 页面加载完成后注册 Service Worker
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      ServiceWorkerService.register();
      ServiceWorkerService.initializeOfflineDetection();
    });
  } else {
    ServiceWorkerService.register();
    ServiceWorkerService.initializeOfflineDetection();
  }
}
