/**
 * 本地文件系统服务 - 真实的文件访问和监控
 * 注意：由于浏览器安全限制，需要用户主动选择文件/目录
 */

// 临时类型声明，直到浏览器 API 类型完全支持
declare global {
  interface FileSystemDirectoryHandle {
    entries(): AsyncIterableIterator<[string, FileSystemHandle]>;
  }
  interface FileSystemFileHandle {
    getFile(): Promise<File>;
  }
}

export interface FileSystemHandle {
  kind: 'file' | 'directory';
  name: string;
  path?: string;
}

export interface ProjectFileStructure {
  root: string;
  files: FileInfo[];
  directories: DirectoryInfo[];
  gitInfo?: GitInfo;
  packageInfo?: PackageInfo;
}

export interface FileInfo {
  name: string;
  path: string;
  size: number;
  lastModified: Date;
  type: string;
  content?: string;
}

export interface DirectoryInfo {
  name: string;
  path: string;
  fileCount: number;
  subdirectories: string[];
}

export interface GitInfo {
  isRepository: boolean;
  branch?: string;
  remoteUrl?: string;
  lastCommit?: string;
  status?: 'clean' | 'dirty';
}

export interface PackageInfo {
  name: string;
  version: string;
  description: string;
  scripts: Record<string, string>;
  dependencies: Record<string, string>;
  devDependencies: Record<string, string>;
}

export class LocalFileSystemService {
  private static readonly SUPPORTED_EXTENSIONS = [
    '.md', '.txt', '.json', '.js', '.ts', '.tsx', '.jsx',
    '.py', '.java', '.cpp', '.c', '.h', '.css', '.scss',
    '.html', '.xml', '.yaml', '.yml', '.toml', '.ini'
  ];

  private static readonly PROJECT_INDICATORS = [
    'package.json', 'Cargo.toml', 'pom.xml', 'build.gradle',
    'requirements.txt', 'Pipfile', 'composer.json', 'go.mod'
  ];

  /**
   * 请求用户选择项目目录
   */
  static async selectProjectDirectory(): Promise<FileSystemDirectoryHandle | null> {
    try {
      // 检查浏览器支持
      if (!('showDirectoryPicker' in window)) {
        throw new Error('浏览器不支持文件系统访问 API');
      }

      const dirHandle = await (window as any).showDirectoryPicker({
        mode: 'readwrite',
        startIn: 'documents'
      });

      return dirHandle;
    } catch (error) {
      if ((error as Error).name === 'AbortError') {
        console.log('用户取消了目录选择');
        return null;
      }
      console.error('选择目录失败:', error);
      throw error;
    }
  }

  /**
   * 扫描项目目录结构
   */
  static async scanProjectDirectory(dirHandle: FileSystemDirectoryHandle): Promise<ProjectFileStructure> {
    const files: FileInfo[] = [];
    const directories: DirectoryInfo[] = [];
    
    try {
      await this.scanDirectoryRecursive(dirHandle, '', files, directories);
      
      const structure: ProjectFileStructure = {
        root: dirHandle.name,
        files,
        directories,
        gitInfo: await this.detectGitInfo(dirHandle),
        packageInfo: await this.detectPackageInfo(files)
      };

      return structure;
    } catch (error) {
      console.error('扫描目录失败:', error);
      throw error;
    }
  }

  /**
   * 递归扫描目录
   */
  private static async scanDirectoryRecursive(
    dirHandle: FileSystemDirectoryHandle,
    currentPath: string,
    files: FileInfo[],
    directories: DirectoryInfo[],
    maxDepth: number = 5,
    currentDepth: number = 0
  ): Promise<void> {
    if (currentDepth >= maxDepth) return;

    const subdirs: string[] = [];
    let fileCount = 0;

    for await (const [name, handle] of dirHandle.entries()) {
      // 跳过隐藏文件和常见的忽略目录
      if (this.shouldIgnore(name)) continue;

      const fullPath = currentPath ? `${currentPath}/${name}` : name;

      if (handle.kind === 'file') {
        const file = await (handle as FileSystemFileHandle).getFile();
        const fileInfo: FileInfo = {
          name,
          path: fullPath,
          size: file.size,
          lastModified: new Date(file.lastModified),
          type: this.getFileType(name)
        };

        // 读取支持的文件内容
        if (this.isSupportedFile(name) && file.size < 1024 * 1024) { // 限制1MB
          try {
            fileInfo.content = await file.text();
          } catch (error) {
            console.warn(`读取文件内容失败: ${fullPath}`, error);
          }
        }

        files.push(fileInfo);
        fileCount++;
      } else if (handle.kind === 'directory') {
        subdirs.push(name);
        await this.scanDirectoryRecursive(
          handle as FileSystemDirectoryHandle,
          fullPath,
          files,
          directories,
          maxDepth,
          currentDepth + 1
        );
      }
    }

    if (currentPath) {
      directories.push({
        name: currentPath.split('/').pop() || '',
        path: currentPath,
        fileCount,
        subdirectories: subdirs
      });
    }
  }

  /**
   * 检测 Git 信息
   */
  private static async detectGitInfo(dirHandle: FileSystemDirectoryHandle): Promise<GitInfo | undefined> {
    try {
      // 检查是否存在 .git 目录
      const gitHandle = await dirHandle.getDirectoryHandle('.git');
      if (!gitHandle) return undefined;

      // 尝试读取 HEAD 文件获取当前分支
      let branch: string | undefined;
      try {
        const headHandle = await gitHandle.getFileHandle('HEAD');
        const headFile = await headHandle.getFile();
        const headContent = await headFile.text();
        const match = headContent.match(/ref: refs\/heads\/(.+)/);
        if (match) {
          branch = match[1].trim();
        }
      } catch (error) {
        console.warn('读取 Git HEAD 失败:', error);
      }

      return {
        isRepository: true,
        branch,
        status: 'unknown' as any // 需要更复杂的逻辑来检测状态
      };
    } catch (error) {
      return {
        isRepository: false
      };
    }
  }

  /**
   * 检测包信息
   */
  private static async detectPackageInfo(files: FileInfo[]): Promise<PackageInfo | undefined> {
    const packageFile = files.find(f => f.name === 'package.json');
    if (!packageFile || !packageFile.content) return undefined;

    try {
      const packageData = JSON.parse(packageFile.content);
      return {
        name: packageData.name || '',
        version: packageData.version || '',
        description: packageData.description || '',
        scripts: packageData.scripts || {},
        dependencies: packageData.dependencies || {},
        devDependencies: packageData.devDependencies || {}
      };
    } catch (error) {
      console.warn('解析 package.json 失败:', error);
      return undefined;
    }
  }

  /**
   * 监控文件变化（模拟实现）
   */
  static createFileWatcher(
    dirHandle: FileSystemDirectoryHandle,
    callback: (changes: FileChangeEvent[]) => void
  ): FileWatcher {
    return new FileWatcher(dirHandle, callback);
  }

  /**
   * 工具方法
   */
  private static shouldIgnore(name: string): boolean {
    const ignorePatterns = [
      /^\./, // 隐藏文件
      /^node_modules$/,
      /^\.git$/,
      /^dist$/,
      /^build$/,
      /^target$/,
      /^__pycache__$/,
      /\.pyc$/,
      /\.class$/
    ];

    return ignorePatterns.some(pattern => pattern.test(name));
  }

  private static isSupportedFile(name: string): boolean {
    return this.SUPPORTED_EXTENSIONS.some(ext => name.endsWith(ext));
  }

  private static getFileType(name: string): string {
    const ext = name.split('.').pop()?.toLowerCase();
    const typeMap: Record<string, string> = {
      'md': 'markdown',
      'js': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript-react',
      'jsx': 'javascript-react',
      'json': 'json',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'css': 'css',
      'scss': 'scss',
      'html': 'html',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml'
    };

    return typeMap[ext || ''] || 'text';
  }

  /**
   * 检查浏览器支持
   */
  static isSupported(): boolean {
    return 'showDirectoryPicker' in window;
  }

  /**
   * 获取支持信息
   */
  static getSupportInfo(): { supported: boolean; message: string } {
    if (this.isSupported()) {
      return {
        supported: true,
        message: '浏览器支持文件系统访问 API'
      };
    }

    return {
      supported: false,
      message: '浏览器不支持文件系统访问 API，请使用 Chrome 86+ 或 Edge 86+'
    };
  }
}

/**
 * 文件监控器（简化实现）
 */
export interface FileChangeEvent {
  type: 'added' | 'modified' | 'deleted';
  path: string;
  timestamp: Date;
}

export class FileWatcher {
  private isActive = false;
  private intervalId?: number;

  constructor(
    private dirHandle: FileSystemDirectoryHandle,
    private callback: (changes: FileChangeEvent[]) => void
  ) {}

  start(interval: number = 5000): void {
    if (this.isActive) return;

    this.isActive = true;
    this.intervalId = window.setInterval(async () => {
      try {
        // 简化的文件变化检测
        // 实际实现需要比较文件的修改时间等
        const changes: FileChangeEvent[] = [];
        // TODO: 实现真实的文件变化检测逻辑
        
        if (changes.length > 0) {
          this.callback(changes);
        }
      } catch (error) {
        console.error('文件监控错误:', error);
      }
    }, interval);
  }

  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = undefined;
    }
    this.isActive = false;
  }

  isRunning(): boolean {
    return this.isActive;
  }
}
