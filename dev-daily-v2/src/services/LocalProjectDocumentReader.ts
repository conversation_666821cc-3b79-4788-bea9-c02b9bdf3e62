/**
 * 本地项目文档读取器
 * 通过读取项目目录中的 .dev-daily/ 文档来获取项目信息
 */

export interface ProjectDocument {
  project: string;      // project.md 内容
  progress: string;     // progress.md 内容  
  config: any;          // config.json 内容
  tasks?: string;       // tasks.md 内容 (可选)
  notes?: string;       // notes.md 内容 (可选)
}

export interface ProjectInfo {
  id: string;
  name: string;
  description: string;
  type: string;
  status: string;
  progress: number;
  technology: any;
  structure: any;
  lastModified: string;
  healthScore: number;
  path: string;
}

export class LocalProjectDocumentReader {
  private static readonly DEV_DAILY_FOLDER = '.dev-daily';
  
  /**
   * 模拟文件读取 - 在实际应用中需要用户手动提供文档内容
   */
  static async readProjectDocuments(projectPath: string): Promise<ProjectDocument | null> {
    try {
      // 在实际应用中，这里需要用户通过文件选择器或拖拽提供文档内容
      // 目前返回模拟数据来演示功能
      
      const mockDocuments: ProjectDocument = {
        project: `# 📊 Cloudflare 用户行为分析系统

## 🎯 项目基本信息
- **项目名称**: Cloudflare 用户行为分析系统
- **项目类型**: 全栈 Web 应用 + Serverless
- **创建时间**: 2025-01-10
- **当前版本**: v1.0.0-alpha
- **开发状态**: 🚧 开发中

## 🏗️ 技术架构
### 核心技术栈
- **前端**: React + TypeScript + Tailwind CSS
- **后端**: Cloudflare Workers + Hono
- **数据库**: Cloudflare D1 (SQLite)
- **分析引擎**: Cloudflare Analytics Engine`,

        progress: `# 📈 项目开发进度

## 🎯 总体进度: 25%

### 📊 模块完成度
| 模块 | 进度 | 状态 | 备注 |
|------|------|------|------|
| 🌍 A模块: 环境与依赖 | 60% | 🚧 进行中 | 基础环境已搭建 |
| ⚡ B模块: 核心功能 | 15% | ⏳ 计划中 | 数据采集器已完成 |
| 🎨 C模块: 用户界面 | 0% | ⏳ 计划中 | 待开始 |
| 📊 D模块: 数据与集成 | 5% | ⏳ 计划中 | 数据库设计已完成 |`,

        config: {
          "project": {
            "id": "cloudflare-user-analytics",
            "name": "Cloudflare 用户行为分析系统",
            "version": "1.0.0-alpha",
            "type": "web-application",
            "status": "development",
            "priority": "high"
          },
          "progress": {
            "overall": 25,
            "modules": {
              "environment": 60,
              "core": 15,
              "ui": 0,
              "data": 5
            }
          },
          "technology": {
            "frontend": {
              "framework": "React",
              "language": "TypeScript"
            },
            "backend": {
              "runtime": "Cloudflare Workers",
              "framework": "Hono"
            }
          }
        }
      };
      
      return mockDocuments;
      
    } catch (error) {
      console.error('读取项目文档失败:', error);
      return null;
    }
  }

  /**
   * 解析项目文档，提取项目信息
   */
  static parseProjectInfo(documents: ProjectDocument, projectPath: string): ProjectInfo {
    const config = documents.config;
    
    return {
      id: config.project?.id || 'unknown',
      name: config.project?.name || '未知项目',
      description: config.metadata?.description || '无描述',
      type: config.project?.type || 'unknown',
      status: config.project?.status || 'unknown',
      progress: config.progress?.overall || 0,
      technology: config.technology || {},
      structure: config.structure || {},
      lastModified: config.metadata?.lastModified || new Date().toISOString(),
      healthScore: config.quality?.healthScore || 50,
      path: projectPath
    };
  }

  /**
   * 从项目路径加载项目信息
   */
  static async loadProjectFromPath(projectPath: string): Promise<ProjectInfo | null> {
    try {
      const documents = await this.readProjectDocuments(projectPath);
      if (!documents) {
        return null;
      }
      
      return this.parseProjectInfo(documents, projectPath);
      
    } catch (error) {
      console.error('加载项目失败:', error);
      return null;
    }
  }

  /**
   * 验证项目目录是否包含 .dev-daily 文档
   */
  static async validateProjectDirectory(projectPath: string): Promise<boolean> {
    // 在实际应用中，这里需要检查目录是否存在 .dev-daily 文件夹
    // 目前返回模拟结果
    return projectPath.includes('cloudflare-user-analytics');
  }

  /**
   * 获取项目文档的更新时间
   */
  static async getDocumentLastModified(projectPath: string): Promise<Date | null> {
    try {
      const documents = await this.readProjectDocuments(projectPath);
      if (documents?.config?.metadata?.lastModified) {
        return new Date(documents.config.metadata.lastModified);
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * 批量加载多个项目
   */
  static async loadMultipleProjects(projectPaths: string[]): Promise<ProjectInfo[]> {
    const projects: ProjectInfo[] = [];
    
    for (const path of projectPaths) {
      try {
        const project = await this.loadProjectFromPath(path);
        if (project) {
          projects.push(project);
        }
      } catch (error) {
        console.error(`加载项目 ${path} 失败:`, error);
      }
    }
    
    return projects;
  }

  /**
   * 创建项目文档模板
   */
  static generateProjectDocumentTemplate(projectName: string, projectType: string): ProjectDocument {
    const now = new Date().toISOString();
    
    return {
      project: `# 📊 ${projectName}

## 🎯 项目基本信息
- **项目名称**: ${projectName}
- **项目类型**: ${projectType}
- **创建时间**: ${now.split('T')[0]}
- **当前版本**: v1.0.0-alpha
- **开发状态**: 🚧 开发中

## 🏗️ 技术架构
### 核心技术栈
- **前端**: (待填写)
- **后端**: (待填写)
- **数据库**: (待填写)

## 🎯 项目目标
(待填写项目目标和功能描述)`,

      progress: `# 📈 项目开发进度

## 🎯 总体进度: 0%

### 📊 模块完成度
| 模块 | 进度 | 状态 | 备注 |
|------|------|------|------|
| 基础设施 | 0% | ⏳ 计划中 | 待开始 |
| 核心功能 | 0% | ⏳ 计划中 | 待开始 |
| 用户界面 | 0% | ⏳ 计划中 | 待开始 |

## 📅 开发时间线
### ${now.split('T')[0]} (今日)
- 🎯 项目初始化

## 🏆 已完成任务
(暂无)

## 🚧 进行中任务
(暂无)

## ⏳ 待开始任务
(待规划)`,

      config: {
        "project": {
          "id": projectName.toLowerCase().replace(/\s+/g, '-'),
          "name": projectName,
          "version": "1.0.0-alpha",
          "type": projectType,
          "status": "planning",
          "priority": "medium"
        },
        "metadata": {
          "created": now,
          "lastModified": now,
          "createdBy": "dev-daily-v2-system",
          "tags": [],
          "description": ""
        },
        "progress": {
          "overall": 0,
          "modules": {}
        },
        "technology": {},
        "quality": {
          "healthScore": 50
        }
      }
    };
  }
}
