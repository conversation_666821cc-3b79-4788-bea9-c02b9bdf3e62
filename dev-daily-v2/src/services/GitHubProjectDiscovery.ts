/**
 * GitHub 项目发现服务 - 通过 API 读取 GitHub 项目信息并同步到项目管理平台
 */

export interface GitHubRepository {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  private: boolean;
  html_url: string;
  clone_url: string;
  ssh_url: string;
  language: string | null;
  stargazers_count: number;
  forks_count: number;
  open_issues_count: number;
  default_branch: string;
  created_at: string;
  updated_at: string;
  pushed_at: string;
  size: number;
  topics: string[];
  license: {
    key: string;
    name: string;
  } | null;
  owner: {
    login: string;
    type: string;
    avatar_url: string;
  };
}

export interface GitHubIssue {
  id: number;
  number: number;
  title: string;
  body: string | null;
  state: 'open' | 'closed';
  labels: Array<{
    name: string;
    color: string;
    description: string | null;
  }>;
  assignee: {
    login: string;
    avatar_url: string;
  } | null;
  milestone: {
    title: string;
    description: string | null;
    due_on: string | null;
    state: 'open' | 'closed';
  } | null;
  created_at: string;
  updated_at: string;
  closed_at: string | null;
}

export interface GitHubPullRequest {
  id: number;
  number: number;
  title: string;
  body: string | null;
  state: 'open' | 'closed' | 'merged';
  head: {
    ref: string;
    sha: string;
  };
  base: {
    ref: string;
    sha: string;
  };
  user: {
    login: string;
    avatar_url: string;
  };
  created_at: string;
  updated_at: string;
  merged_at: string | null;
}

export interface ProjectSyncConfig {
  githubToken: string;
  includePrivateRepos: boolean;
  syncIssues: boolean;
  syncPullRequests: boolean;
  syncMilestones: boolean;
  filterByTopics: string[];
  filterByLanguage: string[];
  excludeArchived: boolean;
  excludeForked: boolean;
}

export interface DiscoveredProject {
  source: 'github';
  repository: GitHubRepository;
  issues: GitHubIssue[];
  pullRequests: GitHubPullRequest[];
  milestones: any[];
  projectStructure: ProjectStructure;
  estimatedComplexity: 'low' | 'medium' | 'high';
  suggestedPlatform: 'notion' | 'airtable' | 'both';
}

export interface ProjectStructure {
  hasReadme: boolean;
  hasDocumentation: boolean;
  hasTests: boolean;
  hasCICD: boolean;
  packageManager: string | null;
  framework: string | null;
  directories: string[];
  fileTypes: Record<string, number>;
}

export class GitHubProjectDiscovery {
  private static readonly API_BASE = 'https://api.github.com';

  /**
   * 发现用户的所有 GitHub 项目
   */
  static async discoverUserProjects(
    config: ProjectSyncConfig
  ): Promise<{ success: boolean; projects: DiscoveredProject[]; errors: string[] }> {
    const result = {
      success: true,
      projects: [] as DiscoveredProject[],
      errors: [] as string[]
    };

    try {
      // 1. 获取用户仓库列表
      const repositories = await this.getUserRepositories(config);
      
      // 2. 过滤仓库
      const filteredRepos = this.filterRepositories(repositories, config);
      
      // 3. 分析每个仓库
      for (const repo of filteredRepos) {
        try {
          const project = await this.analyzeRepository(repo, config);
          result.projects.push(project);
        } catch (error) {
          result.errors.push(`分析仓库 ${repo.name} 失败: ${error}`);
        }
      }

      result.success = result.errors.length === 0;
      return result;
    } catch (error) {
      result.success = false;
      result.errors.push(error instanceof Error ? error.message : '发现项目失败');
      return result;
    }
  }

  /**
   * 发现组织的项目
   */
  static async discoverOrgProjects(
    orgName: string,
    config: ProjectSyncConfig
  ): Promise<{ success: boolean; projects: DiscoveredProject[]; errors: string[] }> {
    const result = {
      success: true,
      projects: [] as DiscoveredProject[],
      errors: [] as string[]
    };

    try {
      const repositories = await this.getOrgRepositories(orgName, config);
      const filteredRepos = this.filterRepositories(repositories, config);
      
      for (const repo of filteredRepos) {
        try {
          const project = await this.analyzeRepository(repo, config);
          result.projects.push(project);
        } catch (error) {
          result.errors.push(`分析仓库 ${repo.name} 失败: ${error}`);
        }
      }

      result.success = result.errors.length === 0;
      return result;
    } catch (error) {
      result.success = false;
      result.errors.push(error instanceof Error ? error.message : '发现组织项目失败');
      return result;
    }
  }

  /**
   * 同步项目到指定平台
   */
  static async syncProjectsToPlatform(
    projects: DiscoveredProject[],
    platformType: 'notion' | 'airtable',
    platformConfig: any
  ): Promise<{ success: boolean; syncedProjects: number; errors: string[] }> {
    const result = {
      success: true,
      syncedProjects: 0,
      errors: [] as string[]
    };

    try {
      for (const project of projects) {
        try {
          if (platformType === 'notion') {
            await this.syncToNotion(project, platformConfig);
          } else if (platformType === 'airtable') {
            await this.syncToAirtable(project, platformConfig);
          }
          result.syncedProjects++;
        } catch (error) {
          result.errors.push(`同步项目 ${project.repository.name} 失败: ${error}`);
        }
      }

      result.success = result.errors.length === 0;
      return result;
    } catch (error) {
      result.success = false;
      result.errors.push(error instanceof Error ? error.message : '同步项目失败');
      return result;
    }
  }

  /**
   * 生成项目清单报告
   */
  static generateProjectInventory(projects: DiscoveredProject[]): string {
    const timestamp = new Date().toISOString().split('T')[0];
    
    let report = `# GitHub 项目清单报告\n\n`;
    report += `**生成时间**: ${new Date().toLocaleString()}\n`;
    report += `**项目总数**: ${projects.length}\n\n`;

    // 统计信息
    const stats = this.calculateProjectStats(projects);
    report += `## 📊 统计概览\n\n`;
    report += `- **编程语言分布**:\n`;
    Object.entries(stats.languages).forEach(([lang, count]) => {
      report += `  - ${lang}: ${count} 个项目\n`;
    });
    
    report += `\n- **项目复杂度**:\n`;
    report += `  - 低复杂度: ${stats.complexity.low} 个\n`;
    report += `  - 中等复杂度: ${stats.complexity.medium} 个\n`;
    report += `  - 高复杂度: ${stats.complexity.high} 个\n`;

    report += `\n- **推荐平台**:\n`;
    report += `  - Notion: ${stats.platforms.notion} 个\n`;
    report += `  - Airtable: ${stats.platforms.airtable} 个\n`;
    report += `  - 两者皆可: ${stats.platforms.both} 个\n`;

    // 项目详情
    report += `\n## 📋 项目详情\n\n`;
    
    projects.forEach((project, index) => {
      const repo = project.repository;
      report += `### ${index + 1}. ${repo.name}\n\n`;
      report += `**描述**: ${repo.description || '无描述'}\n`;
      report += `**语言**: ${repo.language || '未知'}\n`;
      report += `**Stars**: ${repo.stargazers_count} | **Forks**: ${repo.forks_count} | **Issues**: ${repo.open_issues_count}\n`;
      report += `**最后更新**: ${new Date(repo.updated_at).toLocaleDateString()}\n`;
      report += `**复杂度**: ${project.estimatedComplexity}\n`;
      report += `**推荐平台**: ${project.suggestedPlatform}\n`;
      report += `**仓库地址**: [${repo.full_name}](${repo.html_url})\n\n`;
      
      if (repo.topics.length > 0) {
        report += `**标签**: ${repo.topics.join(', ')}\n`;
      }
      
      if (project.issues.length > 0) {
        report += `**开放 Issues**: ${project.issues.length} 个\n`;
      }
      
      if (project.pullRequests.length > 0) {
        report += `**开放 PR**: ${project.pullRequests.length} 个\n`;
      }
      
      report += `\n---\n\n`;
    });

    // 建议
    report += `## 💡 管理建议\n\n`;
    report += `### 高优先级项目\n`;
    const highPriorityProjects = projects
      .filter(p => p.repository.stargazers_count > 10 || p.repository.open_issues_count > 5)
      .slice(0, 5);
    
    highPriorityProjects.forEach(project => {
      report += `- **${project.repository.name}**: ${project.repository.stargazers_count} stars, ${project.repository.open_issues_count} issues\n`;
    });

    report += `\n### 需要关注的项目\n`;
    const needsAttention = projects.filter(p => 
      p.repository.open_issues_count > 10 || 
      new Date(p.repository.pushed_at) < new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
    );
    
    needsAttention.forEach(project => {
      const daysSinceUpdate = Math.floor((Date.now() - new Date(project.repository.pushed_at).getTime()) / (1000 * 60 * 60 * 24));
      report += `- **${project.repository.name}**: ${daysSinceUpdate} 天未更新\n`;
    });

    return report;
  }

  // ==================== 私有方法 ====================

  private static async getUserRepositories(config: ProjectSyncConfig): Promise<GitHubRepository[]> {
    const headers = {
      'Authorization': `token ${config.githubToken}`,
      'Accept': 'application/vnd.github.v3+json'
    };

    const repos: GitHubRepository[] = [];
    let page = 1;
    const perPage = 100;

    while (true) {
      const response = await fetch(
        `${this.API_BASE}/user/repos?page=${page}&per_page=${perPage}&sort=updated`,
        { headers }
      );

      if (!response.ok) {
        throw new Error(`GitHub API 错误: ${response.status}`);
      }

      const pageRepos = await response.json();
      if (pageRepos.length === 0) break;

      repos.push(...pageRepos);
      page++;
    }

    return repos;
  }

  private static async getOrgRepositories(orgName: string, config: ProjectSyncConfig): Promise<GitHubRepository[]> {
    const headers = {
      'Authorization': `token ${config.githubToken}`,
      'Accept': 'application/vnd.github.v3+json'
    };

    const response = await fetch(`${this.API_BASE}/orgs/${orgName}/repos`, { headers });
    
    if (!response.ok) {
      throw new Error(`获取组织仓库失败: ${response.status}`);
    }

    return await response.json();
  }

  private static filterRepositories(repositories: GitHubRepository[], config: ProjectSyncConfig): GitHubRepository[] {
    return repositories.filter(repo => {
      // 过滤私有仓库
      if (repo.private && !config.includePrivateRepos) return false;
      
      // 过滤归档仓库
      if (config.excludeArchived && (repo as any).archived) return false;
      
      // 过滤 Fork 仓库
      if (config.excludeForked && (repo as any).fork) return false;
      
      // 按主题过滤
      if (config.filterByTopics.length > 0) {
        const hasMatchingTopic = config.filterByTopics.some(topic => 
          repo.topics.includes(topic)
        );
        if (!hasMatchingTopic) return false;
      }
      
      // 按语言过滤
      if (config.filterByLanguage.length > 0) {
        if (!repo.language || !config.filterByLanguage.includes(repo.language)) {
          return false;
        }
      }
      
      return true;
    });
  }

  private static async analyzeRepository(repo: GitHubRepository, config: ProjectSyncConfig): Promise<DiscoveredProject> {
    const headers = {
      'Authorization': `token ${config.githubToken}`,
      'Accept': 'application/vnd.github.v3+json'
    };

    // 获取 Issues
    let issues: GitHubIssue[] = [];
    if (config.syncIssues) {
      const issuesResponse = await fetch(`${this.API_BASE}/repos/${repo.full_name}/issues?state=open`, { headers });
      if (issuesResponse.ok) {
        issues = await issuesResponse.json();
      }
    }

    // 获取 Pull Requests
    let pullRequests: GitHubPullRequest[] = [];
    if (config.syncPullRequests) {
      const prResponse = await fetch(`${this.API_BASE}/repos/${repo.full_name}/pulls?state=open`, { headers });
      if (prResponse.ok) {
        pullRequests = await prResponse.json();
      }
    }

    // 分析项目结构
    const projectStructure = await this.analyzeProjectStructure(repo, headers);
    
    // 估算复杂度
    const estimatedComplexity = this.estimateComplexity(repo, issues, projectStructure);
    
    // 推荐平台
    const suggestedPlatform = this.suggestPlatform(repo, issues, projectStructure);

    return {
      source: 'github',
      repository: repo,
      issues,
      pullRequests,
      milestones: [], // 可以后续添加
      projectStructure,
      estimatedComplexity,
      suggestedPlatform
    };
  }

  private static async analyzeProjectStructure(repo: GitHubRepository, headers: any): Promise<ProjectStructure> {
    try {
      const response = await fetch(`${this.API_BASE}/repos/${repo.full_name}/contents`, { headers });
      if (!response.ok) {
        return this.getDefaultProjectStructure();
      }

      const contents = await response.json();
      const files = contents.filter((item: any) => item.type === 'file');
      const directories = contents.filter((item: any) => item.type === 'dir').map((item: any) => item.name);

      const fileTypes: Record<string, number> = {};
      files.forEach((file: any) => {
        const ext = file.name.split('.').pop()?.toLowerCase();
        if (ext) {
          fileTypes[ext] = (fileTypes[ext] || 0) + 1;
        }
      });

      return {
        hasReadme: files.some((file: any) => file.name.toLowerCase().includes('readme')),
        hasDocumentation: directories.includes('docs') || directories.includes('documentation'),
        hasTests: directories.includes('test') || directories.includes('tests') || files.some((file: any) => file.name.includes('test')),
        hasCICD: files.some((file: any) => file.name.includes('.yml') || file.name.includes('.yaml')) || directories.includes('.github'),
        packageManager: this.detectPackageManager(files),
        framework: this.detectFramework(files, fileTypes),
        directories,
        fileTypes
      };
    } catch (error) {
      return this.getDefaultProjectStructure();
    }
  }

  private static getDefaultProjectStructure(): ProjectStructure {
    return {
      hasReadme: false,
      hasDocumentation: false,
      hasTests: false,
      hasCICD: false,
      packageManager: null,
      framework: null,
      directories: [],
      fileTypes: {}
    };
  }

  private static detectPackageManager(files: any[]): string | null {
    if (files.some(f => f.name === 'package.json')) return 'npm';
    if (files.some(f => f.name === 'yarn.lock')) return 'yarn';
    if (files.some(f => f.name === 'pnpm-lock.yaml')) return 'pnpm';
    if (files.some(f => f.name === 'Cargo.toml')) return 'cargo';
    if (files.some(f => f.name === 'requirements.txt')) return 'pip';
    if (files.some(f => f.name === 'Gemfile')) return 'bundler';
    return null;
  }

  private static detectFramework(files: any[], fileTypes: Record<string, number>): string | null {
    const fileNames = files.map(f => f.name.toLowerCase());
    
    if (fileNames.includes('next.config.js')) return 'Next.js';
    if (fileNames.includes('nuxt.config.js')) return 'Nuxt.js';
    if (fileNames.includes('vue.config.js')) return 'Vue.js';
    if (fileNames.includes('angular.json')) return 'Angular';
    if (fileNames.includes('svelte.config.js')) return 'Svelte';
    if (fileTypes['tsx'] || fileTypes['jsx']) return 'React';
    if (fileTypes['vue']) return 'Vue.js';
    if (fileTypes['py']) return 'Python';
    if (fileTypes['rs']) return 'Rust';
    if (fileTypes['go']) return 'Go';
    
    return null;
  }

  private static estimateComplexity(repo: GitHubRepository, issues: GitHubIssue[], structure: ProjectStructure): 'low' | 'medium' | 'high' {
    let score = 0;
    
    // 基于仓库大小
    if (repo.size > 10000) score += 3;
    else if (repo.size > 1000) score += 2;
    else score += 1;
    
    // 基于 Issues 数量
    if (issues.length > 50) score += 3;
    else if (issues.length > 10) score += 2;
    else score += 1;
    
    // 基于项目结构
    if (structure.hasTests) score += 1;
    if (structure.hasCICD) score += 1;
    if (structure.hasDocumentation) score += 1;
    if (structure.directories.length > 10) score += 2;
    
    if (score >= 8) return 'high';
    if (score >= 5) return 'medium';
    return 'low';
  }

  private static suggestPlatform(repo: GitHubRepository, issues: GitHubIssue[], structure: ProjectStructure): 'notion' | 'airtable' | 'both' {
    // 基于项目特征推荐平台
    if (structure.hasDocumentation || (repo.description && repo.description.length > 100)) {
      return 'notion'; // 适合文档丰富的项目
    }
    
    if (issues.length > 20 || repo.open_issues_count > 20) {
      return 'airtable'; // 适合任务管理密集的项目
    }
    
    return 'both'; // 默认推荐两者皆可
  }

  private static calculateProjectStats(projects: DiscoveredProject[]) {
    const stats = {
      languages: {} as Record<string, number>,
      complexity: { low: 0, medium: 0, high: 0 },
      platforms: { notion: 0, airtable: 0, both: 0 }
    };

    projects.forEach(project => {
      // 语言统计
      const lang = project.repository.language || 'Unknown';
      stats.languages[lang] = (stats.languages[lang] || 0) + 1;
      
      // 复杂度统计
      stats.complexity[project.estimatedComplexity]++;
      
      // 平台推荐统计
      stats.platforms[project.suggestedPlatform]++;
    });

    return stats;
  }

  private static async syncToNotion(project: DiscoveredProject, config: any): Promise<void> {
    // 实现 Notion 同步逻辑
    console.log(`同步项目 ${project.repository.name} 到 Notion`);
  }

  private static async syncToAirtable(project: DiscoveredProject, config: any): Promise<void> {
    // 实现 Airtable 同步逻辑
    console.log(`同步项目 ${project.repository.name} 到 Airtable`);
  }
}
