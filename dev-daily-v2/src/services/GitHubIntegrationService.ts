/**
 * GitHub 集成服务 - 实现本地优先的 GitHub 同步
 */

export interface GitHubConfig {
  token: string;
  owner: string;
  repo: string;
  branch: string;
  basePath?: string; // 项目在仓库中的路径
}

export interface GitHubRepository {
  id: number;
  name: string;
  full_name: string;
  description: string;
  private: boolean;
  html_url: string;
  clone_url: string;
  default_branch: string;
  updated_at: string;
}

export interface GitHubCommit {
  sha: string;
  message: string;
  author: {
    name: string;
    email: string;
    date: string;
  };
  url: string;
}

export interface SyncResult {
  success: boolean;
  message: string;
  commits?: GitHubCommit[];
  filesChanged?: string[];
  error?: string;
}

export interface WebhookConfig {
  url: string;
  secret: string;
  events: string[];
  active: boolean;
}

export class GitHubIntegrationService {
  private static readonly API_BASE = 'https://api.github.com';
  private static readonly STORAGE_KEY = 'dev-daily-v2-github-configs';

  /**
   * 保存 GitHub 配置
   */
  static saveConfig(projectId: string, config: GitHubConfig): void {
    const configs = this.getAllConfigs();
    configs[projectId] = config;
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(configs));
  }

  /**
   * 获取项目的 GitHub 配置
   */
  static getConfig(projectId: string): GitHubConfig | null {
    const configs = this.getAllConfigs();
    return configs[projectId] || null;
  }

  /**
   * 获取所有配置
   */
  private static getAllConfigs(): Record<string, GitHubConfig> {
    const stored = localStorage.getItem(this.STORAGE_KEY);
    return stored ? JSON.parse(stored) : {};
  }

  /**
   * 验证 GitHub Token
   */
  static async validateToken(token: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.API_BASE}/user`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/vnd.github.v3+json'
        }
      });
      return response.ok;
    } catch (error) {
      console.error('验证 GitHub Token 失败:', error);
      return false;
    }
  }

  /**
   * 获取用户仓库列表
   */
  static async getUserRepositories(token: string): Promise<GitHubRepository[]> {
    try {
      const response = await fetch(`${this.API_BASE}/user/repos?sort=updated&per_page=100`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/vnd.github.v3+json'
        }
      });

      if (!response.ok) {
        throw new Error(`GitHub API 错误: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('获取仓库列表失败:', error);
      throw error;
    }
  }

  /**
   * 推送项目数据到 GitHub
   */
  static async pushProjectData(
    projectId: string,
    files: { path: string; content: string }[],
    commitMessage: string
  ): Promise<SyncResult> {
    const config = this.getConfig(projectId);
    if (!config) {
      return {
        success: false,
        message: '未配置 GitHub 集成',
        error: 'NO_CONFIG'
      };
    }

    try {
      // 获取当前分支的最新 commit SHA
      const latestCommit = await this.getLatestCommit(config);
      
      // 创建新的 tree
      const tree = await this.createTree(config, files, latestCommit.sha);
      
      // 创建新的 commit
      const commit = await this.createCommit(config, commitMessage, tree.sha, latestCommit.sha);
      
      // 更新分支引用
      await this.updateReference(config, commit.sha);

      return {
        success: true,
        message: '推送成功',
        commits: [commit],
        filesChanged: files.map(f => f.path)
      };
    } catch (error) {
      console.error('推送到 GitHub 失败:', error);
      return {
        success: false,
        message: '推送失败',
        error: error instanceof Error ? error.message : 'UNKNOWN_ERROR'
      };
    }
  }

  /**
   * 从 GitHub 拉取最新数据
   */
  static async pullProjectData(projectId: string): Promise<SyncResult> {
    const config = this.getConfig(projectId);
    if (!config) {
      return {
        success: false,
        message: '未配置 GitHub 集成',
        error: 'NO_CONFIG'
      };
    }

    try {
      // 获取最新的 commits
      const commits = await this.getRecentCommits(config, 10);
      
      // 获取文件内容
      const files = await this.getRepositoryFiles(config);

      return {
        success: true,
        message: '拉取成功',
        commits,
        filesChanged: files.map(f => f.path)
      };
    } catch (error) {
      console.error('从 GitHub 拉取失败:', error);
      return {
        success: false,
        message: '拉取失败',
        error: error instanceof Error ? error.message : 'UNKNOWN_ERROR'
      };
    }
  }

  /**
   * 配置 Webhook
   */
  static async setupWebhook(projectId: string, webhookConfig: WebhookConfig): Promise<boolean> {
    const config = this.getConfig(projectId);
    if (!config) return false;

    try {
      const response = await fetch(`${this.API_BASE}/repos/${config.owner}/${config.repo}/hooks`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${config.token}`,
          'Accept': 'application/vnd.github.v3+json',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: 'web',
          active: webhookConfig.active,
          events: webhookConfig.events,
          config: {
            url: webhookConfig.url,
            content_type: 'json',
            secret: webhookConfig.secret
          }
        })
      });

      return response.ok;
    } catch (error) {
      console.error('配置 Webhook 失败:', error);
      return false;
    }
  }

  // ==================== 私有方法 ====================

  private static async getLatestCommit(config: GitHubConfig): Promise<GitHubCommit> {
    const response = await fetch(
      `${this.API_BASE}/repos/${config.owner}/${config.repo}/commits/${config.branch}`,
      {
        headers: {
          'Authorization': `Bearer ${config.token}`,
          'Accept': 'application/vnd.github.v3+json'
        }
      }
    );

    if (!response.ok) {
      throw new Error(`获取最新 commit 失败: ${response.status}`);
    }

    const data = await response.json();
    return {
      sha: data.sha,
      message: data.commit.message,
      author: {
        name: data.commit.author.name,
        email: data.commit.author.email,
        date: data.commit.author.date
      },
      url: data.html_url
    };
  }

  private static async createTree(
    config: GitHubConfig,
    files: { path: string; content: string }[],
    baseTreeSha: string
  ): Promise<{ sha: string }> {
    const tree = files.map(file => ({
      path: config.basePath ? `${config.basePath}/${file.path}` : file.path,
      mode: '100644',
      type: 'blob',
      content: file.content
    }));

    const response = await fetch(`${this.API_BASE}/repos/${config.owner}/${config.repo}/git/trees`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.token}`,
        'Accept': 'application/vnd.github.v3+json',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        base_tree: baseTreeSha,
        tree
      })
    });

    if (!response.ok) {
      throw new Error(`创建 tree 失败: ${response.status}`);
    }

    return await response.json();
  }

  private static async createCommit(
    config: GitHubConfig,
    message: string,
    treeSha: string,
    parentSha: string
  ): Promise<GitHubCommit> {
    const response = await fetch(`${this.API_BASE}/repos/${config.owner}/${config.repo}/git/commits`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.token}`,
        'Accept': 'application/vnd.github.v3+json',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        message,
        tree: treeSha,
        parents: [parentSha]
      })
    });

    if (!response.ok) {
      throw new Error(`创建 commit 失败: ${response.status}`);
    }

    const data = await response.json();
    return {
      sha: data.sha,
      message: data.message,
      author: {
        name: data.author.name,
        email: data.author.email,
        date: data.author.date
      },
      url: data.html_url
    };
  }

  private static async updateReference(config: GitHubConfig, commitSha: string): Promise<void> {
    const response = await fetch(
      `${this.API_BASE}/repos/${config.owner}/${config.repo}/git/refs/heads/${config.branch}`,
      {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${config.token}`,
          'Accept': 'application/vnd.github.v3+json',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sha: commitSha
        })
      }
    );

    if (!response.ok) {
      throw new Error(`更新分支引用失败: ${response.status}`);
    }
  }

  private static async getRecentCommits(config: GitHubConfig, count: number): Promise<GitHubCommit[]> {
    const response = await fetch(
      `${this.API_BASE}/repos/${config.owner}/${config.repo}/commits?sha=${config.branch}&per_page=${count}`,
      {
        headers: {
          'Authorization': `Bearer ${config.token}`,
          'Accept': 'application/vnd.github.v3+json'
        }
      }
    );

    if (!response.ok) {
      throw new Error(`获取 commits 失败: ${response.status}`);
    }

    const data = await response.json();
    return data.map((commit: any) => ({
      sha: commit.sha,
      message: commit.commit.message,
      author: {
        name: commit.commit.author.name,
        email: commit.commit.author.email,
        date: commit.commit.author.date
      },
      url: commit.html_url
    }));
  }

  private static async getRepositoryFiles(config: GitHubConfig): Promise<{ path: string; content: string }[]> {
    // 简化实现，实际需要递归获取所有文件
    const response = await fetch(
      `${this.API_BASE}/repos/${config.owner}/${config.repo}/contents/${config.basePath || ''}?ref=${config.branch}`,
      {
        headers: {
          'Authorization': `Bearer ${config.token}`,
          'Accept': 'application/vnd.github.v3+json'
        }
      }
    );

    if (!response.ok) {
      throw new Error(`获取文件列表失败: ${response.status}`);
    }

    const data = await response.json();
    const files: { path: string; content: string }[] = [];

    // 这里需要递归处理目录和文件
    // 简化实现只处理直接文件
    for (const item of data) {
      if (item.type === 'file' && item.download_url) {
        try {
          const fileResponse = await fetch(item.download_url);
          const content = await fileResponse.text();
          files.push({
            path: item.path,
            content
          });
        } catch (error) {
          console.warn(`获取文件内容失败: ${item.path}`, error);
        }
      }
    }

    return files;
  }
}
