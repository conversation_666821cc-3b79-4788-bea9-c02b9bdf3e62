/**
 * GitHub 账号管理服务 - 支持多账号绑定和管理
 */

export interface GitHubAccount {
  id: string;
  login: string;
  name: string;
  email: string;
  avatar_url: string;
  type: 'User' | 'Organization';
  token: string;
  tokenName: string; // Token 的自定义名称
  permissions: string[]; // Token 权限范围
  isActive: boolean;
  addedAt: string;
  lastUsed?: string;
  organizations?: GitHubOrganization[];
}

export interface GitHubOrganization {
  id: number;
  login: string;
  name: string;
  description: string | null;
  avatar_url: string;
  public_repos: number;
  total_private_repos: number;
}

export interface AccountStats {
  totalRepos: number;
  publicRepos: number;
  privateRepos: number;
  totalStars: number;
  totalForks: number;
  totalIssues: number;
  languages: Record<string, number>;
  lastActivity: string;
}

export interface ProjectWithAccount {
  accountId: string;
  accountLogin: string;
  accountName: string;
  accountAvatar: string;
  accountType: 'User' | 'Organization';
  repository: any;
  // ... 其他项目信息
}

export class GitHubAccountManager {
  private static readonly ACCOUNTS_KEY = 'dev-daily-v2-github-accounts';
  private static readonly ACTIVE_ACCOUNT_KEY = 'dev-daily-v2-active-github-account';

  /**
   * 添加 GitHub 账号
   */
  static async addAccount(token: string, tokenName: string): Promise<{
    success: boolean;
    account?: GitHubAccount;
    error?: string;
  }> {
    try {
      // 验证 Token 并获取用户信息
      const userInfo = await this.validateTokenAndGetUser(token);
      if (!userInfo.success || !userInfo.user) {
        return { success: false, error: userInfo.error };
      }

      const user = userInfo.user;
      
      // 检查账号是否已存在
      const existingAccounts = this.getAllAccounts();
      const existingAccount = existingAccounts.find(acc => acc.login === user.login);
      
      if (existingAccount) {
        return { success: false, error: '该账号已经添加过了' };
      }

      // 获取 Token 权限
      const permissions = await this.getTokenPermissions(token);
      
      // 获取用户的组织信息
      const organizations = await this.getUserOrganizations(token);

      // 创建账号对象
      const account: GitHubAccount = {
        id: `github-${user.id}`,
        login: user.login,
        name: user.name || user.login,
        email: user.email || '',
        avatar_url: user.avatar_url,
        type: user.type === 'Organization' ? 'Organization' : 'User',
        token,
        tokenName,
        permissions,
        isActive: existingAccounts.length === 0, // 第一个账号默认激活
        addedAt: new Date().toISOString(),
        organizations
      };

      // 保存账号
      const updatedAccounts = [...existingAccounts, account];
      this.saveAccounts(updatedAccounts);

      // 如果是第一个账号，设为活跃账号
      if (existingAccounts.length === 0) {
        this.setActiveAccount(account.id);
      }

      return { success: true, account };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '添加账号失败'
      };
    }
  }

  /**
   * 获取所有账号
   */
  static getAllAccounts(): GitHubAccount[] {
    return this.loadAccounts();
  }

  /**
   * 获取活跃账号
   */
  static getActiveAccount(): GitHubAccount | null {
    const activeId = localStorage.getItem(this.ACTIVE_ACCOUNT_KEY);
    if (!activeId) return null;

    const accounts = this.getAllAccounts();
    return accounts.find(acc => acc.id === activeId) || null;
  }

  /**
   * 设置活跃账号
   */
  static setActiveAccount(accountId: string): boolean {
    const accounts = this.getAllAccounts();
    const account = accounts.find(acc => acc.id === accountId);
    
    if (!account) return false;

    // 更新所有账号的活跃状态
    const updatedAccounts = accounts.map(acc => ({
      ...acc,
      isActive: acc.id === accountId,
      lastUsed: acc.id === accountId ? new Date().toISOString() : acc.lastUsed
    }));

    this.saveAccounts(updatedAccounts);
    localStorage.setItem(this.ACTIVE_ACCOUNT_KEY, accountId);
    
    return true;
  }

  /**
   * 删除账号
   */
  static removeAccount(accountId: string): boolean {
    const accounts = this.getAllAccounts();
    const accountIndex = accounts.findIndex(acc => acc.id === accountId);
    
    if (accountIndex === -1) return false;

    const isActiveAccount = accounts[accountIndex].isActive;
    accounts.splice(accountIndex, 1);
    
    // 如果删除的是活跃账号，需要重新设置活跃账号
    if (isActiveAccount && accounts.length > 0) {
      accounts[0].isActive = true;
      localStorage.setItem(this.ACTIVE_ACCOUNT_KEY, accounts[0].id);
    } else if (accounts.length === 0) {
      localStorage.removeItem(this.ACTIVE_ACCOUNT_KEY);
    }

    this.saveAccounts(accounts);
    return true;
  }

  /**
   * 更新账号信息
   */
  static async updateAccount(accountId: string): Promise<{
    success: boolean;
    account?: GitHubAccount;
    error?: string;
  }> {
    try {
      const accounts = this.getAllAccounts();
      const accountIndex = accounts.findIndex(acc => acc.id === accountId);
      
      if (accountIndex === -1) {
        return { success: false, error: '账号不存在' };
      }

      const account = accounts[accountIndex];
      
      // 重新获取用户信息
      const userInfo = await this.validateTokenAndGetUser(account.token);
      if (!userInfo.success || !userInfo.user) {
        return { success: false, error: userInfo.error };
      }

      const user = userInfo.user;
      
      // 更新账号信息
      const updatedAccount: GitHubAccount = {
        ...account,
        name: user.name || user.login,
        email: user.email || account.email,
        avatar_url: user.avatar_url,
        organizations: await this.getUserOrganizations(account.token),
        lastUsed: new Date().toISOString()
      };

      accounts[accountIndex] = updatedAccount;
      this.saveAccounts(accounts);

      return { success: true, account: updatedAccount };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新账号失败'
      };
    }
  }

  /**
   * 获取账号统计信息
   */
  static async getAccountStats(accountId: string): Promise<AccountStats | null> {
    const account = this.getAllAccounts().find(acc => acc.id === accountId);
    if (!account) return null;

    try {
      const headers = {
        'Authorization': `token ${account.token}`,
        'Accept': 'application/vnd.github.v3+json'
      };

      // 获取用户仓库
      const reposResponse = await fetch('https://api.github.com/user/repos?per_page=100', { headers });
      if (!reposResponse.ok) return null;

      const repos = await reposResponse.json();
      
      // 计算统计信息
      const stats: AccountStats = {
        totalRepos: repos.length,
        publicRepos: repos.filter((r: any) => !r.private).length,
        privateRepos: repos.filter((r: any) => r.private).length,
        totalStars: repos.reduce((sum: number, r: any) => sum + r.stargazers_count, 0),
        totalForks: repos.reduce((sum: number, r: any) => sum + r.forks_count, 0),
        totalIssues: repos.reduce((sum: number, r: any) => sum + r.open_issues_count, 0),
        languages: {},
        lastActivity: repos.length > 0 ? repos[0].pushed_at : new Date().toISOString()
      };

      // 统计编程语言
      repos.forEach((repo: any) => {
        if (repo.language) {
          stats.languages[repo.language] = (stats.languages[repo.language] || 0) + 1;
        }
      });

      return stats;
    } catch (error) {
      console.error('获取账号统计失败:', error);
      return null;
    }
  }

  /**
   * 发现指定账号的项目
   */
  static async discoverAccountProjects(
    accountId: string,
    config: any
  ): Promise<{ success: boolean; projects: ProjectWithAccount[]; errors: string[] }> {
    const account = this.getAllAccounts().find(acc => acc.id === accountId);
    if (!account) {
      return { success: false, projects: [], errors: ['账号不存在'] };
    }

    try {
      // 使用账号的 Token 发现项目
      const projectConfig = { ...config, githubToken: account.token };
      
      // 这里调用原有的项目发现逻辑
      const result = await this.discoverProjectsWithAccount(account, projectConfig);
      
      return result;
    } catch (error) {
      return {
        success: false,
        projects: [],
        errors: [error instanceof Error ? error.message : '发现项目失败']
      };
    }
  }

  /**
   * 发现所有账号的项目
   */
  static async discoverAllAccountsProjects(
    config: any
  ): Promise<{ success: boolean; projectsByAccount: Record<string, ProjectWithAccount[]>; errors: string[] }> {
    const accounts = this.getAllAccounts();
    const result = {
      success: true,
      projectsByAccount: {} as Record<string, ProjectWithAccount[]>,
      errors: [] as string[]
    };

    for (const account of accounts) {
      try {
        const accountResult = await this.discoverAccountProjects(account.id, config);
        result.projectsByAccount[account.id] = accountResult.projects;
        result.errors.push(...accountResult.errors);
      } catch (error) {
        result.errors.push(`账号 ${account.login} 发现失败: ${error}`);
      }
    }

    result.success = result.errors.length === 0;
    return result;
  }

  // ==================== 私有方法 ====================

  private static async validateTokenAndGetUser(token: string): Promise<{
    success: boolean;
    user?: any;
    error?: string;
  }> {
    try {
      const response = await fetch('https://api.github.com/user', {
        headers: {
          'Authorization': `token ${token}`,
          'Accept': 'application/vnd.github.v3+json'
        }
      });

      if (response.ok) {
        const user = await response.json();
        return { success: true, user };
      } else {
        const error = await response.json();
        return { success: false, error: error.message || 'Token 验证失败' };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '网络连接失败'
      };
    }
  }

  private static async getTokenPermissions(token: string): Promise<string[]> {
    try {
      const response = await fetch('https://api.github.com/user', {
        headers: {
          'Authorization': `token ${token}`,
          'Accept': 'application/vnd.github.v3+json'
        }
      });

      if (response.ok) {
        const scopes = response.headers.get('X-OAuth-Scopes');
        return scopes ? scopes.split(', ').map(s => s.trim()) : [];
      }
      return [];
    } catch (error) {
      return [];
    }
  }

  private static async getUserOrganizations(token: string): Promise<GitHubOrganization[]> {
    try {
      const response = await fetch('https://api.github.com/user/orgs', {
        headers: {
          'Authorization': `token ${token}`,
          'Accept': 'application/vnd.github.v3+json'
        }
      });

      if (response.ok) {
        return await response.json();
      }
      return [];
    } catch (error) {
      return [];
    }
  }

  private static async discoverProjectsWithAccount(
    account: GitHubAccount,
    config: any
  ): Promise<{ success: boolean; projects: ProjectWithAccount[]; errors: string[] }> {
    // 这里实现具体的项目发现逻辑
    // 为每个发现的项目添加账号信息
    const result = {
      success: true,
      projects: [] as ProjectWithAccount[],
      errors: [] as string[]
    };

    try {
      // 确保 token 存在
      if (!account.token) {
        throw new Error(`账号 ${account.username} 缺少有效的 Token`);
      }

      console.log(`Fetching repos for account: ${account.username}, token exists: ${!!account.token}`);

      const headers = {
        'Authorization': `token ${account.token}`,
        'Accept': 'application/vnd.github.v3+json',
        'User-Agent': 'dev-daily-v2'
      };

      // 获取用户仓库 - 处理分页
      let allRepos: any[] = [];
      let page = 1;
      let hasMore = true;

      while (hasMore) {
        const reposResponse = await fetch(`https://api.github.com/user/repos?per_page=100&page=${page}`, {
          method: 'GET',
          headers
        });

        if (!reposResponse.ok) {
          const errorText = await reposResponse.text();
          console.error('GitHub API Error:', {
            status: reposResponse.status,
            statusText: reposResponse.statusText,
            body: errorText
          });
          throw new Error(`获取仓库列表失败: ${reposResponse.status} ${reposResponse.statusText}`);
        }

        const repos = await reposResponse.json();
        console.log(`Page ${page}: fetched ${repos.length} repositories`);

        allRepos.push(...repos);

        // 如果返回的仓库数量少于100，说明没有更多页面了
        hasMore = repos.length === 100;
        page++;
      }

      console.log(`Total repositories fetched: ${allRepos.length}`);
      const repos = allRepos;
      
      // 为每个仓库添加账号信息
      result.projects = repos.map((repo: any) => ({
        accountId: account.id,
        accountLogin: account.login,
        accountName: account.name,
        accountAvatar: account.avatar_url,
        accountType: account.type,
        repository: repo
      }));

      console.log(`Mapped ${result.projects.length} projects for account ${account.login}`);

    } catch (error) {
      result.success = false;
      result.errors.push(error instanceof Error ? error.message : '发现项目失败');
    }

    return result;
  }

  private static loadAccounts(): GitHubAccount[] {
    try {
      const accountsData = localStorage.getItem(this.ACCOUNTS_KEY);
      const tokensData = localStorage.getItem(`${this.ACCOUNTS_KEY}-tokens`);

      if (!accountsData) return [];

      const accounts = JSON.parse(accountsData) as GitHubAccount[];
      const tokens = tokensData ? JSON.parse(tokensData) : {};

      // 恢复 token 信息
      const accountsWithTokens = accounts.map(acc => ({
        ...acc,
        token: tokens[acc.id] || ''
      }));

      console.log('Loaded accounts:', accountsWithTokens.map(acc => ({
        id: acc.id,
        username: acc.username,
        hasToken: !!acc.token
      })));

      return accountsWithTokens;
    } catch (error) {
      console.error('Failed to load GitHub accounts:', error);
      return [];
    }
  }

  private static saveAccounts(accounts: GitHubAccount[]): void {
    // 保存账号信息（不包含 token）
    const accountsToSave = accounts.map(acc => ({
      ...acc,
      token: '' // 不直接保存 token 到 localStorage
    }));

    localStorage.setItem(this.ACCOUNTS_KEY, JSON.stringify(accountsToSave));

    // Token 单独存储
    const tokens = accounts.reduce((acc, account) => {
      acc[account.id] = account.token;
      return acc;
    }, {} as Record<string, string>);

    localStorage.setItem(`${this.ACCOUNTS_KEY}-tokens`, JSON.stringify(tokens));
  }

  /**
   * 获取账号的 Token（内部使用）
   */
  static getAccountToken(accountId: string): string | null {
    try {
      const tokens = localStorage.getItem(`${this.ACCOUNTS_KEY}-tokens`);
      if (!tokens) return null;
      
      const tokenMap = JSON.parse(tokens);
      return tokenMap[accountId] || null;
    } catch (error) {
      return null;
    }
  }
}
