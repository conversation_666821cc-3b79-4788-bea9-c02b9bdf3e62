/**
 * 多项目管理服务
 * 负责工作空间、项目、用户的统一管理
 */

import { 
  Workspace, 
  Project, 
  UserProfile, 
  ProjectTemplate,
  GlobalStats,
  CrossProjectTask,
  ExportData,
  ImportOptions
} from '../types/MultiProject';

export class MultiProjectService {
  private static readonly WORKSPACE_KEY = 'dev-daily-v2-workspaces';
  private static readonly USER_PROFILE_KEY = 'dev-daily-v2-user-profile';
  private static readonly CURRENT_WORKSPACE_KEY = 'dev-daily-v2-current-workspace';
  private static readonly CURRENT_PROJECT_KEY = 'dev-daily-v2-current-project';

  // ==================== 工作空间管理 ====================

  /**
   * 获取所有工作空间
   */
  static getAllWorkspaces(): Workspace[] {
    const stored = localStorage.getItem(this.WORKSPACE_KEY);
    return stored ? JSON.parse(stored) : [];
  }

  /**
   * 获取当前工作空间
   */
  static getCurrentWorkspace(): Workspace | null {
    const currentId = localStorage.getItem(this.CURRENT_WORKSPACE_KEY);
    if (!currentId) return null;
    
    const workspaces = this.getAllWorkspaces();
    return workspaces.find(w => w.id === currentId) || null;
  }

  /**
   * 创建工作空间
   */
  static createWorkspace(name: string, description: string): Workspace {
    const workspace: Workspace = {
      id: this.generateId(),
      name,
      description,
      projects: [],
      members: [],
      settings: {
        defaultProjectTemplate: 'default',
        autoBackup: true,
        backupRetention: 30,
        timezone: 'Asia/Shanghai',
        language: 'zh-CN',
        theme: 'light'
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const workspaces = this.getAllWorkspaces();
    workspaces.push(workspace);
    localStorage.setItem(this.WORKSPACE_KEY, JSON.stringify(workspaces));

    // 如果是第一个工作空间，设为当前工作空间
    if (workspaces.length === 1) {
      this.setCurrentWorkspace(workspace.id);
    }

    return workspace;
  }

  /**
   * 切换工作空间
   */
  static setCurrentWorkspace(workspaceId: string): void {
    localStorage.setItem(this.CURRENT_WORKSPACE_KEY, workspaceId);
    // 清除当前项目选择
    localStorage.removeItem(this.CURRENT_PROJECT_KEY);
  }

  // ==================== 项目管理 ====================

  /**
   * 获取当前工作空间的所有项目
   */
  static getAllProjects(): Project[] {
    const workspace = this.getCurrentWorkspace();
    return workspace ? workspace.projects : [];
  }

  /**
   * 获取当前项目
   */
  static getCurrentProject(): Project | null {
    const currentId = localStorage.getItem(this.CURRENT_PROJECT_KEY);
    if (!currentId) return null;
    
    const projects = this.getAllProjects();
    return projects.find(p => p.id === currentId) || null;
  }

  /**
   * 获取项目
   */
  static getProject(projectId: string): Project | null {
    const workspaces = this.getAllWorkspaces();
    for (const workspace of workspaces) {
      const project = workspace.projects.find(p => p.id === projectId);
      if (project) return project;
    }
    return null;
  }

  /**
   * 创建项目
   */
  static createProject(projectData: Partial<Project>): Project {
    const workspace = this.getCurrentWorkspace();
    if (!workspace) {
      throw new Error('请先创建或选择工作空间');
    }

    const project: Project = {
      id: this.generateId(),
      name: projectData.name || '新项目',
      description: projectData.description || '',
      workspaceId: workspace.id,
      status: 'planning',
      priority: 'medium',
      progress: 0,
      startDate: new Date().toISOString(),
      tags: [],
      color: this.getRandomColor(),
      
      // 初始化项目数据
      projectTree: null,
      roadmap: null,
      calendar: null,
      backups: [],
      logs: [],
      
      members: [],
      settings: {
        autoBackup: true,
        backupOnNodeComplete: true,
        gitIntegration: false,
        fileWatching: false,
        notifications: {
          email: false,
          browser: true,
          milestones: true,
          deadlines: true,
          mentions: true
        },
        customFields: []
      },
      
      stats: {
        totalTasks: 0,
        completedTasks: 0,
        totalHours: 0,
        actualHours: 0,
        issuesCount: 0,
        milestonesCount: 0,
        lastActivity: new Date().toISOString(),
        teamSize: 1
      },
      
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      
      ...projectData
    };

    // 更新工作空间
    workspace.projects.push(project);
    workspace.updatedAt = new Date().toISOString();
    this.updateWorkspace(workspace);

    // 如果是第一个项目，设为当前项目
    if (workspace.projects.length === 1) {
      this.setCurrentProject(project.id);
    }

    return project;
  }

  /**
   * 切换项目
   */
  static setCurrentProject(projectId: string): void {
    localStorage.setItem(this.CURRENT_PROJECT_KEY, projectId);
  }

  /**
   * 更新项目
   */
  static updateProject(projectId: string, updates: Partial<Project>): void {
    const workspace = this.getCurrentWorkspace();
    if (!workspace) return;

    const projectIndex = workspace.projects.findIndex(p => p.id === projectId);
    if (projectIndex === -1) return;

    workspace.projects[projectIndex] = {
      ...workspace.projects[projectIndex],
      ...updates,
      updatedAt: new Date().toISOString()
    };

    workspace.updatedAt = new Date().toISOString();
    this.updateWorkspace(workspace);
  }

  /**
   * 删除项目
   */
  static deleteProject(projectId: string): void {
    const workspace = this.getCurrentWorkspace();
    if (!workspace) return;

    workspace.projects = workspace.projects.filter(p => p.id !== projectId);
    workspace.updatedAt = new Date().toISOString();
    this.updateWorkspace(workspace);

    // 如果删除的是当前项目，清除选择
    const currentProjectId = localStorage.getItem(this.CURRENT_PROJECT_KEY);
    if (currentProjectId === projectId) {
      localStorage.removeItem(this.CURRENT_PROJECT_KEY);
    }
  }

  // ==================== 数据迁移和兼容性 ====================

  /**
   * 从单项目数据迁移到多项目
   */
  static migrateFromSingleProject(): void {
    // 检查是否已经是多项目结构
    const workspaces = this.getAllWorkspaces();
    if (workspaces.length > 0) return;

    // 创建默认工作空间
    const defaultWorkspace = this.createWorkspace(
      'dev-daily v2 项目',
      '从单项目模式迁移的默认工作空间'
    );

    // 迁移现有项目数据
    const legacyProjectTree = localStorage.getItem('dev-daily-v2-project-tree');
    const legacyBackups = localStorage.getItem('dev-daily-v2-backups');
    const legacyLogs = localStorage.getItem('dev-daily-v2-project-logs');
    const legacyCalendar = localStorage.getItem('dev-daily-v2-daily-progress');

    if (legacyProjectTree || legacyBackups || legacyLogs || legacyCalendar) {
      const migratedProject = this.createProject({
        name: 'dev-daily v2',
        description: '从单项目模式迁移的项目',
        status: 'active',
        priority: 'high',
        projectTree: legacyProjectTree ? JSON.parse(legacyProjectTree) : null,
        backups: legacyBackups ? JSON.parse(legacyBackups) : [],
        logs: legacyLogs ? JSON.parse(legacyLogs) : [],
        calendar: legacyCalendar ? JSON.parse(legacyCalendar) : null
      });

      console.log('已成功迁移单项目数据到多项目结构');
    }
  }

  /**
   * 初始化多项目系统
   */
  static initialize(): void {
    // 检查并迁移单项目数据
    this.migrateFromSingleProject();

    // 确保有默认工作空间
    const workspaces = this.getAllWorkspaces();
    if (workspaces.length === 0) {
      this.createWorkspace('我的工作空间', '默认工作空间');
    }

    // 确保有当前工作空间
    if (!this.getCurrentWorkspace()) {
      const firstWorkspace = workspaces[0];
      if (firstWorkspace) {
        this.setCurrentWorkspace(firstWorkspace.id);
      }
    }
  }

  // ==================== 工具方法 ====================

  private static updateWorkspace(workspace: Workspace): void {
    const workspaces = this.getAllWorkspaces();
    const index = workspaces.findIndex(w => w.id === workspace.id);
    if (index !== -1) {
      workspaces[index] = workspace;
      localStorage.setItem(this.WORKSPACE_KEY, JSON.stringify(workspaces));
    }
  }

  private static generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private static getRandomColor(): string {
    const colors = [
      '#3B82F6', '#10B981', '#F59E0B', '#EF4444', 
      '#8B5CF6', '#06B6D4', '#84CC16', '#F97316'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  /**
   * 获取全局统计信息
   */
  static getGlobalStats(): GlobalStats {
    const workspaces = this.getAllWorkspaces();
    const allProjects = workspaces.flatMap(w => w.projects);
    
    return {
      totalProjects: allProjects.length,
      activeProjects: allProjects.filter(p => p.status === 'active').length,
      completedProjects: allProjects.filter(p => p.status === 'completed').length,
      totalTasks: allProjects.reduce((sum, p) => sum + p.stats.totalTasks, 0),
      completedTasks: allProjects.reduce((sum, p) => sum + p.stats.completedTasks, 0),
      totalHours: allProjects.reduce((sum, p) => sum + p.stats.totalHours, 0),
      teamMembers: new Set(workspaces.flatMap(w => w.members.map(m => m.userId))).size,
      recentActivity: [] // TODO: 实现活动记录
    };
  }

  /**
   * 导出数据
   */
  static exportData(): ExportData {
    const workspace = this.getCurrentWorkspace();
    const userProfile = this.getUserProfile();
    
    if (!workspace) {
      throw new Error('没有可导出的工作空间');
    }

    return {
      workspace,
      projects: workspace.projects,
      userProfile: userProfile || {} as UserProfile,
      exportDate: new Date().toISOString(),
      version: '2.0.0'
    };
  }

  /**
   * 获取用户配置
   */
  static getUserProfile(): UserProfile | null {
    const stored = localStorage.getItem(this.USER_PROFILE_KEY);
    return stored ? JSON.parse(stored) : null;
  }
}
