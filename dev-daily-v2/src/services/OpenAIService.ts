// OpenAI API服务
import { Document, ProjectTreeNode } from '../types/index';

// OpenAI API配置
interface OpenAIConfig {
  apiKey: string;
  model: string;
  temperature: number;
  maxTokens: number;
}

// AI分析结果
export interface AIAnalysis {
  id: string;
  type: 'insight' | 'suggestion' | 'warning' | 'opportunity';
  title: string;
  description: string;
  confidence: number;
  actionable: boolean;
  relatedDocuments: string[];
}

// 聊天消息
export interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

// OpenAI API响应类型
interface OpenAIResponse {
  choices: {
    message: {
      content: string;
    };
  }[];
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class OpenAIService {
  private config: OpenAIConfig;
  private baseURL = 'https://api.openai.com/v1';

  constructor(config: OpenAIConfig) {
    this.config = config;
  }

  // 更新配置
  updateConfig(config: Partial<OpenAIConfig>) {
    this.config = { ...this.config, ...config };
  }

  // 测试API连接
  async testConnection(): Promise<{ success: boolean; message: string }> {
    try {
      const response = await this.makeRequest('/models', 'GET');
      if (response.ok) {
        return { success: true, message: 'API连接成功' };
      } else {
        return { success: false, message: 'API连接失败，请检查密钥' };
      }
    } catch (error) {
      return { success: false, message: `连接错误: ${error}` };
    }
  }

  // AI对话
  async chat(messages: ChatMessage[]): Promise<string> {
    try {
      const openAIMessages = messages.map(msg => ({
        role: msg.type === 'user' ? 'user' : 'assistant',
        content: msg.content
      }));

      // 添加系统提示
      const systemMessage = {
        role: 'system',
        content: `你是dev-daily项目管理工具的AI助手。你的任务是帮助用户管理项目、分析文档、提供建议。
        请用中文回答，保持专业、友好的语调。回答要简洁明了，重点突出。`
      };

      const response = await this.makeRequest('/chat/completions', 'POST', {
        model: this.config.model,
        messages: [systemMessage, ...openAIMessages],
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
      }

      const data: OpenAIResponse = await response.json();
      return data.choices[0]?.message?.content || '抱歉，我无法生成回复。';
    } catch (error) {
      console.error('Chat API error:', error);
      return `抱歉，AI服务暂时不可用。错误信息: ${error}`;
    }
  }

  // 分析文档
  async analyzeDocument(document: Document): Promise<AIAnalysis[]> {
    try {
      const prompt = `请分析以下项目文档，提供4个不同类型的分析结果：

文档标题: ${document.title}
文档类型: ${document.type}
文档内容: ${document.content}

请按以下JSON格式返回分析结果：
[
  {
    "type": "insight|suggestion|warning|opportunity",
    "title": "分析标题",
    "description": "详细描述",
    "confidence": 0.85,
    "actionable": true,
    "relatedDocuments": []
  }
]

要求：
1. 提供4个不同类型的分析（insight, suggestion, warning, opportunity各一个）
2. confidence值在0.7-0.95之间
3. description要具体、可操作
4. 用中文回答`;

      const response = await this.makeRequest('/chat/completions', 'POST', {
        model: this.config.model,
        messages: [
          {
            role: 'system',
            content: '你是一个专业的项目分析师，擅长分析项目文档并提供有价值的洞察和建议。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3, // 降低温度以获得更一致的结果
        max_tokens: 1500
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
      }

      const data: OpenAIResponse = await response.json();
      const content = data.choices[0]?.message?.content || '';

      try {
        // 尝试解析JSON响应
        const analyses = JSON.parse(content);
        return analyses.map((analysis: any, index: number) => ({
          id: `ai-analysis-${Date.now()}-${index}`,
          type: analysis.type,
          title: analysis.title,
          description: analysis.description,
          confidence: analysis.confidence,
          actionable: analysis.actionable,
          relatedDocuments: analysis.relatedDocuments || [document.filename]
        }));
      } catch (parseError) {
        // 如果JSON解析失败，返回基于内容的分析
        return this.createFallbackAnalysis(document, content);
      }
    } catch (error) {
      console.error('Document analysis error:', error);
      return this.createFallbackAnalysis(document, `分析失败: ${error}`);
    }
  }

  // 分析项目树
  async analyzeProjectTree(projectTree: ProjectTreeNode, documents: Document[]): Promise<AIAnalysis[]> {
    try {
      const treeInfo = this.extractTreeInfo(projectTree);
      const docInfo = documents.map(d => `${d.title} (${d.type})`).join(', ');

      const prompt = `请分析以下项目结构和文档，提供项目管理建议：

项目树信息:
- 总节点数: ${treeInfo.totalNodes}
- 已完成节点: ${treeInfo.completedNodes}
- 进行中节点: ${treeInfo.activeNodes}
- 阻塞节点: ${treeInfo.blockedNodes}

相关文档: ${docInfo}

请提供4个不同类型的项目分析建议，格式同上。`;

      const response = await this.makeRequest('/chat/completions', 'POST', {
        model: this.config.model,
        messages: [
          {
            role: 'system',
            content: '你是一个专业的项目管理顾问，擅长分析项目结构并提供管理建议。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.4,
        max_tokens: 1500
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
      }

      const data: OpenAIResponse = await response.json();
      const content = data.choices[0]?.message?.content || '';

      try {
        const analyses = JSON.parse(content);
        return analyses.map((analysis: any, index: number) => ({
          id: `project-analysis-${Date.now()}-${index}`,
          type: analysis.type,
          title: analysis.title,
          description: analysis.description,
          confidence: analysis.confidence,
          actionable: analysis.actionable,
          relatedDocuments: analysis.relatedDocuments || []
        }));
      } catch (parseError) {
        return this.createProjectFallbackAnalysis(treeInfo, content);
      }
    } catch (error) {
      console.error('Project analysis error:', error);
      return this.createProjectFallbackAnalysis(this.extractTreeInfo(projectTree), `分析失败: ${error}`);
    }
  }

  // 生成项目建议
  async generateSuggestions(context: {
    documents: Document[];
    projectTree: ProjectTreeNode;
    userQuery?: string;
  }): Promise<string[]> {
    try {
      const { documents, projectTree, userQuery } = context;
      const treeInfo = this.extractTreeInfo(projectTree);

      const prompt = `基于以下项目信息，生成5个具体的改进建议：

项目状态:
- 总节点: ${treeInfo.totalNodes}
- 完成率: ${Math.round((treeInfo.completedNodes / treeInfo.totalNodes) * 100)}%
- 文档数量: ${documents.length}

${userQuery ? `用户问题: ${userQuery}` : ''}

请提供5个简洁、可操作的建议，每个建议一行。`;

      const response = await this.makeRequest('/chat/completions', 'POST', {
        model: this.config.model,
        messages: [
          {
            role: 'system',
            content: '你是一个项目管理专家，提供实用的项目改进建议。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.6,
        max_tokens: 800
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status}`);
      }

      const data: OpenAIResponse = await response.json();
      const content = data.choices[0]?.message?.content || '';

      return content.split('\n').filter(line => line.trim()).slice(0, 5);
    } catch (error) {
      console.error('Suggestions generation error:', error);
      return [
        '定期更新项目文档',
        '设置明确的里程碑',
        '加强团队沟通',
        '优化工作流程',
        '建立质量检查机制'
      ];
    }
  }

  // 发送API请求
  private async makeRequest(endpoint: string, method: 'GET' | 'POST', body?: any): Promise<Response> {
    const url = `${this.baseURL}${endpoint}`;
    const headers: Record<string, string> = {
      'Authorization': `Bearer ${this.config.apiKey}`,
      'Content-Type': 'application/json'
    };

    const options: RequestInit = {
      method,
      headers
    };

    if (body && method === 'POST') {
      options.body = JSON.stringify(body);
    }

    console.log('OpenAI API Request:', {
      url,
      method,
      headers: { ...headers, 'Authorization': `Bearer ${this.config.apiKey.substring(0, 20)}...` },
      bodySize: options.body ? (typeof options.body === 'string' ? options.body.length : 'unknown') : 0
    });

    try {
      const response = await fetch(url, options);
      console.log('OpenAI API Response:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('OpenAI API Error Response:', errorText);
      }

      return response;
    } catch (error) {
      console.error('OpenAI API Network Error:', error);
      throw error;
    }
  }

  // 提取项目树信息
  private extractTreeInfo(node: ProjectTreeNode): {
    totalNodes: number;
    completedNodes: number;
    activeNodes: number;
    blockedNodes: number;
  } {
    let totalNodes = 1;
    let completedNodes = node.status === 'completed' ? 1 : 0;
    let activeNodes = node.status === 'active' ? 1 : 0;
    let blockedNodes = node.status === 'blocked' ? 1 : 0;

    if (node.children) {
      for (const child of node.children) {
        const childInfo = this.extractTreeInfo(child);
        totalNodes += childInfo.totalNodes;
        completedNodes += childInfo.completedNodes;
        activeNodes += childInfo.activeNodes;
        blockedNodes += childInfo.blockedNodes;
      }
    }

    return { totalNodes, completedNodes, activeNodes, blockedNodes };
  }

  // 创建备用分析结果
  private createFallbackAnalysis(document: Document, content: string): AIAnalysis[] {
    return [
      {
        id: `fallback-${Date.now()}-1`,
        type: 'insight',
        title: '文档分析',
        description: `基于文档"${document.title}"的分析: ${content.substring(0, 100)}...`,
        confidence: 0.75,
        actionable: true,
        relatedDocuments: [document.filename]
      }
    ];
  }

  // 创建项目备用分析结果
  private createProjectFallbackAnalysis(treeInfo: any, content: string): AIAnalysis[] {
    const completionRate = Math.round((treeInfo.completedNodes / treeInfo.totalNodes) * 100);
    
    return [
      {
        id: `project-fallback-${Date.now()}`,
        type: 'insight',
        title: '项目进度分析',
        description: `项目当前完成率为${completionRate}%，共有${treeInfo.totalNodes}个节点。${content.substring(0, 100)}`,
        confidence: 0.8,
        actionable: true,
        relatedDocuments: []
      }
    ];
  }
}
