/**
 * 工作流自动化服务 - 实现需求变更 → 文档生成 → 本地同步的自动化流程
 */

import { GitHubIntegrationService, SyncResult } from './GitHubIntegrationService';
import { ProjectDataManager } from './ProjectDataManager';
import { LocalFileSystemService } from './LocalFileSystemService';

export interface WorkflowConfig {
  projectId: string;
  autoSync: boolean;
  syncInterval: number; // minutes
  conflictResolution: 'local' | 'remote' | 'manual';
  notifications: boolean;
  webhookUrl?: string;
}

export interface WorkflowEvent {
  id: string;
  type: WorkflowEventType;
  projectId: string;
  timestamp: Date;
  data: any;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  result?: WorkflowResult;
}

export type WorkflowEventType = 
  | 'local_file_changed'
  | 'github_push_received'
  | 'requirement_generated'
  | 'sync_requested'
  | 'conflict_detected';

export interface WorkflowResult {
  success: boolean;
  message: string;
  actions: WorkflowAction[];
  errors?: string[];
}

export interface WorkflowAction {
  type: 'file_updated' | 'commit_created' | 'notification_sent' | 'conflict_resolved';
  description: string;
  timestamp: Date;
  data?: any;
}

export interface RequirementChange {
  type: 'feature_request' | 'bug_report' | 'improvement' | 'documentation';
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assignee?: string;
  labels: string[];
  source: 'team_feedback' | 'user_request' | 'automated_analysis';
}

export class WorkflowAutomationService {
  private static readonly CONFIG_KEY = 'dev-daily-v2-workflow-configs';
  private static readonly EVENTS_KEY = 'dev-daily-v2-workflow-events';
  private static readonly MAX_EVENTS = 1000;

  private static eventListeners: Map<string, ((event: WorkflowEvent) => void)[]> = new Map();
  private static isInitialized = false;

  /**
   * 初始化工作流自动化系统
   */
  static initialize(): void {
    if (this.isInitialized) return;

    // 启动定期同步检查
    this.startPeriodicSync();
    
    // 监听文件系统变化
    this.setupFileSystemWatcher();
    
    // 监听 GitHub Webhook 事件
    this.setupWebhookListener();

    this.isInitialized = true;
    console.log('工作流自动化系统已初始化');
  }

  /**
   * 配置项目工作流
   */
  static configureWorkflow(config: WorkflowConfig): void {
    const configs = this.getAllConfigs();
    configs[config.projectId] = config;
    localStorage.setItem(this.CONFIG_KEY, JSON.stringify(configs));
    
    this.emitEvent({
      id: this.generateEventId(),
      type: 'sync_requested',
      projectId: config.projectId,
      timestamp: new Date(),
      data: { action: 'config_updated', config },
      status: 'completed'
    });
  }

  /**
   * 处理本地文件变化
   */
  static async handleLocalFileChange(
    projectId: string,
    changes: { path: string; type: 'added' | 'modified' | 'deleted'; content?: string }[]
  ): Promise<WorkflowResult> {
    const config = this.getConfig(projectId);
    if (!config || !config.autoSync) {
      return {
        success: false,
        message: '自动同步未启用',
        actions: []
      };
    }

    const event: WorkflowEvent = {
      id: this.generateEventId(),
      type: 'local_file_changed',
      projectId,
      timestamp: new Date(),
      data: { changes },
      status: 'processing'
    };

    this.emitEvent(event);

    try {
      const actions: WorkflowAction[] = [];

      // 1. 更新项目数据
      const projectData = ProjectDataManager.getProjectData(projectId);
      if (projectData) {
        // 更新文档列表
        for (const change of changes) {
          if (change.type === 'added' || change.type === 'modified') {
            // 添加或更新文档
            actions.push({
              type: 'file_updated',
              description: `更新文件: ${change.path}`,
              timestamp: new Date(),
              data: { path: change.path, type: change.type }
            });
          }
        }
        
        ProjectDataManager.saveProjectData(projectId, projectData);
      }

      // 2. 推送到 GitHub
      if (config.autoSync) {
        const files = changes
          .filter(c => c.type !== 'deleted' && c.content)
          .map(c => ({ path: c.path, content: c.content! }));

        if (files.length > 0) {
          const syncResult = await GitHubIntegrationService.pushProjectData(
            projectId,
            files,
            `自动同步: ${changes.length} 个文件变更`
          );

          if (syncResult.success) {
            actions.push({
              type: 'commit_created',
              description: '成功推送到 GitHub',
              timestamp: new Date(),
              data: syncResult
            });
          }
        }
      }

      // 3. 发送通知
      if (config.notifications) {
        await this.sendNotification(projectId, {
          title: '文件变更已同步',
          message: `${changes.length} 个文件已自动同步到 GitHub`,
          type: 'success'
        });

        actions.push({
          type: 'notification_sent',
          description: '发送同步通知',
          timestamp: new Date()
        });
      }

      const result: WorkflowResult = {
        success: true,
        message: `成功处理 ${changes.length} 个文件变更`,
        actions
      };

      event.status = 'completed';
      event.result = result;
      this.updateEvent(event);

      return result;

    } catch (error) {
      const result: WorkflowResult = {
        success: false,
        message: '处理文件变更失败',
        actions: [],
        errors: [error instanceof Error ? error.message : '未知错误']
      };

      event.status = 'failed';
      event.result = result;
      this.updateEvent(event);

      return result;
    }
  }

  /**
   * 处理 GitHub Webhook 事件
   */
  static async handleGitHubWebhook(
    projectId: string,
    webhookData: any
  ): Promise<WorkflowResult> {
    const event: WorkflowEvent = {
      id: this.generateEventId(),
      type: 'github_push_received',
      projectId,
      timestamp: new Date(),
      data: webhookData,
      status: 'processing'
    };

    this.emitEvent(event);

    try {
      const actions: WorkflowAction[] = [];

      // 1. 拉取最新数据
      const syncResult = await GitHubIntegrationService.pullProjectData(projectId);
      
      if (syncResult.success) {
        actions.push({
          type: 'file_updated',
          description: '从 GitHub 拉取最新数据',
          timestamp: new Date(),
          data: syncResult
        });

        // 2. 检查是否有需求变更
        if (webhookData.commits) {
          const requirementChanges = await this.analyzeCommitsForRequirements(webhookData.commits);
          
          if (requirementChanges.length > 0) {
            // 3. 生成需求文档
            const requirementDoc = await this.generateRequirementDocument(projectId, requirementChanges);
            
            actions.push({
              type: 'file_updated',
              description: '生成需求变更文档',
              timestamp: new Date(),
              data: { document: requirementDoc }
            });
          }
        }

        // 4. 发送通知
        const config = this.getConfig(projectId);
        if (config?.notifications) {
          await this.sendNotification(projectId, {
            title: 'GitHub 更新已同步',
            message: `收到 ${webhookData.commits?.length || 0} 个新提交`,
            type: 'info'
          });

          actions.push({
            type: 'notification_sent',
            description: '发送 GitHub 同步通知',
            timestamp: new Date()
          });
        }
      }

      const result: WorkflowResult = {
        success: syncResult.success,
        message: syncResult.message,
        actions
      };

      event.status = 'completed';
      event.result = result;
      this.updateEvent(event);

      return result;

    } catch (error) {
      const result: WorkflowResult = {
        success: false,
        message: '处理 GitHub Webhook 失败',
        actions: [],
        errors: [error instanceof Error ? error.message : '未知错误']
      };

      event.status = 'failed';
      event.result = result;
      this.updateEvent(event);

      return result;
    }
  }

  /**
   * 生成需求变更文档
   */
  static async generateRequirementDocument(
    projectId: string,
    changes: RequirementChange[]
  ): Promise<string> {
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `requirements-${timestamp}.md`;

    let content = `# 需求变更文档\n\n`;
    content += `**生成时间**: ${new Date().toLocaleString()}\n`;
    content += `**项目ID**: ${projectId}\n\n`;

    content += `## 📋 变更概览\n\n`;
    content += `本次共收集到 ${changes.length} 个需求变更：\n\n`;

    const groupedChanges = this.groupChangesByType(changes);
    
    for (const [type, typeChanges] of Object.entries(groupedChanges)) {
      content += `### ${this.getTypeDisplayName(type)}\n\n`;
      
      for (const change of typeChanges) {
        content += `#### ${change.title}\n\n`;
        content += `**优先级**: ${change.priority}\n`;
        content += `**来源**: ${change.source}\n`;
        if (change.assignee) {
          content += `**负责人**: ${change.assignee}\n`;
        }
        if (change.labels.length > 0) {
          content += `**标签**: ${change.labels.join(', ')}\n`;
        }
        content += `\n${change.description}\n\n`;
        content += `---\n\n`;
      }
    }

    content += `## 🎯 下一步行动\n\n`;
    content += `1. 评估需求变更的影响范围\n`;
    content += `2. 更新项目计划和时间线\n`;
    content += `3. 分配开发任务\n`;
    content += `4. 更新项目文档\n\n`;

    content += `## 📊 统计信息\n\n`;
    content += `- 总计: ${changes.length} 个变更\n`;
    content += `- 高优先级: ${changes.filter(c => c.priority === 'high' || c.priority === 'urgent').length} 个\n`;
    content += `- 功能请求: ${changes.filter(c => c.type === 'feature_request').length} 个\n`;
    content += `- 问题修复: ${changes.filter(c => c.type === 'bug_report').length} 个\n`;

    return content;
  }

  // ==================== 私有方法 ====================

  private static getAllConfigs(): Record<string, WorkflowConfig> {
    const stored = localStorage.getItem(this.CONFIG_KEY);
    return stored ? JSON.parse(stored) : {};
  }

  static getConfig(projectId: string): WorkflowConfig | null {
    const configs = this.getAllConfigs();
    return configs[projectId] || null;
  }

  private static generateEventId(): string {
    return `event-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private static emitEvent(event: WorkflowEvent): void {
    // 保存事件
    this.saveEvent(event);
    
    // 触发监听器
    const listeners = this.eventListeners.get(event.type) || [];
    listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('事件监听器执行失败:', error);
      }
    });
  }

  private static saveEvent(event: WorkflowEvent): void {
    const events = this.getAllEvents();
    events.unshift(event);
    
    // 限制事件数量
    if (events.length > this.MAX_EVENTS) {
      events.splice(this.MAX_EVENTS);
    }
    
    localStorage.setItem(this.EVENTS_KEY, JSON.stringify(events));
  }

  private static updateEvent(event: WorkflowEvent): void {
    const events = this.getAllEvents();
    const index = events.findIndex(e => e.id === event.id);
    if (index !== -1) {
      events[index] = event;
      localStorage.setItem(this.EVENTS_KEY, JSON.stringify(events));
    }
  }

  private static getAllEvents(): WorkflowEvent[] {
    const stored = localStorage.getItem(this.EVENTS_KEY);
    return stored ? JSON.parse(stored) : [];
  }

  private static startPeriodicSync(): void {
    setInterval(async () => {
      const configs = this.getAllConfigs();
      
      for (const [projectId, config] of Object.entries(configs)) {
        if (config.autoSync) {
          try {
            // 检查是否需要同步
            const lastSync = this.getLastSyncTime(projectId);
            const now = Date.now();
            const syncInterval = config.syncInterval * 60 * 1000; // 转换为毫秒
            
            if (now - lastSync > syncInterval) {
              await this.performPeriodicSync(projectId);
            }
          } catch (error) {
            console.error(`定期同步失败 [${projectId}]:`, error);
          }
        }
      }
    }, 60000); // 每分钟检查一次
  }

  private static async performPeriodicSync(projectId: string): Promise<void> {
    // 实现定期同步逻辑
    console.log(`执行定期同步: ${projectId}`);
  }

  private static getLastSyncTime(projectId: string): number {
    const key = `last-sync-${projectId}`;
    const stored = localStorage.getItem(key);
    return stored ? parseInt(stored) : 0;
  }

  private static setupFileSystemWatcher(): void {
    // 文件系统监控设置
    console.log('设置文件系统监控');
  }

  private static setupWebhookListener(): void {
    // Webhook 监听器设置
    console.log('设置 Webhook 监听器');
  }

  private static async analyzeCommitsForRequirements(commits: any[]): Promise<RequirementChange[]> {
    // 分析提交信息，提取需求变更
    const changes: RequirementChange[] = [];
    
    for (const commit of commits) {
      const message = commit.message || '';
      
      // 简单的关键词检测
      if (message.includes('feat:') || message.includes('feature:')) {
        changes.push({
          type: 'feature_request',
          title: `新功能: ${message.replace(/^(feat:|feature:)\s*/, '')}`,
          description: `基于提交 ${commit.sha} 的功能请求`,
          priority: 'medium',
          labels: ['auto-generated', 'feature'],
          source: 'automated_analysis'
        });
      } else if (message.includes('fix:') || message.includes('bug:')) {
        changes.push({
          type: 'bug_report',
          title: `问题修复: ${message.replace(/^(fix:|bug:)\s*/, '')}`,
          description: `基于提交 ${commit.sha} 的问题修复`,
          priority: 'high',
          labels: ['auto-generated', 'bug'],
          source: 'automated_analysis'
        });
      }
    }
    
    return changes;
  }

  private static groupChangesByType(changes: RequirementChange[]): Record<string, RequirementChange[]> {
    return changes.reduce((groups, change) => {
      if (!groups[change.type]) {
        groups[change.type] = [];
      }
      groups[change.type].push(change);
      return groups;
    }, {} as Record<string, RequirementChange[]>);
  }

  private static getTypeDisplayName(type: string): string {
    const typeNames: Record<string, string> = {
      'feature_request': '🚀 功能请求',
      'bug_report': '🐛 问题报告',
      'improvement': '⚡ 改进建议',
      'documentation': '📚 文档更新'
    };
    return typeNames[type] || type;
  }

  private static async sendNotification(
    projectId: string,
    notification: { title: string; message: string; type: 'success' | 'info' | 'warning' | 'error' }
  ): Promise<void> {
    // 发送通知的实现
    console.log(`发送通知 [${projectId}]:`, notification);
    
    // 可以集成浏览器通知 API
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico'
      });
    }
  }

  /**
   * 获取工作流事件历史
   */
  static getWorkflowEvents(projectId?: string, limit?: number): WorkflowEvent[] {
    const events = this.getAllEvents();
    
    let filtered = events;
    if (projectId) {
      filtered = events.filter(e => e.projectId === projectId);
    }
    
    if (limit) {
      filtered = filtered.slice(0, limit);
    }
    
    return filtered;
  }

  /**
   * 添加事件监听器
   */
  static addEventListener(
    eventType: WorkflowEventType,
    listener: (event: WorkflowEvent) => void
  ): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, []);
    }
    this.eventListeners.get(eventType)!.push(listener);
  }

  /**
   * 移除事件监听器
   */
  static removeEventListener(
    eventType: WorkflowEventType,
    listener: (event: WorkflowEvent) => void
  ): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index !== -1) {
        listeners.splice(index, 1);
      }
    }
  }
}
