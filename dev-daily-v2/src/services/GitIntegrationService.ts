/**
 * Git集成服务 - 连接本地开发与项目管理
 * 
 * 功能：
 * 1. 监听Git提交，自动更新项目进度
 * 2. 分析提交信息，识别完成的任务
 * 3. 统计代码变更，评估工作量
 * 4. 生成开发报告
 */

import { ProjectLogService } from './ProjectLogService';
import { BackupService } from './BackupService';

export interface GitCommit {
  hash: string;
  message: string;
  author: string;
  date: string;
  files: string[];
  additions: number;
  deletions: number;
  nodeId?: string; // 关联的项目树节点
}

export interface GitStats {
  totalCommits: number;
  totalFiles: number;
  totalAdditions: number;
  totalDeletions: number;
  activeFiles: string[];
  commitFrequency: { [date: string]: number };
}

export class GitIntegrationService {
  private static readonly STORAGE_KEY = 'dev-daily-v2-git-commits';
  private static readonly CONFIG_KEY = 'dev-daily-v2-git-config';

  /**
   * Git配置
   */
  static getGitConfig() {
    const stored = localStorage.getItem(this.CONFIG_KEY);
    return stored ? JSON.parse(stored) : {
      autoSync: true,
      commitPattern: /^(feat|fix|docs|style|refactor|test|chore)(\([A-Z]\d+\))?: .+/,
      nodePattern: /\[([A-Z]\d+)\]/,
      autoBackupOnMilestone: true,
      trackFileChanges: true
    };
  }

  /**
   * 更新Git配置
   */
  static updateGitConfig(config: any) {
    localStorage.setItem(this.CONFIG_KEY, JSON.stringify(config));
  }

  /**
   * 模拟Git提交监听 (实际项目中需要Git hooks)
   */
  static async simulateGitCommit(commit: Omit<GitCommit, 'hash' | 'date'>) {
    const gitCommit: GitCommit = {
      ...commit,
      hash: this.generateCommitHash(),
      date: new Date().toISOString()
    };

    // 保存提交记录
    await this.saveCommit(gitCommit);

    // 分析提交信息
    await this.analyzeCommit(gitCommit);

    return gitCommit;
  }

  /**
   * 分析Git提交，自动更新项目进度
   */
  private static async analyzeCommit(commit: GitCommit) {
    const config = this.getGitConfig();
    
    // 1. 提取节点ID
    const nodeMatch = commit.message.match(config.nodePattern);
    if (nodeMatch) {
      commit.nodeId = nodeMatch[1];
    }

    // 2. 识别提交类型和完成状态
    const commitType = this.getCommitType(commit.message);
    const isFeatureComplete = this.isFeatureComplete(commit.message);
    const workHours = this.estimateWorkHours(commit);

    // 3. 自动记录项目日志
    if (commit.nodeId) {
      ProjectLogService.addLogEntry({
        date: commit.date.split('T')[0],
        projectId: 'root',
        projectName: 'dev-daily v2 项目',
        nodeId: commit.nodeId,
        nodeName: this.getNodeName(commit.nodeId),
        action: isFeatureComplete ? 'task_completed' : 'task_started',
        description: `Git提交: ${commit.message}`,
        metadata: {
          hoursWorked: workHours,
          tags: ['git', commitType],
          linkedDocuments: commit.files
        }
      });
    }

    // 4. 检查是否需要自动备份
    if (isFeatureComplete && config.autoBackupOnMilestone) {
      // 触发自动备份逻辑
      console.log(`检测到功能完成，建议创建备份: ${commit.nodeId}`);
    }

    // 5. 更新文件活跃度统计
    if (config.trackFileChanges) {
      this.updateFileActivity(commit);
    }
  }

  /**
   * 获取提交类型
   */
  private static getCommitType(message: string): string {
    const typeMatch = message.match(/^(feat|fix|docs|style|refactor|test|chore)/);
    return typeMatch ? typeMatch[1] : 'other';
  }

  /**
   * 判断是否为功能完成
   */
  private static isFeatureComplete(message: string): boolean {
    const completeKeywords = ['完成', 'complete', 'finish', 'done', 'implement'];
    return completeKeywords.some(keyword => 
      message.toLowerCase().includes(keyword)
    );
  }

  /**
   * 估算工作时长 (基于代码变更量)
   */
  private static estimateWorkHours(commit: GitCommit): number {
    const totalChanges = commit.additions + commit.deletions;
    
    // 简单的估算公式：每100行代码约1小时
    // 实际项目中可以根据历史数据优化
    const baseHours = Math.max(0.5, totalChanges / 100);
    
    // 根据文件类型调整
    const complexFiles = commit.files.filter(file => 
      file.endsWith('.tsx') || file.endsWith('.ts') || file.endsWith('.vue')
    ).length;
    
    return Math.round((baseHours + complexFiles * 0.2) * 10) / 10;
  }

  /**
   * 获取节点名称
   */
  private static getNodeName(nodeId: string): string {
    const nodeNames: { [key: string]: string } = {
      'A1': 'A1: Node.js 环境',
      'A2': 'A2: TypeScript 配置',
      'A3': 'A3: React 生态系统',
      'A4': 'A4: Vite 构建工具',
      'A5': 'A5: Cloudflare 部署',
      'B1': 'B1: Project Tree管理',
      'B2': 'B2: 文档管理系统',
      'B3': 'B3: AI功能集成',
      'B4': 'B4: 思维模型系统',
      'C1': 'C1: 主仪表板',
      'C2': 'C2: 响应式设计',
      'C3': 'C3: 思维模型界面',
      'D1': 'D1: 迁移脚本',
      'D2': 'D2: 数据验证',
      'D3': 'D3: 备份与恢复'
    };
    
    return nodeNames[nodeId] || `节点 ${nodeId}`;
  }

  /**
   * 保存提交记录
   */
  private static async saveCommit(commit: GitCommit) {
    const commits = this.getAllCommits();
    commits.unshift(commit);
    
    // 保留最近1000条提交
    if (commits.length > 1000) {
      commits.splice(1000);
    }
    
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(commits));
  }

  /**
   * 获取所有提交记录
   */
  static getAllCommits(): GitCommit[] {
    const stored = localStorage.getItem(this.STORAGE_KEY);
    return stored ? JSON.parse(stored) : [];
  }

  /**
   * 获取Git统计信息
   */
  static getGitStats(days: number = 30): GitStats {
    const commits = this.getAllCommits();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const recentCommits = commits.filter(commit => 
      new Date(commit.date) >= cutoffDate
    );

    const stats: GitStats = {
      totalCommits: recentCommits.length,
      totalFiles: 0,
      totalAdditions: 0,
      totalDeletions: 0,
      activeFiles: [],
      commitFrequency: {}
    };

    const fileActivity: { [file: string]: number } = {};

    recentCommits.forEach(commit => {
      stats.totalAdditions += commit.additions;
      stats.totalDeletions += commit.deletions;
      
      const dateKey = commit.date.split('T')[0];
      stats.commitFrequency[dateKey] = (stats.commitFrequency[dateKey] || 0) + 1;

      commit.files.forEach(file => {
        fileActivity[file] = (fileActivity[file] || 0) + 1;
      });
    });

    // 获取最活跃的文件
    stats.activeFiles = Object.entries(fileActivity)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([file]) => file);

    stats.totalFiles = Object.keys(fileActivity).length;

    return stats;
  }

  /**
   * 更新文件活跃度
   */
  private static updateFileActivity(commit: GitCommit) {
    const activityKey = 'dev-daily-v2-file-activity';
    const stored = localStorage.getItem(activityKey);
    const activity = stored ? JSON.parse(stored) : {};

    commit.files.forEach(file => {
      if (!activity[file]) {
        activity[file] = {
          commits: 0,
          lastModified: commit.date,
          totalChanges: 0
        };
      }
      
      activity[file].commits += 1;
      activity[file].lastModified = commit.date;
      activity[file].totalChanges += commit.additions + commit.deletions;
    });

    localStorage.setItem(activityKey, JSON.stringify(activity));
  }

  /**
   * 生成提交哈希
   */
  private static generateCommitHash(): string {
    return Math.random().toString(36).substr(2, 8);
  }

  /**
   * 生成开发报告
   */
  static generateDevelopmentReport(days: number = 7) {
    const stats = this.getGitStats(days);
    const commits = this.getAllCommits().slice(0, 20);

    return {
      period: `最近${days}天`,
      summary: {
        commits: stats.totalCommits,
        files: stats.totalFiles,
        additions: stats.totalAdditions,
        deletions: stats.totalDeletions,
        productivity: this.calculateProductivity(stats)
      },
      recentCommits: commits,
      activeFiles: stats.activeFiles,
      commitFrequency: stats.commitFrequency,
      recommendations: this.generateRecommendations(stats)
    };
  }

  /**
   * 计算生产力指标
   */
  private static calculateProductivity(stats: GitStats): string {
    const avgCommitsPerDay = stats.totalCommits / 30;
    
    if (avgCommitsPerDay >= 3) return '高';
    if (avgCommitsPerDay >= 1.5) return '中';
    return '低';
  }

  /**
   * 生成改进建议
   */
  private static generateRecommendations(stats: GitStats): string[] {
    const recommendations: string[] = [];
    
    if (stats.totalCommits < 10) {
      recommendations.push('建议增加提交频率，小步快跑');
    }
    
    if (stats.activeFiles.length > 20) {
      recommendations.push('文件修改较为分散，建议聚焦核心功能');
    }
    
    const avgChangesPerCommit = (stats.totalAdditions + stats.totalDeletions) / stats.totalCommits;
    if (avgChangesPerCommit > 500) {
      recommendations.push('单次提交变更较大，建议拆分为更小的提交');
    }
    
    return recommendations;
  }

  /**
   * 导出Git数据
   */
  static exportGitData() {
    const data = {
      commits: this.getAllCommits(),
      stats: this.getGitStats(90),
      config: this.getGitConfig(),
      exportDate: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `git-integration-data-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
}
