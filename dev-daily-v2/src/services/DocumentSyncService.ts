/**
 * 文档同步服务 - 专注于GitHub Markdown文档的同步和版本管理
 */

export interface GitHubDocument {
  id: string;
  projectId: string;
  filePath: string;
  title: string;
  content: string;
  contentHash: string;
  lastModified: string;
  author: string;
  commitSha?: string;
  size: number;
}

export interface DocumentVersion {
  id: string;
  documentId: string;
  version: string;
  content: string;
  contentHash: string;
  createdAt: string;
  author: string;
  commitSha?: string;
  changeType: 'created' | 'updated' | 'deleted';
  changes: {
    added: number;
    removed: number;
    modified: number;
  };
  r2Key: string;
}

export interface SyncProject {
  id: string;
  name: string;
  githubUrl: string;
  githubToken?: string;
  syncEnabled: boolean;
  lastSyncAt?: string;
  lastCommitSha?: string;
  documentCount: number;
  totalVersions: number;
}

export interface SyncResult {
  success: boolean;
  projectId: string;
  documentsProcessed: number;
  versionsCreated: number;
  errors: string[];
  syncDuration: number;
}

export class DocumentSyncService {
  private static readonly API_BASE = 'https://api.github.com';
  private static readonly R2_ENDPOINT = process.env.R2_ENDPOINT || '';
  private static readonly R2_ACCESS_KEY = process.env.R2_ACCESS_KEY || '';
  private static readonly R2_SECRET_KEY = process.env.R2_SECRET_KEY || '';
  private static readonly R2_BUCKET = 'dev-daily-documents';

  /**
   * 发现GitHub仓库中的所有Markdown文档
   */
  static async discoverDocuments(
    repoUrl: string,
    token: string,
    branch: string = 'main'
  ): Promise<GitHubDocument[]> {
    try {
      const { owner, repo } = this.parseGitHubUrl(repoUrl);
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/vnd.github.v3+json'
      };

      // 获取仓库树结构
      const treeResponse = await fetch(
        `${this.API_BASE}/repos/${owner}/${repo}/git/trees/${branch}?recursive=1`,
        { headers }
      );

      if (!treeResponse.ok) {
        throw new Error(`获取仓库树失败: ${treeResponse.status}`);
      }

      const treeData = await treeResponse.json();
      const markdownFiles = treeData.tree.filter((item: any) => 
        item.type === 'blob' && 
        (item.path.endsWith('.md') || item.path.endsWith('.markdown'))
      );

      const documents: GitHubDocument[] = [];

      // 批量获取文档内容
      for (const file of markdownFiles) {
        try {
          const content = await this.getFileContent(owner, repo, file.path, headers);
          const document: GitHubDocument = {
            id: this.generateDocumentId(repoUrl, file.path),
            projectId: this.generateProjectId(repoUrl),
            filePath: file.path,
            title: this.extractTitle(content) || this.getFileNameFromPath(file.path),
            content,
            contentHash: await this.calculateHash(content),
            lastModified: new Date().toISOString(),
            author: 'github-sync',
            size: file.size || content.length
          };
          documents.push(document);
        } catch (error) {
          console.warn(`跳过文件 ${file.path}: ${error}`);
        }
      }

      return documents;
    } catch (error) {
      console.error('发现文档失败:', error);
      throw error;
    }
  }

  /**
   * 同步项目文档
   */
  static async syncProject(
    project: SyncProject,
    options: {
      forceFullSync?: boolean;
      batchSize?: number;
    } = {}
  ): Promise<SyncResult> {
    const startTime = Date.now();
    const result: SyncResult = {
      success: false,
      projectId: project.id,
      documentsProcessed: 0,
      versionsCreated: 0,
      errors: [],
      syncDuration: 0
    };

    try {
      if (!project.githubToken) {
        throw new Error('GitHub Token 未配置');
      }

      // 1. 发现文档
      const documents = await this.discoverDocuments(
        project.githubUrl,
        project.githubToken
      );

      // 2. 获取现有文档版本
      const existingDocuments = await this.getProjectDocuments(project.id);
      const existingMap = new Map(existingDocuments.map(doc => [doc.filePath, doc]));

      // 3. 处理文档变更
      const batchSize = options.batchSize || 10;
      for (let i = 0; i < documents.length; i += batchSize) {
        const batch = documents.slice(i, i + batchSize);
        
        for (const document of batch) {
          try {
            const existing = existingMap.get(document.filePath);
            
            // 检查是否需要创建新版本
            if (!existing || existing.contentHash !== document.contentHash || options.forceFullSync) {
              await this.createDocumentVersion(document, existing);
              result.versionsCreated++;
            }
            
            result.documentsProcessed++;
          } catch (error) {
            result.errors.push(`处理文档 ${document.filePath} 失败: ${error}`);
          }
        }
      }

      // 4. 更新项目同步状态
      await this.updateProjectSyncStatus(project.id, {
        lastSyncAt: new Date().toISOString(),
        documentCount: documents.length,
        totalVersions: result.versionsCreated
      });

      result.success = result.errors.length === 0;
      result.syncDuration = Date.now() - startTime;

      return result;
    } catch (error) {
      result.errors.push(`同步失败: ${error}`);
      result.syncDuration = Date.now() - startTime;
      return result;
    }
  }

  /**
   * 创建文档版本
   */
  static async createDocumentVersion(
    document: GitHubDocument,
    previousVersion?: GitHubDocument
  ): Promise<DocumentVersion> {
    const version = this.generateVersionNumber(previousVersion);
    const versionId = `${document.id}-${version}`;
    
    // 计算变更统计
    const changes = previousVersion 
      ? await this.calculateChanges(previousVersion.content, document.content)
      : { added: document.content.split('\n').length, removed: 0, modified: 0 };

    const documentVersion: DocumentVersion = {
      id: versionId,
      documentId: document.id,
      version,
      content: document.content,
      contentHash: document.contentHash,
      createdAt: new Date().toISOString(),
      author: document.author,
      commitSha: document.commitSha,
      changeType: previousVersion ? 'updated' : 'created',
      changes,
      r2Key: `documents/${document.projectId}/${document.id}/versions/${version}.md`
    };

    // 存储到R2
    await this.storeDocumentToR2(documentVersion);

    // 更新D1元数据
    await this.updateDocumentMetadata(document, documentVersion);

    return documentVersion;
  }

  /**
   * 获取文档版本历史
   */
  static async getDocumentVersions(documentId: string): Promise<DocumentVersion[]> {
    // 这里应该从D1数据库查询
    // 目前返回模拟数据
    return [];
  }

  /**
   * 比较两个版本的差异
   */
  static async compareVersions(
    documentId: string,
    versionA: string,
    versionB: string
  ): Promise<{
    added: string[];
    removed: string[];
    modified: string[];
  }> {
    // 从R2获取两个版本的内容
    const contentA = await this.getVersionContent(documentId, versionA);
    const contentB = await this.getVersionContent(documentId, versionB);

    // 执行diff算法
    return this.performDiff(contentA, contentB);
  }

  // 私有辅助方法

  private static parseGitHubUrl(url: string): { owner: string; repo: string } {
    const match = url.match(/github\.com\/([^\/]+)\/([^\/]+)/);
    if (!match) {
      throw new Error('无效的GitHub URL');
    }
    return { owner: match[1], repo: match[2].replace('.git', '') };
  }

  private static async getFileContent(
    owner: string,
    repo: string,
    path: string,
    headers: Record<string, string>
  ): Promise<string> {
    const response = await fetch(
      `${this.API_BASE}/repos/${owner}/${repo}/contents/${path}`,
      { headers }
    );

    if (!response.ok) {
      throw new Error(`获取文件内容失败: ${response.status}`);
    }

    const data = await response.json();
    return atob(data.content.replace(/\n/g, ''));
  }

  private static extractTitle(content: string): string | null {
    const match = content.match(/^#\s+(.+)$/m);
    return match ? match[1].trim() : null;
  }

  private static getFileNameFromPath(path: string): string {
    return path.split('/').pop()?.replace(/\.(md|markdown)$/, '') || 'Untitled';
  }

  private static generateDocumentId(repoUrl: string, filePath: string): string {
    return `doc-${this.generateProjectId(repoUrl)}-${this.hashString(filePath)}`;
  }

  private static generateProjectId(repoUrl: string): string {
    return `proj-${this.hashString(repoUrl)}`;
  }

  private static generateVersionNumber(previousVersion?: GitHubDocument): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    return `v${timestamp}`;
  }

  private static async calculateHash(content: string): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(content);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  private static hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(36);
  }

  private static async calculateChanges(
    oldContent: string,
    newContent: string
  ): Promise<{ added: number; removed: number; modified: number }> {
    const oldLines = oldContent.split('\n');
    const newLines = newContent.split('\n');
    
    // 简单的行级别差异计算
    const maxLines = Math.max(oldLines.length, newLines.length);
    let added = 0, removed = 0, modified = 0;
    
    for (let i = 0; i < maxLines; i++) {
      const oldLine = oldLines[i];
      const newLine = newLines[i];
      
      if (oldLine === undefined) {
        added++;
      } else if (newLine === undefined) {
        removed++;
      } else if (oldLine !== newLine) {
        modified++;
      }
    }
    
    return { added, removed, modified };
  }

  private static async storeDocumentToR2(version: DocumentVersion): Promise<void> {
    // R2存储实现
    console.log(`存储文档版本到R2: ${version.r2Key}`);
    // 实际实现需要使用Cloudflare R2 API
  }

  private static async updateDocumentMetadata(
    document: GitHubDocument,
    version: DocumentVersion
  ): Promise<void> {
    // D1数据库更新实现
    console.log(`更新文档元数据: ${document.id}`);
    // 实际实现需要使用Cloudflare D1 API
  }

  private static async getProjectDocuments(projectId: string): Promise<GitHubDocument[]> {
    // 从D1查询项目文档
    return [];
  }

  private static async updateProjectSyncStatus(
    projectId: string,
    status: Partial<SyncProject>
  ): Promise<void> {
    // 更新项目同步状态
    console.log(`更新项目同步状态: ${projectId}`, status);
  }

  private static async getVersionContent(
    documentId: string,
    version: string
  ): Promise<string> {
    // 从R2获取版本内容
    return '';
  }

  private static performDiff(
    contentA: string,
    contentB: string
  ): { added: string[]; removed: string[]; modified: string[] } {
    // 实现diff算法
    return { added: [], removed: [], modified: [] };
  }
}
