/**
 * 多项目本地扫描器
 * 支持扫描和管理多个本地项目
 */

import { Project } from '../types/MultiProject';
import { MultiProjectService } from './MultiProjectService';

export interface LocalProjectInfo {
  path: string;
  name: string;
  type: ProjectType;
  framework: string[];
  packageManager: string;
  gitRepository: boolean;
  lastModified: string;
  size: number;
  fileCount: number;
  structure: ProjectStructure;
  dependencies: string[];
  scripts: { [key: string]: string };
  linkedProjectId?: string; // 关联的在线项目ID
}

export type ProjectType = 'web' | 'mobile' | 'desktop' | 'library' | 'api' | 'unknown';

export interface ProjectStructure {
  src: string[];
  components: string[];
  services: string[];
  pages: string[];
  tests: string[];
  docs: string[];
  config: string[];
  assets: string[];
}

export interface ScanResult {
  totalProjects: number;
  scannedPaths: string[];
  foundProjects: LocalProjectInfo[];
  errors: string[];
  scanTime: number;
}

export class MultiProjectLocalScanner {
  private static readonly LOCAL_PROJECTS_KEY = 'dev-daily-v2-local-projects';
  private static readonly SCAN_HISTORY_KEY = 'dev-daily-v2-scan-history';

  /**
   * 扫描指定目录下的所有项目
   */
  static async scanDirectory(basePath: string): Promise<ScanResult> {
    const startTime = Date.now();
    const result: ScanResult = {
      totalProjects: 0,
      scannedPaths: [],
      foundProjects: [],
      errors: [],
      scanTime: 0
    };

    try {
      // 浏览器环境下模拟扫描
      const mockProjects = this.generateMockLocalProjects(basePath);
      result.foundProjects = mockProjects;
      result.totalProjects = mockProjects.length;
      result.scannedPaths = [basePath];
      
      // 保存扫描结果
      this.saveLocalProjects(mockProjects);
      this.saveScanHistory({
        timestamp: new Date().toISOString(),
        basePath,
        foundCount: mockProjects.length,
        scanTime: Date.now() - startTime
      });

    } catch (error) {
      result.errors.push(`扫描失败: ${error}`);
    }

    result.scanTime = Date.now() - startTime;
    return result;
  }

  /**
   * 生成模拟的本地项目数据
   */
  private static generateMockLocalProjects(basePath: string): LocalProjectInfo[] {
    const mockProjects: LocalProjectInfo[] = [
      {
        path: `${basePath}/dev-daily-v2`,
        name: 'dev-daily-v2',
        type: 'web',
        framework: ['React', 'TypeScript', 'Vite'],
        packageManager: 'npm',
        gitRepository: true,
        lastModified: new Date().toISOString(),
        size: 45000000, // 45MB
        fileCount: 156,
        structure: {
          src: ['src/components', 'src/services', 'src/types', 'src/hooks'],
          components: ['Dashboard', 'ProjectTree', 'Calendar', 'Roadmap', 'DevSync'],
          services: ['BackupService', 'ProjectLogService', 'GitIntegrationService'],
          pages: ['MainDashboard'],
          tests: ['__tests__'],
          docs: ['docs'],
          config: ['vite.config.ts', 'tailwind.config.js', 'tsconfig.json'],
          assets: ['public']
        },
        dependencies: ['react', 'typescript', 'vite', 'tailwindcss', 'lucide-react'],
        scripts: {
          'dev': 'vite',
          'build': 'vite build',
          'preview': 'vite preview'
        }
      },
      {
        path: `${basePath}/mobile-app`,
        name: 'mobile-app',
        type: 'mobile',
        framework: ['React Native', 'TypeScript'],
        packageManager: 'yarn',
        gitRepository: true,
        lastModified: new Date(Date.now() - 86400000).toISOString(),
        size: 32000000, // 32MB
        fileCount: 89,
        structure: {
          src: ['src/screens', 'src/components', 'src/services'],
          components: ['Button', 'Input', 'Modal'],
          services: ['ApiService', 'AuthService'],
          pages: ['HomeScreen', 'ProfileScreen'],
          tests: ['__tests__'],
          docs: ['README.md'],
          config: ['metro.config.js', 'babel.config.js'],
          assets: ['assets']
        },
        dependencies: ['react-native', 'typescript', '@react-navigation/native'],
        scripts: {
          'start': 'react-native start',
          'android': 'react-native run-android',
          'ios': 'react-native run-ios'
        }
      },
      {
        path: `${basePath}/api-server`,
        name: 'api-server',
        type: 'api',
        framework: ['Node.js', 'Express', 'TypeScript'],
        packageManager: 'npm',
        gitRepository: true,
        lastModified: new Date(Date.now() - 172800000).toISOString(),
        size: 18000000, // 18MB
        fileCount: 67,
        structure: {
          src: ['src/routes', 'src/controllers', 'src/models', 'src/middleware'],
          components: [],
          services: ['DatabaseService', 'AuthService', 'EmailService'],
          pages: [],
          tests: ['tests'],
          docs: ['docs/api.md'],
          config: ['tsconfig.json', 'nodemon.json'],
          assets: []
        },
        dependencies: ['express', 'typescript', 'mongoose', 'jsonwebtoken'],
        scripts: {
          'start': 'node dist/index.js',
          'dev': 'nodemon src/index.ts',
          'build': 'tsc'
        }
      }
    ];

    // 检查是否已有真实项目数据
    const existingProjects = this.getLocalProjects();
    if (existingProjects.length > 0) {
      return existingProjects;
    }

    // 不再生成模拟数据，返回空数组
    // 用户需要通过手动添加或文档上传来添加项目
    return [];
  }

  /**
   * 获取所有本地项目
   */
  static getLocalProjects(): LocalProjectInfo[] {
    const stored = localStorage.getItem(this.LOCAL_PROJECTS_KEY);
    return stored ? JSON.parse(stored) : [];
  }

  /**
   * 保存本地项目信息
   */
  static saveLocalProjects(projects: LocalProjectInfo[]): void {
    localStorage.setItem(this.LOCAL_PROJECTS_KEY, JSON.stringify(projects));
  }

  /**
   * 关联本地项目与在线项目
   */
  static linkLocalToOnlineProject(localPath: string, onlineProjectId: string): void {
    const localProjects = this.getLocalProjects();
    const project = localProjects.find(p => p.path === localPath);
    
    if (project) {
      project.linkedProjectId = onlineProjectId;
      this.saveLocalProjects(localProjects);
    }
  }

  /**
   * 获取项目关联状态
   */
  static getProjectLinkStatus(): { linked: number; unlinked: number; total: number } {
    const localProjects = this.getLocalProjects();
    const linked = localProjects.filter(p => p.linkedProjectId).length;
    
    return {
      linked,
      unlinked: localProjects.length - linked,
      total: localProjects.length
    };
  }

  /**
   * 自动关联项目
   */
  static autoLinkProjects(): void {
    const localProjects = this.getLocalProjects();
    const onlineProjects = MultiProjectService.getAllProjects();

    localProjects.forEach(localProject => {
      if (!localProject.linkedProjectId) {
        // 尝试通过名称匹配
        const matchedOnline = onlineProjects.find(online => 
          online.name.toLowerCase() === localProject.name.toLowerCase()
        );
        
        if (matchedOnline) {
          localProject.linkedProjectId = matchedOnline.id;
        }
      }
    });

    this.saveLocalProjects(localProjects);
  }

  /**
   * 分析项目技术栈
   */
  static analyzeProjectTech(project: LocalProjectInfo): {
    frontend: string[];
    backend: string[];
    database: string[];
    tools: string[];
  } {
    const analysis = {
      frontend: [] as string[],
      backend: [] as string[],
      database: [] as string[],
      tools: [] as string[]
    };

    // 分析依赖
    project.dependencies.forEach(dep => {
      if (['react', 'vue', 'angular'].some(fw => dep.includes(fw))) {
        analysis.frontend.push(dep);
      } else if (['express', 'koa', 'fastify'].some(fw => dep.includes(fw))) {
        analysis.backend.push(dep);
      } else if (['mongodb', 'mysql', 'postgres'].some(db => dep.includes(db))) {
        analysis.database.push(dep);
      } else if (['webpack', 'vite', 'rollup'].some(tool => dep.includes(tool))) {
        analysis.tools.push(dep);
      }
    });

    return analysis;
  }

  /**
   * 获取项目健康度评分
   */
  static getProjectHealthScore(project: LocalProjectInfo): {
    score: number;
    factors: { name: string; score: number; weight: number }[];
  } {
    const factors = [
      {
        name: '文档完整性',
        score: project.structure.docs.length > 0 ? 100 : 0,
        weight: 0.2
      },
      {
        name: '测试覆盖',
        score: project.structure.tests.length > 0 ? 80 : 0,
        weight: 0.3
      },
      {
        name: 'Git管理',
        score: project.gitRepository ? 100 : 0,
        weight: 0.2
      },
      {
        name: '代码结构',
        score: project.structure.src.length > 2 ? 90 : 50,
        weight: 0.3
      }
    ];

    const totalScore = factors.reduce((sum, factor) => 
      sum + (factor.score * factor.weight), 0
    );

    return {
      score: Math.round(totalScore),
      factors
    };
  }

  /**
   * 保存扫描历史
   */
  private static saveScanHistory(record: any): void {
    const history = this.getScanHistory();
    history.unshift(record);
    
    // 保留最近10次扫描记录
    if (history.length > 10) {
      history.splice(10);
    }
    
    localStorage.setItem(this.SCAN_HISTORY_KEY, JSON.stringify(history));
  }

  /**
   * 获取扫描历史
   */
  static getScanHistory(): any[] {
    const stored = localStorage.getItem(this.SCAN_HISTORY_KEY);
    return stored ? JSON.parse(stored) : [];
  }

  /**
   * 生成项目报告
   */
  static generateProjectReport(): {
    summary: any;
    projects: any[];
    recommendations: string[];
  } {
    const localProjects = this.getLocalProjects();
    const linkStatus = this.getProjectLinkStatus();
    
    const summary = {
      totalProjects: localProjects.length,
      linkedProjects: linkStatus.linked,
      avgHealthScore: localProjects.reduce((sum, p) => 
        sum + this.getProjectHealthScore(p).score, 0
      ) / localProjects.length || 0,
      techStack: this.getMostUsedTechnologies(localProjects)
    };

    const projects = localProjects.map(project => ({
      ...project,
      healthScore: this.getProjectHealthScore(project),
      techAnalysis: this.analyzeProjectTech(project)
    }));

    const recommendations = this.generateRecommendations(localProjects);

    return { summary, projects, recommendations };
  }

  private static getMostUsedTechnologies(projects: LocalProjectInfo[]): string[] {
    const techCount: { [key: string]: number } = {};
    
    projects.forEach(project => {
      project.framework.forEach(tech => {
        techCount[tech] = (techCount[tech] || 0) + 1;
      });
    });

    return Object.entries(techCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([tech]) => tech);
  }

  private static generateRecommendations(projects: LocalProjectInfo[]): string[] {
    const recommendations: string[] = [];
    
    const unlinkedCount = projects.filter(p => !p.linkedProjectId).length;
    if (unlinkedCount > 0) {
      recommendations.push(`有 ${unlinkedCount} 个本地项目未关联在线项目，建议进行关联`);
    }

    const noTestsCount = projects.filter(p => p.structure.tests.length === 0).length;
    if (noTestsCount > 0) {
      recommendations.push(`有 ${noTestsCount} 个项目缺少测试，建议添加测试用例`);
    }

    const noDocsCount = projects.filter(p => p.structure.docs.length === 0).length;
    if (noDocsCount > 0) {
      recommendations.push(`有 ${noDocsCount} 个项目缺少文档，建议完善项目文档`);
    }

    return recommendations;
  }
}
