/**
 * Airtable 集成服务 - 与 Airtable 基础表的双向同步
 */

export interface AirtableConfig {
  apiKey: string;
  baseId: string;
  tableId: string;
}

export interface AirtableRecord {
  id: string;
  fields: Record<string, any>;
  createdTime: string;
}

export interface AirtableTable {
  id: string;
  name: string;
  fields: AirtableField[];
  records: AirtableRecord[];
}

export interface AirtableField {
  id: string;
  name: string;
  type: string;
  options?: any;
}

export class AirtableIntegration {
  private static readonly API_BASE = 'https://api.airtable.com/v0';

  /**
   * 验证 Airtable API 连接
   */
  static async validateConnection(config: AirtableConfig): Promise<{
    valid: boolean;
    base?: any;
    error?: string;
  }> {
    try {
      const response = await fetch(`${this.API_BASE}/${config.baseId}`, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const base = await response.json();
        return { valid: true, base };
      } else {
        const error = await response.json();
        return { valid: false, error: error.error?.message || '认证失败' };
      }
    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : '网络连接失败'
      };
    }
  }

  /**
   * 获取表信息
   */
  static async getTable(config: AirtableConfig): Promise<AirtableTable | null> {
    try {
      // 获取表结构
      const metaResponse = await fetch(`${this.API_BASE}/meta/bases/${config.baseId}/tables`, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`
        }
      });

      if (!metaResponse.ok) return null;

      const metaData = await metaResponse.json();
      const tableInfo = metaData.tables.find((table: any) => table.id === config.tableId);

      if (!tableInfo) return null;

      // 获取记录
      const records = await this.getRecords(config);

      return {
        id: tableInfo.id,
        name: tableInfo.name,
        fields: tableInfo.fields,
        records
      };
    } catch (error) {
      console.error('获取 Airtable 表失败:', error);
      return null;
    }
  }

  /**
   * 获取记录
   */
  static async getRecords(
    config: AirtableConfig,
    options?: {
      filterByFormula?: string;
      sort?: Array<{ field: string; direction: 'asc' | 'desc' }>;
      maxRecords?: number;
      view?: string;
    }
  ): Promise<AirtableRecord[]> {
    try {
      const params = new URLSearchParams();
      
      if (options?.filterByFormula) {
        params.append('filterByFormula', options.filterByFormula);
      }
      if (options?.sort) {
        options.sort.forEach((sort, index) => {
          params.append(`sort[${index}][field]`, sort.field);
          params.append(`sort[${index}][direction]`, sort.direction);
        });
      }
      if (options?.maxRecords) {
        params.append('maxRecords', options.maxRecords.toString());
      }
      if (options?.view) {
        params.append('view', options.view);
      }

      const url = `${this.API_BASE}/${config.baseId}/${config.tableId}${params.toString() ? '?' + params.toString() : ''}`;
      
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        return data.records;
      }
      return [];
    } catch (error) {
      console.error('获取 Airtable 记录失败:', error);
      return [];
    }
  }

  /**
   * 创建记录
   */
  static async createRecord(
    config: AirtableConfig,
    fields: Record<string, any>
  ): Promise<AirtableRecord | null> {
    try {
      const response = await fetch(`${this.API_BASE}/${config.baseId}/${config.tableId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          fields: this.formatFieldsForAirtable(fields)
        })
      });

      if (response.ok) {
        const data = await response.json();
        return data;
      }
      return null;
    } catch (error) {
      console.error('创建 Airtable 记录失败:', error);
      return null;
    }
  }

  /**
   * 更新记录
   */
  static async updateRecord(
    config: AirtableConfig,
    recordId: string,
    fields: Record<string, any>
  ): Promise<AirtableRecord | null> {
    try {
      const response = await fetch(`${this.API_BASE}/${config.baseId}/${config.tableId}/${recordId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          fields: this.formatFieldsForAirtable(fields)
        })
      });

      if (response.ok) {
        const data = await response.json();
        return data;
      }
      return null;
    } catch (error) {
      console.error('更新 Airtable 记录失败:', error);
      return null;
    }
  }

  /**
   * 批量创建或更新记录
   */
  static async batchUpsert(
    config: AirtableConfig,
    records: Array<{ id?: string; fields: Record<string, any> }>
  ): Promise<{ success: boolean; created: number; updated: number; errors: string[] }> {
    const result = { success: true, created: 0, updated: 0, errors: [] as string[] };

    try {
      // 分批处理（Airtable API 限制每次最多 10 条记录）
      const batchSize = 10;
      for (let i = 0; i < records.length; i += batchSize) {
        const batch = records.slice(i, i + batchSize);
        
        const recordsToCreate = batch.filter(r => !r.id);
        const recordsToUpdate = batch.filter(r => r.id);

        // 创建新记录
        if (recordsToCreate.length > 0) {
          const createResponse = await fetch(`${this.API_BASE}/${config.baseId}/${config.tableId}`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${config.apiKey}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              records: recordsToCreate.map(r => ({
                fields: this.formatFieldsForAirtable(r.fields)
              }))
            })
          });

          if (createResponse.ok) {
            const createData = await createResponse.json();
            result.created += createData.records.length;
          } else {
            const error = await createResponse.json();
            result.errors.push(`创建记录失败: ${error.error?.message}`);
          }
        }

        // 更新现有记录
        if (recordsToUpdate.length > 0) {
          const updateResponse = await fetch(`${this.API_BASE}/${config.baseId}/${config.tableId}`, {
            method: 'PATCH',
            headers: {
              'Authorization': `Bearer ${config.apiKey}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              records: recordsToUpdate.map(r => ({
                id: r.id,
                fields: this.formatFieldsForAirtable(r.fields)
              }))
            })
          });

          if (updateResponse.ok) {
            const updateData = await updateResponse.json();
            result.updated += updateData.records.length;
          } else {
            const error = await updateResponse.json();
            result.errors.push(`更新记录失败: ${error.error?.message}`);
          }
        }
      }

      result.success = result.errors.length === 0;
      return result;
    } catch (error) {
      result.success = false;
      result.errors.push(error instanceof Error ? error.message : '批量操作失败');
      return result;
    }
  }

  /**
   * 同步项目数据到 Airtable
   */
  static async syncProjectToAirtable(
    config: AirtableConfig,
    projectData: any,
    fieldMappings: Record<string, string>
  ): Promise<{ success: boolean; message: string; syncedRecords: number }> {
    try {
      const records: Array<{ id?: string; fields: Record<string, any> }> = [];

      // 获取现有记录
      const existingRecords = await this.getRecords(config, {
        filterByFormula: `{ProjectId} = "${projectData.id}"`
      });

      // 处理项目树节点
      if (projectData.projectTree) {
        const nodes = this.flattenProjectTree(projectData.projectTree);
        
        for (const node of nodes) {
          const fields = this.mapProjectNodeToAirtable(node, fieldMappings);
          
          // 查找现有记录
          const existingRecord = existingRecords.find(record => 
            record.fields['NodeId'] === node.id
          );

          records.push({
            id: existingRecord?.id,
            fields
          });
        }
      }

      // 处理文档
      if (projectData.documents) {
        for (const document of projectData.documents) {
          const fields = this.mapDocumentToAirtable(document, fieldMappings);
          
          const existingRecord = existingRecords.find(record => 
            record.fields['DocumentId'] === document.id
          );

          records.push({
            id: existingRecord?.id,
            fields
          });
        }
      }

      // 批量同步
      const result = await this.batchUpsert(config, records);

      return {
        success: result.success,
        message: result.success 
          ? `成功同步 ${result.created + result.updated} 条记录` 
          : `同步失败: ${result.errors.join(', ')}`,
        syncedRecords: result.created + result.updated
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '同步失败',
        syncedRecords: 0
      };
    }
  }

  /**
   * 从 Airtable 同步数据到项目
   */
  static async syncAirtableToProject(
    config: AirtableConfig,
    projectId: string,
    fieldMappings: Record<string, string>
  ): Promise<{ success: boolean; projectData: any; message: string }> {
    try {
      const records = await this.getRecords(config, {
        filterByFormula: `{ProjectId} = "${projectId}"`
      });

      const projectData = {
        id: projectId,
        projectTree: null,
        documents: [] as any[],
        metadata: {
          lastSync: new Date().toISOString(),
          source: 'airtable',
          totalRecords: records.length
        }
      };

      // 处理项目树节点
      const treeRecords = records.filter(record => record.fields['Type'] === 'node');
      if (treeRecords.length > 0) {
        projectData.projectTree = this.buildProjectTreeFromAirtable(treeRecords, fieldMappings);
      }

      // 处理文档
      const documentRecords = records.filter(record => record.fields['Type'] === 'document');
      projectData.documents = documentRecords.map(record => 
        this.mapAirtableToDocument(record, fieldMappings)
      );

      return {
        success: true,
        projectData,
        message: `成功从 Airtable 同步 ${records.length} 条记录`
      };
    } catch (error) {
      return {
        success: false,
        projectData: null,
        message: error instanceof Error ? error.message : '同步失败'
      };
    }
  }

  // ==================== 私有方法 ====================

  private static formatFieldsForAirtable(fields: Record<string, any>): Record<string, any> {
    const formatted: Record<string, any> = {};

    for (const [key, value] of Object.entries(fields)) {
      if (value !== null && value !== undefined) {
        formatted[key] = value;
      }
    }

    return formatted;
  }

  private static flattenProjectTree(tree: any): any[] {
    const nodes: any[] = [];
    
    const traverse = (node: any, level = 0) => {
      nodes.push({ ...node, level });
      if (node.children) {
        node.children.forEach((child: any) => traverse(child, level + 1));
      }
    };

    traverse(tree);
    return nodes;
  }

  private static mapProjectNodeToAirtable(node: any, fieldMappings: Record<string, string>): Record<string, any> {
    const fields: Record<string, any> = {
      Type: 'node',
      NodeId: node.id,
      Name: node.name,
      Status: node.status,
      Progress: node.progress || 0,
      Level: node.level || 0,
      ProjectId: node.projectId
    };

    // 应用字段映射
    for (const [localField, airtableField] of Object.entries(fieldMappings)) {
      if (node[localField] !== undefined) {
        fields[airtableField] = node[localField];
      }
    }

    return fields;
  }

  private static mapDocumentToAirtable(document: any, fieldMappings: Record<string, string>): Record<string, any> {
    const fields: Record<string, any> = {
      Type: 'document',
      DocumentId: document.id,
      Title: document.title,
      DocumentType: document.type,
      Content: document.content || '',
      CreatedAt: document.createdAt,
      UpdatedAt: document.updatedAt,
      ProjectId: document.projectId
    };

    // 应用字段映射
    for (const [localField, airtableField] of Object.entries(fieldMappings)) {
      if (document[localField] !== undefined) {
        fields[airtableField] = document[localField];
      }
    }

    return fields;
  }

  private static buildProjectTreeFromAirtable(records: AirtableRecord[], fieldMappings: Record<string, string>): any {
    // 构建项目树的逻辑（简化实现）
    const nodeMap = new Map();
    
    records.forEach(record => {
      const nodeId = record.fields['NodeId'];
      const level = record.fields['Level'] || 0;
      
      nodeMap.set(nodeId, {
        id: nodeId,
        name: record.fields['Name'],
        status: record.fields['Status'],
        progress: record.fields['Progress'] || 0,
        level,
        children: []
      });
    });

    const rootNodes = Array.from(nodeMap.values()).filter((node: any) => node.level === 0);
    return rootNodes[0] || null;
  }

  private static mapAirtableToDocument(record: AirtableRecord, fieldMappings: Record<string, string>): any {
    return {
      id: record.fields['DocumentId'],
      title: record.fields['Title'],
      type: record.fields['DocumentType'],
      content: record.fields['Content'] || '',
      createdAt: record.fields['CreatedAt'],
      updatedAt: record.fields['UpdatedAt'],
      source: 'airtable'
    };
  }
}
