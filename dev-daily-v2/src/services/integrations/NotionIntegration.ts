/**
 * Notion 集成服务 - 与 Notion 数据库的双向同步
 */

export interface NotionConfig {
  apiKey: string;
  databaseId: string;
  workspaceId?: string;
}

export interface NotionPage {
  id: string;
  title: string;
  properties: Record<string, any>;
  content?: string;
  lastModified: string;
  url: string;
}

export interface NotionDatabase {
  id: string;
  title: string;
  properties: Record<string, any>;
  pages: NotionPage[];
}

export interface SyncMapping {
  projectId: string;
  notionDatabaseId: string;
  fieldMappings: Record<string, string>; // local field -> notion property
}

export class NotionIntegration {
  private static readonly API_BASE = 'https://api.notion.com/v1';
  private static readonly VERSION = '2022-06-28';

  /**
   * 验证 Notion API 连接
   */
  static async validateConnection(config: NotionConfig): Promise<{
    valid: boolean;
    user?: any;
    error?: string;
  }> {
    try {
      const response = await fetch(`${this.API_BASE}/users/me`, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Notion-Version': this.VERSION,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const user = await response.json();
        return { valid: true, user };
      } else {
        const error = await response.json();
        return { valid: false, error: error.message || '认证失败' };
      }
    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : '网络连接失败'
      };
    }
  }

  /**
   * 获取数据库信息
   */
  static async getDatabase(config: NotionConfig): Promise<NotionDatabase | null> {
    try {
      const response = await fetch(`${this.API_BASE}/databases/${config.databaseId}`, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Notion-Version': this.VERSION
        }
      });

      if (response.ok) {
        const database = await response.json();
        return {
          id: database.id,
          title: database.title?.[0]?.plain_text || 'Untitled',
          properties: database.properties,
          pages: []
        };
      }
      return null;
    } catch (error) {
      console.error('获取 Notion 数据库失败:', error);
      return null;
    }
  }

  /**
   * 查询数据库页面
   */
  static async queryDatabase(
    config: NotionConfig,
    filter?: any,
    sorts?: any[]
  ): Promise<NotionPage[]> {
    try {
      const body: any = {};
      if (filter) body.filter = filter;
      if (sorts) body.sorts = sorts;

      const response = await fetch(`${this.API_BASE}/databases/${config.databaseId}/query`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Notion-Version': this.VERSION,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      });

      if (response.ok) {
        const data = await response.json();
        return data.results.map((page: any) => this.formatNotionPage(page));
      }
      return [];
    } catch (error) {
      console.error('查询 Notion 数据库失败:', error);
      return [];
    }
  }

  /**
   * 创建页面
   */
  static async createPage(
    config: NotionConfig,
    properties: Record<string, any>,
    content?: string
  ): Promise<NotionPage | null> {
    try {
      const body: any = {
        parent: { database_id: config.databaseId },
        properties: this.formatPropertiesForNotion(properties)
      };

      if (content) {
        body.children = this.formatContentForNotion(content);
      }

      const response = await fetch(`${this.API_BASE}/pages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Notion-Version': this.VERSION,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(body)
      });

      if (response.ok) {
        const page = await response.json();
        return this.formatNotionPage(page);
      }
      return null;
    } catch (error) {
      console.error('创建 Notion 页面失败:', error);
      return null;
    }
  }

  /**
   * 更新页面
   */
  static async updatePage(
    config: NotionConfig,
    pageId: string,
    properties: Record<string, any>
  ): Promise<NotionPage | null> {
    try {
      const response = await fetch(`${this.API_BASE}/pages/${pageId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Notion-Version': this.VERSION,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          properties: this.formatPropertiesForNotion(properties)
        })
      });

      if (response.ok) {
        const page = await response.json();
        return this.formatNotionPage(page);
      }
      return null;
    } catch (error) {
      console.error('更新 Notion 页面失败:', error);
      return null;
    }
  }

  /**
   * 同步项目数据到 Notion
   */
  static async syncProjectToNotion(
    config: NotionConfig,
    mapping: SyncMapping,
    projectData: any
  ): Promise<{ success: boolean; message: string; syncedPages: number }> {
    try {
      let syncedPages = 0;

      // 查询现有页面
      const existingPages = await this.queryDatabase(config, {
        property: 'ProjectId',
        rich_text: { equals: mapping.projectId }
      });

      // 同步项目树节点
      if (projectData.projectTree) {
        const nodes = this.flattenProjectTree(projectData.projectTree);
        
        for (const node of nodes) {
          const properties = this.mapProjectNodeToNotion(node, mapping.fieldMappings);
          
          // 查找现有页面
          const existingPage = existingPages.find(page => 
            this.getPropertyValue(page.properties, 'NodeId') === node.id
          );

          if (existingPage) {
            // 更新现有页面
            await this.updatePage(config, existingPage.id, properties);
          } else {
            // 创建新页面
            await this.createPage(config, properties);
          }
          syncedPages++;
        }
      }

      // 同步文档
      if (projectData.documents) {
        for (const document of projectData.documents) {
          const properties = this.mapDocumentToNotion(document, mapping.fieldMappings);
          
          const existingPage = existingPages.find(page => 
            this.getPropertyValue(page.properties, 'DocumentId') === document.id
          );

          if (existingPage) {
            await this.updatePage(config, existingPage.id, properties);
          } else {
            await this.createPage(config, properties, document.content);
          }
          syncedPages++;
        }
      }

      return {
        success: true,
        message: `成功同步 ${syncedPages} 个项目`,
        syncedPages
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '同步失败',
        syncedPages: 0
      };
    }
  }

  /**
   * 从 Notion 同步数据到项目
   */
  static async syncNotionToProject(
    config: NotionConfig,
    mapping: SyncMapping
  ): Promise<{ success: boolean; projectData: any; message: string }> {
    try {
      const pages = await this.queryDatabase(config, {
        property: 'ProjectId',
        rich_text: { equals: mapping.projectId }
      });

      const projectData = {
        projectTree: null,
        documents: [] as any[],
        metadata: {
          lastSync: new Date().toISOString(),
          source: 'notion',
          totalPages: pages.length
        }
      };

      // 处理项目树节点
      const treeNodes = pages.filter(page => 
        this.getPropertyValue(page.properties, 'Type') === 'node'
      );

      if (treeNodes.length > 0) {
        projectData.projectTree = this.buildProjectTreeFromNotion(treeNodes, mapping.fieldMappings);
      }

      // 处理文档
      const documentPages = pages.filter(page => 
        this.getPropertyValue(page.properties, 'Type') === 'document'
      );

      projectData.documents = documentPages.map(page => 
        this.mapNotionToDocument(page, mapping.fieldMappings)
      );

      return {
        success: true,
        projectData,
        message: `成功从 Notion 同步 ${pages.length} 个项目`
      };
    } catch (error) {
      return {
        success: false,
        projectData: null,
        message: error instanceof Error ? error.message : '同步失败'
      };
    }
  }

  // ==================== 私有方法 ====================

  private static formatNotionPage(page: any): NotionPage {
    return {
      id: page.id,
      title: this.getPageTitle(page),
      properties: page.properties,
      lastModified: page.last_edited_time,
      url: page.url
    };
  }

  private static getPageTitle(page: any): string {
    const titleProperty = Object.values(page.properties).find((prop: any) => 
      prop.type === 'title'
    ) as any;
    
    if (titleProperty?.title?.[0]?.plain_text) {
      return titleProperty.title[0].plain_text;
    }
    return 'Untitled';
  }

  private static getPropertyValue(properties: any, propertyName: string): any {
    const property = properties[propertyName];
    if (!property) return null;

    switch (property.type) {
      case 'title':
        return property.title?.[0]?.plain_text || '';
      case 'rich_text':
        return property.rich_text?.[0]?.plain_text || '';
      case 'number':
        return property.number;
      case 'select':
        return property.select?.name;
      case 'multi_select':
        return property.multi_select?.map((item: any) => item.name) || [];
      case 'date':
        return property.date?.start;
      case 'checkbox':
        return property.checkbox;
      default:
        return null;
    }
  }

  private static formatPropertiesForNotion(properties: Record<string, any>): Record<string, any> {
    const formatted: Record<string, any> = {};

    for (const [key, value] of Object.entries(properties)) {
      if (typeof value === 'string') {
        formatted[key] = {
          rich_text: [{ text: { content: value } }]
        };
      } else if (typeof value === 'number') {
        formatted[key] = { number: value };
      } else if (typeof value === 'boolean') {
        formatted[key] = { checkbox: value };
      } else if (Array.isArray(value)) {
        formatted[key] = {
          multi_select: value.map(item => ({ name: item }))
        };
      }
    }

    return formatted;
  }

  private static formatContentForNotion(content: string): any[] {
    return [{
      object: 'block',
      type: 'paragraph',
      paragraph: {
        rich_text: [{ text: { content } }]
      }
    }];
  }

  private static flattenProjectTree(tree: any): any[] {
    const nodes: any[] = [];
    
    const traverse = (node: any, level = 0) => {
      nodes.push({ ...node, level });
      if (node.children) {
        node.children.forEach((child: any) => traverse(child, level + 1));
      }
    };

    traverse(tree);
    return nodes;
  }

  private static mapProjectNodeToNotion(node: any, fieldMappings: Record<string, string>): Record<string, any> {
    const properties: Record<string, any> = {
      Type: 'node',
      NodeId: node.id,
      Title: node.name,
      Status: node.status,
      Progress: node.progress || 0,
      Level: node.level || 0
    };

    // 应用字段映射
    for (const [localField, notionProperty] of Object.entries(fieldMappings)) {
      if (node[localField] !== undefined) {
        properties[notionProperty] = node[localField];
      }
    }

    return properties;
  }

  private static mapDocumentToNotion(document: any, fieldMappings: Record<string, string>): Record<string, any> {
    const properties: Record<string, any> = {
      Type: 'document',
      DocumentId: document.id,
      Title: document.title,
      DocumentType: document.type,
      CreatedAt: document.createdAt,
      UpdatedAt: document.updatedAt
    };

    // 应用字段映射
    for (const [localField, notionProperty] of Object.entries(fieldMappings)) {
      if (document[localField] !== undefined) {
        properties[notionProperty] = document[localField];
      }
    }

    return properties;
  }

  private static buildProjectTreeFromNotion(nodes: NotionPage[], fieldMappings: Record<string, string>): any {
    // 构建项目树的逻辑
    const nodeMap = new Map();
    
    // 首先创建所有节点
    nodes.forEach(page => {
      const nodeId = this.getPropertyValue(page.properties, 'NodeId');
      const level = this.getPropertyValue(page.properties, 'Level') || 0;
      
      nodeMap.set(nodeId, {
        id: nodeId,
        name: this.getPropertyValue(page.properties, 'Title'),
        status: this.getPropertyValue(page.properties, 'Status'),
        progress: this.getPropertyValue(page.properties, 'Progress') || 0,
        level,
        children: []
      });
    });

    // 构建树结构（简化实现）
    const rootNodes = Array.from(nodeMap.values()).filter((node: any) => node.level === 0);
    return rootNodes[0] || null;
  }

  private static mapNotionToDocument(page: NotionPage, fieldMappings: Record<string, string>): any {
    return {
      id: this.getPropertyValue(page.properties, 'DocumentId'),
      title: this.getPropertyValue(page.properties, 'Title'),
      type: this.getPropertyValue(page.properties, 'DocumentType'),
      content: page.content || '',
      createdAt: this.getPropertyValue(page.properties, 'CreatedAt'),
      updatedAt: this.getPropertyValue(page.properties, 'UpdatedAt'),
      source: 'notion'
    };
  }
}
