/**
 * 文件监听服务 - 监控本地项目文件变化
 * 
 * 功能：
 * 1. 监听项目文件变化
 * 2. 分析代码结构变化
 * 3. 自动识别功能完成
 * 4. 同步更新项目进度
 */

import { ProjectLogService } from './ProjectLogService';

export interface FileChange {
  path: string;
  type: 'created' | 'modified' | 'deleted';
  timestamp: string;
  size?: number;
  nodeId?: string;
}

export interface ProjectStructure {
  components: string[];
  services: string[];
  pages: string[];
  tests: string[];
  docs: string[];
  config: string[];
}

export class FileWatcherService {
  private static readonly STORAGE_KEY = 'dev-daily-v2-file-changes';
  private static readonly STRUCTURE_KEY = 'dev-daily-v2-project-structure';
  
  private static watchers: { [path: string]: any } = {};
  private static isWatching = false;

  /**
   * 开始监听项目文件 (浏览器环境模拟)
   */
  static startWatching() {
    if (this.isWatching) return;
    
    this.isWatching = true;
    console.log('开始监听项目文件变化...');
    
    // 浏览器环境下模拟文件监听
    // 实际项目中需要使用 Node.js fs.watch 或 chokidar
    this.simulateFileWatching();
  }

  /**
   * 停止监听
   */
  static stopWatching() {
    this.isWatching = false;
    Object.values(this.watchers).forEach(watcher => {
      if (watcher && typeof watcher.close === 'function') {
        watcher.close();
      }
    });
    this.watchers = {};
    console.log('停止监听项目文件变化');
  }

  /**
   * 模拟文件监听 (用于演示)
   */
  private static simulateFileWatching() {
    // 模拟定期检查文件变化
    const checkInterval = setInterval(() => {
      if (!this.isWatching) {
        clearInterval(checkInterval);
        return;
      }
      
      // 模拟随机文件变化
      if (Math.random() < 0.1) { // 10% 概率
        this.simulateFileChange();
      }
    }, 5000); // 每5秒检查一次
  }

  /**
   * 模拟文件变化
   */
  private static simulateFileChange() {
    const sampleFiles = [
      'src/components/ProjectTree/ProjectTreeNode.tsx',
      'src/components/Roadmap/ProjectRoadmap.tsx',
      'src/services/BackupService.ts',
      'src/components/Calendar/ProjectCalendar.tsx',
      'docs/feature-guide.md',
      'src/types/index.ts'
    ];

    const randomFile = sampleFiles[Math.floor(Math.random() * sampleFiles.length)];
    const changeType = ['modified', 'created'][Math.floor(Math.random() * 2)] as 'modified' | 'created';
    
    const change: FileChange = {
      path: randomFile,
      type: changeType,
      timestamp: new Date().toISOString(),
      size: Math.floor(Math.random() * 10000) + 1000,
      nodeId: this.extractNodeIdFromPath(randomFile)
    };

    this.handleFileChange(change);
  }

  /**
   * 处理文件变化
   */
  static handleFileChange(change: FileChange) {
    // 保存变化记录
    this.saveFileChange(change);

    // 分析变化类型
    const analysis = this.analyzeFileChange(change);

    // 自动更新项目进度
    if (analysis.shouldUpdateProgress) {
      this.updateProjectProgress(change, analysis);
    }

    // 更新项目结构
    this.updateProjectStructure(change);

    console.log(`文件变化: ${change.path} (${change.type})`);
  }

  /**
   * 分析文件变化
   */
  private static analyzeFileChange(change: FileChange) {
    const analysis = {
      shouldUpdateProgress: false,
      estimatedHours: 0,
      changeType: 'minor',
      relatedNode: change.nodeId
    };

    // 根据文件类型和路径分析
    if (change.path.includes('components/') && change.type === 'created') {
      analysis.shouldUpdateProgress = true;
      analysis.estimatedHours = 2;
      analysis.changeType = 'feature';
    }

    if (change.path.includes('services/') && change.type === 'modified') {
      analysis.shouldUpdateProgress = true;
      analysis.estimatedHours = 1;
      analysis.changeType = 'enhancement';
    }

    if (change.path.includes('test/') || change.path.includes('.test.')) {
      analysis.changeType = 'testing';
      analysis.estimatedHours = 0.5;
    }

    if (change.path.includes('docs/')) {
      analysis.changeType = 'documentation';
      analysis.estimatedHours = 0.5;
    }

    return analysis;
  }

  /**
   * 从文件路径提取节点ID
   */
  private static extractNodeIdFromPath(filePath: string): string | undefined {
    const pathMappings: { [pattern: string]: string } = {
      'ProjectTree': 'B1',
      'Roadmap': 'B1',
      'Calendar': 'B2',
      'Backup': 'D3',
      'AI': 'B3',
      'ThinkingModels': 'B4',
      'Documents': 'B2',
      'Guide': 'C1',
      'Dashboard': 'C1'
    };

    for (const [pattern, nodeId] of Object.entries(pathMappings)) {
      if (filePath.includes(pattern)) {
        return nodeId;
      }
    }

    return undefined;
  }

  /**
   * 更新项目进度
   */
  private static updateProjectProgress(change: FileChange, analysis: any) {
    if (!change.nodeId) return;

    const action = analysis.changeType === 'feature' ? 'task_completed' : 'task_started';
    
    ProjectLogService.addLogEntry({
      date: change.timestamp.split('T')[0],
      projectId: 'root',
      projectName: 'dev-daily v2 项目',
      nodeId: change.nodeId,
      nodeName: this.getNodeNameById(change.nodeId),
      action: action as any,
      description: `文件变化: ${change.path} (${change.type})`,
      metadata: {
        hoursWorked: analysis.estimatedHours,
        tags: ['file-watcher', analysis.changeType],
        linkedDocuments: [change.path]
      }
    });
  }

  /**
   * 获取节点名称
   */
  private static getNodeNameById(nodeId: string): string {
    const nodeNames: { [key: string]: string } = {
      'B1': 'B1: Project Tree管理',
      'B2': 'B2: 文档管理系统',
      'B3': 'B3: AI功能集成',
      'B4': 'B4: 思维模型系统',
      'C1': 'C1: 主仪表板',
      'C2': 'C2: 响应式设计',
      'C3': 'C3: 思维模型界面',
      'D3': 'D3: 备份与恢复'
    };
    
    return nodeNames[nodeId] || `节点 ${nodeId}`;
  }

  /**
   * 保存文件变化记录
   */
  private static saveFileChange(change: FileChange) {
    const changes = this.getAllFileChanges();
    changes.unshift(change);
    
    // 保留最近500条记录
    if (changes.length > 500) {
      changes.splice(500);
    }
    
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(changes));
  }

  /**
   * 获取所有文件变化记录
   */
  static getAllFileChanges(): FileChange[] {
    const stored = localStorage.getItem(this.STORAGE_KEY);
    return stored ? JSON.parse(stored) : [];
  }

  /**
   * 更新项目结构
   */
  private static updateProjectStructure(change: FileChange) {
    const structure = this.getProjectStructure();
    
    if (change.type === 'created' || change.type === 'modified') {
      if (change.path.includes('components/')) {
        if (!structure.components.includes(change.path)) {
          structure.components.push(change.path);
        }
      } else if (change.path.includes('services/')) {
        if (!structure.services.includes(change.path)) {
          structure.services.push(change.path);
        }
      } else if (change.path.includes('pages/')) {
        if (!structure.pages.includes(change.path)) {
          structure.pages.push(change.path);
        }
      } else if (change.path.includes('test/') || change.path.includes('.test.')) {
        if (!structure.tests.includes(change.path)) {
          structure.tests.push(change.path);
        }
      } else if (change.path.includes('docs/')) {
        if (!structure.docs.includes(change.path)) {
          structure.docs.push(change.path);
        }
      } else if (change.path.includes('config/') || change.path.endsWith('.config.js')) {
        if (!structure.config.includes(change.path)) {
          structure.config.push(change.path);
        }
      }
    } else if (change.type === 'deleted') {
      // 从结构中移除删除的文件
      Object.keys(structure).forEach(key => {
        const files = structure[key as keyof ProjectStructure] as string[];
        const index = files.indexOf(change.path);
        if (index > -1) {
          files.splice(index, 1);
        }
      });
    }
    
    localStorage.setItem(this.STRUCTURE_KEY, JSON.stringify(structure));
  }

  /**
   * 获取项目结构
   */
  static getProjectStructure(): ProjectStructure {
    const stored = localStorage.getItem(this.STRUCTURE_KEY);
    return stored ? JSON.parse(stored) : {
      components: [],
      services: [],
      pages: [],
      tests: [],
      docs: [],
      config: []
    };
  }

  /**
   * 获取文件活动统计
   */
  static getFileActivityStats(days: number = 7) {
    const changes = this.getAllFileChanges();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const recentChanges = changes.filter(change => 
      new Date(change.timestamp) >= cutoffDate
    );

    const stats = {
      totalChanges: recentChanges.length,
      fileTypes: {} as { [type: string]: number },
      mostActiveFiles: {} as { [file: string]: number },
      changeTypes: {} as { [type: string]: number },
      dailyActivity: {} as { [date: string]: number }
    };

    recentChanges.forEach(change => {
      // 文件类型统计
      const ext = change.path.split('.').pop() || 'unknown';
      stats.fileTypes[ext] = (stats.fileTypes[ext] || 0) + 1;

      // 活跃文件统计
      stats.mostActiveFiles[change.path] = (stats.mostActiveFiles[change.path] || 0) + 1;

      // 变化类型统计
      stats.changeTypes[change.type] = (stats.changeTypes[change.type] || 0) + 1;

      // 每日活动统计
      const dateKey = change.timestamp.split('T')[0];
      stats.dailyActivity[dateKey] = (stats.dailyActivity[dateKey] || 0) + 1;
    });

    return stats;
  }

  /**
   * 生成文件活动报告
   */
  static generateFileActivityReport() {
    const stats = this.getFileActivityStats(30);
    const structure = this.getProjectStructure();
    
    return {
      summary: {
        totalFiles: Object.values(structure).flat().length,
        recentChanges: stats.totalChanges,
        mostActiveType: Object.entries(stats.fileTypes)
          .sort(([,a], [,b]) => b - a)[0]?.[0] || 'none'
      },
      structure,
      activity: stats,
      recommendations: this.generateFileRecommendations(stats, structure)
    };
  }

  /**
   * 生成文件管理建议
   */
  private static generateFileRecommendations(stats: any, structure: ProjectStructure): string[] {
    const recommendations: string[] = [];
    
    if (structure.tests.length < structure.components.length * 0.5) {
      recommendations.push('建议增加测试文件，提高代码覆盖率');
    }
    
    if (structure.docs.length < 5) {
      recommendations.push('建议完善项目文档，便于团队协作');
    }
    
    if (stats.totalChanges > 100) {
      recommendations.push('文件变化频繁，建议定期整理和重构');
    }
    
    return recommendations;
  }
}
