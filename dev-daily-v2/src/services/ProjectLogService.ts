import { format, startOfDay, endOfDay, isWithinInterval } from 'date-fns';
import { DailyProgress } from '../components/Calendar/ProjectCalendar';

export interface ProjectLogEntry {
  id: string;
  date: string;
  projectId: string;
  projectName: string;
  nodeId?: string;
  nodeName?: string;
  action: 'task_completed' | 'task_started' | 'issue_reported' | 'milestone_reached' | 'note_added';
  description: string;
  metadata?: {
    hoursWorked?: number;
    difficulty?: 'easy' | 'medium' | 'hard';
    tags?: string[];
    linkedDocuments?: string[];
  };
  timestamp: string;
}

export interface ProjectAnalytics {
  totalHours: number;
  completedTasks: number;
  issuesCount: number;
  milestonesReached: number;
  averageTaskTime: number;
  productivityTrend: 'up' | 'down' | 'stable';
  commonIssues: string[];
  topPerformingDays: string[];
}

export class ProjectLogService {
  private static readonly STORAGE_KEY = 'dev-daily-v2-project-logs';
  private static readonly DAILY_PROGRESS_KEY = 'dev-daily-v2-daily-progress';

  /**
   * 添加项目日志条目
   */
  static addLogEntry(entry: Omit<ProjectLogEntry, 'id' | 'timestamp'>): ProjectLogEntry {
    const logEntry: ProjectLogEntry = {
      ...entry,
      id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString()
    };

    const logs = this.getAllLogs();
    logs.unshift(logEntry);
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(logs));

    // 更新每日进度
    this.updateDailyProgress(logEntry);

    return logEntry;
  }

  /**
   * 获取所有日志
   */
  static getAllLogs(): ProjectLogEntry[] {
    const stored = localStorage.getItem(this.STORAGE_KEY);
    if (!stored) return [];

    try {
      return JSON.parse(stored) as ProjectLogEntry[];
    } catch (error) {
      console.error('Failed to parse project logs:', error);
      return [];
    }
  }

  /**
   * 获取指定日期范围的日志
   */
  static getLogsByDateRange(startDate: Date, endDate: Date): ProjectLogEntry[] {
    const logs = this.getAllLogs();
    return logs.filter(log => {
      const logDate = new Date(log.date);
      return isWithinInterval(logDate, { start: startOfDay(startDate), end: endOfDay(endDate) });
    });
  }

  /**
   * 获取指定项目的日志
   */
  static getLogsByProject(projectId: string): ProjectLogEntry[] {
    const logs = this.getAllLogs();
    return logs.filter(log => log.projectId === projectId);
  }

  /**
   * 记录任务完成
   */
  static logTaskCompleted(
    projectId: string,
    projectName: string,
    nodeId: string,
    nodeName: string,
    hoursWorked?: number
  ): ProjectLogEntry {
    return this.addLogEntry({
      date: format(new Date(), 'yyyy-MM-dd'),
      projectId,
      projectName,
      nodeId,
      nodeName,
      action: 'task_completed',
      description: `完成任务: ${nodeName}`,
      metadata: {
        hoursWorked,
        tags: ['task', 'completed']
      }
    });
  }

  /**
   * 记录问题报告
   */
  static logIssueReported(
    projectId: string,
    projectName: string,
    issueDescription: string,
    nodeId?: string,
    nodeName?: string
  ): ProjectLogEntry {
    return this.addLogEntry({
      date: format(new Date(), 'yyyy-MM-dd'),
      projectId,
      projectName,
      nodeId,
      nodeName,
      action: 'issue_reported',
      description: `问题报告: ${issueDescription}`,
      metadata: {
        tags: ['issue', 'problem']
      }
    });
  }

  /**
   * 记录里程碑达成
   */
  static logMilestoneReached(
    projectId: string,
    projectName: string,
    milestoneName: string,
    nodeId?: string
  ): ProjectLogEntry {
    return this.addLogEntry({
      date: format(new Date(), 'yyyy-MM-dd'),
      projectId,
      projectName,
      nodeId,
      nodeName: milestoneName,
      action: 'milestone_reached',
      description: `达成里程碑: ${milestoneName}`,
      metadata: {
        tags: ['milestone', 'achievement']
      }
    });
  }

  /**
   * 添加项目笔记
   */
  static addProjectNote(
    projectId: string,
    projectName: string,
    note: string,
    nodeId?: string,
    nodeName?: string
  ): ProjectLogEntry {
    return this.addLogEntry({
      date: format(new Date(), 'yyyy-MM-dd'),
      projectId,
      projectName,
      nodeId,
      nodeName,
      action: 'note_added',
      description: note,
      metadata: {
        tags: ['note', 'documentation']
      }
    });
  }

  /**
   * 获取每日进度数据
   */
  static getDailyProgress(): DailyProgress[] {
    const stored = localStorage.getItem(this.DAILY_PROGRESS_KEY);
    if (!stored) return [];

    try {
      return JSON.parse(stored) as DailyProgress[];
    } catch (error) {
      console.error('Failed to parse daily progress:', error);
      return [];
    }
  }

  /**
   * 更新每日进度
   */
  private static updateDailyProgress(logEntry: ProjectLogEntry): void {
    const dailyProgress = this.getDailyProgress();
    const dateStr = logEntry.date;
    
    let dayProgress = dailyProgress.find(p => p.date === dateStr && p.projectId === logEntry.projectId);
    
    if (!dayProgress) {
      dayProgress = {
        date: dateStr,
        projectId: logEntry.projectId,
        projectName: logEntry.projectName,
        completedTasks: 0,
        totalTasks: 0,
        hoursWorked: 0,
        issues: [],
        achievements: [],
        notes: ''
      };
      dailyProgress.push(dayProgress);
    }

    // 根据日志类型更新进度
    switch (logEntry.action) {
      case 'task_completed':
        dayProgress.completedTasks += 1;
        dayProgress.totalTasks += 1;
        if (logEntry.metadata?.hoursWorked) {
          dayProgress.hoursWorked += logEntry.metadata.hoursWorked;
        }
        if (logEntry.nodeName) {
          dayProgress.achievements.push(logEntry.nodeName);
        }
        break;
      
      case 'task_started':
        dayProgress.totalTasks += 1;
        break;
      
      case 'issue_reported':
        dayProgress.issues.push(logEntry.description);
        break;
      
      case 'milestone_reached':
        if (logEntry.nodeName) {
          dayProgress.achievements.push(`🎯 ${logEntry.nodeName}`);
        }
        break;
      
      case 'note_added':
        if (dayProgress.notes) {
          dayProgress.notes += '\n' + logEntry.description;
        } else {
          dayProgress.notes = logEntry.description;
        }
        break;
    }

    localStorage.setItem(this.DAILY_PROGRESS_KEY, JSON.stringify(dailyProgress));
  }

  /**
   * 生成项目分析报告
   */
  static generateAnalytics(projectId: string, days: number = 30): ProjectAnalytics {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const logs = this.getLogsByDateRange(startDate, endDate)
      .filter(log => log.projectId === projectId);

    const completedTasks = logs.filter(log => log.action === 'task_completed').length;
    const issuesCount = logs.filter(log => log.action === 'issue_reported').length;
    const milestonesReached = logs.filter(log => log.action === 'milestone_reached').length;
    
    const totalHours = logs.reduce((sum, log) => {
      return sum + (log.metadata?.hoursWorked || 0);
    }, 0);

    const averageTaskTime = completedTasks > 0 ? totalHours / completedTasks : 0;

    // 计算生产力趋势
    const recentLogs = logs.filter(log => {
      const logDate = new Date(log.date);
      const recentDate = new Date();
      recentDate.setDate(recentDate.getDate() - 7);
      return logDate >= recentDate;
    });

    const olderLogs = logs.filter(log => {
      const logDate = new Date(log.date);
      const olderDate = new Date();
      olderDate.setDate(olderDate.getDate() - 14);
      const recentDate = new Date();
      recentDate.setDate(recentDate.getDate() - 7);
      return logDate >= olderDate && logDate < recentDate;
    });

    const recentTasksPerDay = recentLogs.filter(l => l.action === 'task_completed').length / 7;
    const olderTasksPerDay = olderLogs.filter(l => l.action === 'task_completed').length / 7;

    let productivityTrend: 'up' | 'down' | 'stable' = 'stable';
    if (recentTasksPerDay > olderTasksPerDay * 1.1) {
      productivityTrend = 'up';
    } else if (recentTasksPerDay < olderTasksPerDay * 0.9) {
      productivityTrend = 'down';
    }

    // 统计常见问题
    const issueDescriptions = logs
      .filter(log => log.action === 'issue_reported')
      .map(log => log.description);
    
    const commonIssues = [...new Set(issueDescriptions)].slice(0, 5);

    // 找出高效工作日
    const dailyStats = new Map<string, number>();
    logs.filter(log => log.action === 'task_completed').forEach(log => {
      const count = dailyStats.get(log.date) || 0;
      dailyStats.set(log.date, count + 1);
    });

    const topPerformingDays = Array.from(dailyStats.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([date]) => date);

    return {
      totalHours,
      completedTasks,
      issuesCount,
      milestonesReached,
      averageTaskTime,
      productivityTrend,
      commonIssues,
      topPerformingDays
    };
  }

  /**
   * 导出日志数据
   */
  static exportLogs(projectId?: string): void {
    const logs = projectId ? this.getLogsByProject(projectId) : this.getAllLogs();
    const dailyProgress = this.getDailyProgress();
    
    const exportData = {
      logs,
      dailyProgress,
      exportDate: new Date().toISOString(),
      version: '2.0.0'
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const blob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `dev-daily-v2-logs-${format(new Date(), 'yyyy-MM-dd')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  /**
   * 清理旧日志
   */
  static cleanupOldLogs(daysToKeep: number = 90): void {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    const logs = this.getAllLogs();
    const filteredLogs = logs.filter(log => new Date(log.date) >= cutoffDate);
    
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredLogs));

    const dailyProgress = this.getDailyProgress();
    const filteredProgress = dailyProgress.filter(p => new Date(p.date) >= cutoffDate);
    
    localStorage.setItem(this.DAILY_PROGRESS_KEY, JSON.stringify(filteredProgress));
  }
}
