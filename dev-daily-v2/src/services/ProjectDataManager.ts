/**
 * 项目数据管理器 - 确保项目间数据完全独立
 */

import { Document, ProjectTreeNode } from '../types';
import { Project } from '../types/MultiProject';

export interface ProjectData {
  documents: Document[];
  projectTree: ProjectTreeNode;
  settings: ProjectSettings;
  localPath?: string;
  gitConfig?: GitConfig;
  lastSync?: string;
  metadata: ProjectMetadata;
}

export interface ProjectSettings {
  autoBackup: boolean;
  fileWatching: boolean;
  gitAutoSync: boolean;
  syncInterval: number; // minutes
  excludePatterns: string[];
  includePatterns: string[];
}

export interface GitConfig {
  repository: string;
  branch: string;
  token?: string;
  webhookUrl?: string;
  autoCommit: boolean;
  commitMessage: string;
}

export interface ProjectMetadata {
  version: string;
  lastModified: string;
  fileCount: number;
  totalSize: number;
  checksum: string;
}

export class ProjectDataManager {
  private static readonly STORAGE_PREFIX = 'dev-daily-v2-project-';
  private static readonly INDEX_KEY = 'dev-daily-v2-project-index';

  /**
   * 获取项目数据（完全独立）
   */
  static getProjectData(projectId: string): ProjectData | null {
    try {
      const key = this.STORAGE_PREFIX + projectId;
      const stored = localStorage.getItem(key);
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      console.error(`获取项目数据失败 [${projectId}]:`, error);
      return null;
    }
  }

  /**
   * 保存项目数据（完全独立）
   */
  static saveProjectData(projectId: string, data: ProjectData): void {
    try {
      const key = this.STORAGE_PREFIX + projectId;
      
      // 更新元数据
      data.metadata = {
        ...data.metadata,
        lastModified: new Date().toISOString(),
        fileCount: data.documents.length,
        totalSize: this.calculateDataSize(data),
        checksum: this.generateChecksum(data)
      };

      localStorage.setItem(key, JSON.stringify(data));
      this.updateProjectIndex(projectId);
      
    } catch (error) {
      console.error(`保存项目数据失败 [${projectId}]:`, error);
    }
  }

  /**
   * 删除项目数据
   */
  static deleteProjectData(projectId: string): void {
    try {
      const key = this.STORAGE_PREFIX + projectId;
      localStorage.removeItem(key);
      this.removeFromProjectIndex(projectId);
    } catch (error) {
      console.error(`删除项目数据失败 [${projectId}]:`, error);
    }
  }

  /**
   * 获取所有项目ID列表
   */
  static getAllProjectIds(): string[] {
    try {
      const stored = localStorage.getItem(this.INDEX_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('获取项目索引失败:', error);
      return [];
    }
  }

  /**
   * 初始化项目数据
   */
  static initializeProjectData(project: Project): ProjectData {
    const defaultData: ProjectData = {
      documents: [],
      projectTree: {
        id: 'root',
        name: project.name,
        description: project.description,
        type: 'project',
        status: 'active',
        priority: project.priority === 'urgent' ? 'high' : project.priority as 'high' | 'medium' | 'low',
        progress: 0,
        children: [],
        metadata: {
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          tags: project.tags
        }
      },
      settings: {
        autoBackup: true,
        fileWatching: false,
        gitAutoSync: false,
        syncInterval: 30,
        excludePatterns: ['node_modules', '.git', 'dist', 'build'],
        includePatterns: ['src/**', 'docs/**', '*.md', '*.json']
      },
      metadata: {
        version: '1.0.0',
        lastModified: new Date().toISOString(),
        fileCount: 0,
        totalSize: 0,
        checksum: ''
      }
    };

    this.saveProjectData(project.id, defaultData);
    return defaultData;
  }

  /**
   * 项目数据迁移
   */
  static migrateProjectData(projectId: string, fromVersion: string, toVersion: string): boolean {
    try {
      const data = this.getProjectData(projectId);
      if (!data) return false;

      // 根据版本进行数据迁移
      if (fromVersion === '1.0.0' && toVersion === '2.0.0') {
        // 执行具体的迁移逻辑
        data.metadata.version = toVersion;
        this.saveProjectData(projectId, data);
      }

      return true;
    } catch (error) {
      console.error(`项目数据迁移失败 [${projectId}]:`, error);
      return false;
    }
  }

  // ==================== 私有方法 ====================

  private static updateProjectIndex(projectId: string): void {
    const index = this.getAllProjectIds();
    if (!index.includes(projectId)) {
      index.push(projectId);
      localStorage.setItem(this.INDEX_KEY, JSON.stringify(index));
    }
  }

  private static removeFromProjectIndex(projectId: string): void {
    const index = this.getAllProjectIds();
    const filtered = index.filter(id => id !== projectId);
    localStorage.setItem(this.INDEX_KEY, JSON.stringify(filtered));
  }

  private static calculateDataSize(data: ProjectData): number {
    return JSON.stringify(data).length;
  }

  private static generateChecksum(data: ProjectData): string {
    // 简单的校验和生成
    const str = JSON.stringify(data);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16);
  }

  /**
   * 数据完整性检查
   */
  static validateProjectData(projectId: string): boolean {
    const data = this.getProjectData(projectId);
    if (!data) return false;

    // 检查必要字段
    if (!data.projectTree || !data.documents || !data.settings) {
      return false;
    }

    // 检查数据完整性
    const currentChecksum = this.generateChecksum(data);
    return currentChecksum === data.metadata.checksum;
  }

  /**
   * 获取项目统计信息
   */
  static getProjectStats(projectId: string): ProjectStats | null {
    const data = this.getProjectData(projectId);
    if (!data) return null;

    return {
      totalDocuments: data.documents.length,
      totalNodes: this.countNodes(data.projectTree),
      completedNodes: this.countNodesByStatus(data.projectTree, 'completed'),
      dataSize: data.metadata.totalSize,
      lastModified: data.metadata.lastModified
    };
  }

  private static countNodes(node: ProjectTreeNode): number {
    let count = 1;
    if (node.children) {
      count += node.children.reduce((sum, child) => sum + this.countNodes(child), 0);
    }
    return count;
  }

  private static countNodesByStatus(node: ProjectTreeNode, status: string): number {
    let count = node.status === status ? 1 : 0;
    if (node.children) {
      count += node.children.reduce((sum, child) => sum + this.countNodesByStatus(child, status), 0);
    }
    return count;
  }
}

interface ProjectStats {
  totalDocuments: number;
  totalNodes: number;
  completedNodes: number;
  dataSize: number;
  lastModified: string;
}
