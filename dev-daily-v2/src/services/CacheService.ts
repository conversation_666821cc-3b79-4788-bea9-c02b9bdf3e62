/**
 * 智能缓存服务 - 多层缓存架构
 */

export interface CacheConfig {
  maxMemorySize: number; // MB
  maxLocalStorageSize: number; // MB
  maxIndexedDBSize: number; // MB
  defaultTTL: number; // 秒
  compressionEnabled: boolean;
}

export interface CacheItem<T> {
  key: string;
  data: T;
  timestamp: number;
  ttl: number;
  size: number;
  accessCount: number;
  lastAccessed: number;
}

export interface CacheStats {
  memoryUsage: number;
  localStorageUsage: number;
  indexedDBUsage: number;
  hitRate: number;
  totalRequests: number;
  totalHits: number;
}

export class CacheService {
  private static instance: CacheService;
  private memoryCache = new Map<string, CacheItem<any>>();
  private config: CacheConfig;
  private stats: CacheStats = {
    memoryUsage: 0,
    localStorageUsage: 0,
    indexedDBUsage: 0,
    hitRate: 0,
    totalRequests: 0,
    totalHits: 0
  };

  private constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxMemorySize: 50, // 50MB
      maxLocalStorageSize: 100, // 100MB
      maxIndexedDBSize: 500, // 500MB
      defaultTTL: 3600, // 1小时
      compressionEnabled: true,
      ...config
    };

    // 定期清理过期缓存
    setInterval(() => this.cleanup(), 60000); // 每分钟清理一次
  }

  static getInstance(config?: Partial<CacheConfig>): CacheService {
    if (!CacheService.instance) {
      CacheService.instance = new CacheService(config);
    }
    return CacheService.instance;
  }

  /**
   * 获取缓存数据
   */
  async get<T>(key: string): Promise<T | null> {
    this.stats.totalRequests++;

    // 1. 检查内存缓存
    const memoryItem = this.memoryCache.get(key);
    if (memoryItem && !this.isExpired(memoryItem)) {
      memoryItem.accessCount++;
      memoryItem.lastAccessed = Date.now();
      this.stats.totalHits++;
      this.updateHitRate();
      return memoryItem.data;
    }

    // 2. 检查 localStorage
    const localStorageItem = this.getFromLocalStorage<T>(key);
    if (localStorageItem) {
      // 提升到内存缓存
      this.setInMemory(key, localStorageItem.data, localStorageItem.ttl);
      this.stats.totalHits++;
      this.updateHitRate();
      return localStorageItem.data;
    }

    // 3. 检查 IndexedDB
    const indexedDBItem = await this.getFromIndexedDB<T>(key);
    if (indexedDBItem) {
      // 提升到内存缓存
      this.setInMemory(key, indexedDBItem.data, indexedDBItem.ttl);
      this.stats.totalHits++;
      this.updateHitRate();
      return indexedDBItem.data;
    }

    this.updateHitRate();
    return null;
  }

  /**
   * 设置缓存数据
   */
  async set<T>(key: string, data: T, ttl?: number): Promise<void> {
    const actualTTL = ttl || this.config.defaultTTL;
    const size = this.calculateSize(data);

    // 根据数据大小选择存储层级
    if (size < 1024 * 1024) { // < 1MB，存储在内存
      this.setInMemory(key, data, actualTTL);
    } else if (size < 10 * 1024 * 1024) { // < 10MB，存储在 localStorage
      this.setInLocalStorage(key, data, actualTTL);
    } else { // >= 10MB，存储在 IndexedDB
      await this.setInIndexedDB(key, data, actualTTL);
    }
  }

  /**
   * 删除缓存
   */
  async delete(key: string): Promise<void> {
    this.memoryCache.delete(key);
    this.deleteFromLocalStorage(key);
    await this.deleteFromIndexedDB(key);
  }

  /**
   * 清空所有缓存
   */
  async clear(): Promise<void> {
    this.memoryCache.clear();
    this.clearLocalStorage();
    await this.clearIndexedDB();
    this.resetStats();
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    this.updateStats();
    return { ...this.stats };
  }

  // ==================== 私有方法 ====================

  private setInMemory<T>(key: string, data: T, ttl: number): void {
    const item: CacheItem<T> = {
      key,
      data,
      timestamp: Date.now(),
      ttl,
      size: this.calculateSize(data),
      accessCount: 1,
      lastAccessed: Date.now()
    };

    // 检查内存限制
    if (this.stats.memoryUsage + item.size > this.config.maxMemorySize * 1024 * 1024) {
      this.evictMemoryCache();
    }

    this.memoryCache.set(key, item);
    this.stats.memoryUsage += item.size;
  }

  private getFromLocalStorage<T>(key: string): CacheItem<T> | null {
    try {
      const item = localStorage.getItem(`cache_${key}`);
      if (item) {
        const parsed: CacheItem<T> = JSON.parse(item);
        if (!this.isExpired(parsed)) {
          return parsed;
        } else {
          this.deleteFromLocalStorage(key);
        }
      }
    } catch (error) {
      console.warn('LocalStorage 读取失败:', error);
    }
    return null;
  }

  private setInLocalStorage<T>(key: string, data: T, ttl: number): void {
    try {
      const item: CacheItem<T> = {
        key,
        data,
        timestamp: Date.now(),
        ttl,
        size: this.calculateSize(data),
        accessCount: 1,
        lastAccessed: Date.now()
      };

      localStorage.setItem(`cache_${key}`, JSON.stringify(item));
    } catch (error) {
      console.warn('LocalStorage 写入失败:', error);
    }
  }

  private deleteFromLocalStorage(key: string): void {
    try {
      localStorage.removeItem(`cache_${key}`);
    } catch (error) {
      console.warn('LocalStorage 删除失败:', error);
    }
  }

  private clearLocalStorage(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith('cache_')) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.warn('LocalStorage 清空失败:', error);
    }
  }

  private async getFromIndexedDB<T>(key: string): Promise<CacheItem<T> | null> {
    try {
      const db = await this.openIndexedDB();
      const transaction = db.transaction(['cache'], 'readonly');
      const store = transaction.objectStore('cache');
      const request = store.get(key);

      return new Promise((resolve) => {
        request.onsuccess = () => {
          const item = request.result;
          if (item && !this.isExpired(item)) {
            resolve(item);
          } else {
            if (item) this.deleteFromIndexedDB(key);
            resolve(null);
          }
        };
        request.onerror = () => resolve(null);
      });
    } catch (error) {
      console.warn('IndexedDB 读取失败:', error);
      return null;
    }
  }

  private async setInIndexedDB<T>(key: string, data: T, ttl: number): Promise<void> {
    try {
      const db = await this.openIndexedDB();
      const transaction = db.transaction(['cache'], 'readwrite');
      const store = transaction.objectStore('cache');

      const item: CacheItem<T> = {
        key,
        data,
        timestamp: Date.now(),
        ttl,
        size: this.calculateSize(data),
        accessCount: 1,
        lastAccessed: Date.now()
      };

      store.put(item);
    } catch (error) {
      console.warn('IndexedDB 写入失败:', error);
    }
  }

  private async deleteFromIndexedDB(key: string): Promise<void> {
    try {
      const db = await this.openIndexedDB();
      const transaction = db.transaction(['cache'], 'readwrite');
      const store = transaction.objectStore('cache');
      store.delete(key);
    } catch (error) {
      console.warn('IndexedDB 删除失败:', error);
    }
  }

  private async clearIndexedDB(): Promise<void> {
    try {
      const db = await this.openIndexedDB();
      const transaction = db.transaction(['cache'], 'readwrite');
      const store = transaction.objectStore('cache');
      store.clear();
    } catch (error) {
      console.warn('IndexedDB 清空失败:', error);
    }
  }

  private openIndexedDB(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('dev-daily-cache', 1);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains('cache')) {
          db.createObjectStore('cache', { keyPath: 'key' });
        }
      };
    });
  }

  private isExpired(item: CacheItem<any>): boolean {
    return Date.now() - item.timestamp > item.ttl * 1000;
  }

  private calculateSize(data: any): number {
    return new Blob([JSON.stringify(data)]).size;
  }

  private evictMemoryCache(): void {
    // LRU 淘汰策略
    const items = Array.from(this.memoryCache.values());
    items.sort((a, b) => a.lastAccessed - b.lastAccessed);

    // 删除最少使用的项目，直到内存使用量降到限制以下
    while (this.stats.memoryUsage > this.config.maxMemorySize * 1024 * 1024 * 0.8 && items.length > 0) {
      const item = items.shift()!;
      this.memoryCache.delete(item.key);
      this.stats.memoryUsage -= item.size;
    }
  }

  private cleanup(): void {
    // 清理过期的内存缓存
    for (const [key, item] of this.memoryCache.entries()) {
      if (this.isExpired(item)) {
        this.memoryCache.delete(key);
        this.stats.memoryUsage -= item.size;
      }
    }
  }

  private updateStats(): void {
    this.stats.memoryUsage = Array.from(this.memoryCache.values())
      .reduce((total, item) => total + item.size, 0);
  }

  private updateHitRate(): void {
    this.stats.hitRate = this.stats.totalRequests > 0 
      ? this.stats.totalHits / this.stats.totalRequests 
      : 0;
  }

  private resetStats(): void {
    this.stats = {
      memoryUsage: 0,
      localStorageUsage: 0,
      indexedDBUsage: 0,
      hitRate: 0,
      totalRequests: 0,
      totalHits: 0
    };
  }
}

// 缓存装饰器
export function cached(ttl?: number) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    const cache = CacheService.getInstance();

    descriptor.value = async function (...args: any[]) {
      const cacheKey = `${target.constructor.name}_${propertyName}_${JSON.stringify(args)}`;
      
      // 尝试从缓存获取
      const cachedResult = await cache.get(cacheKey);
      if (cachedResult !== null) {
        return cachedResult;
      }

      // 执行原方法
      const result = await method.apply(this, args);
      
      // 缓存结果
      await cache.set(cacheKey, result, ttl);
      
      return result;
    };
  };
}
