<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📄 文档加载项目功能演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
        }
        .demo-section.highlight {
            border-color: #3b82f6;
            background: #eff6ff;
        }
        .file-upload {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            transition: all 0.3s;
        }
        .file-upload:hover {
            border-color: #3b82f6;
            background: #f8fafc;
        }
        .file-upload.dragover {
            border-color: #10b981;
            background: #ecfdf5;
        }
        .button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s;
        }
        .button:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }
        .button.orange {
            background: #ea580c;
        }
        .button.orange:hover {
            background: #c2410c;
        }
        .button.green {
            background: #10b981;
        }
        .button.green:hover {
            background: #059669;
        }
        .file-list {
            margin-top: 20px;
        }
        .file-item {
            display: flex;
            justify-content: between;
            align-items: center;
            padding: 12px;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            margin-bottom: 8px;
        }
        .project-preview {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: #10b981;
            transition: width 0.3s;
        }
        .tag {
            display: inline-block;
            padding: 4px 8px;
            background: #dbeafe;
            color: #1e40af;
            border-radius: 4px;
            font-size: 12px;
            margin: 2px;
        }
        .log {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .step {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: #f9fafb;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        .step-number {
            background: #3b82f6;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📄 dev-daily v2 文档加载项目功能演示</h1>
            <p>通过上传项目文档来加载和管理本地项目</p>
        </div>

        <!-- 功能说明 -->
        <div class="demo-section">
            <h2>🎯 功能特点</h2>
            <div class="step">
                <div class="step-number">1</div>
                <div>
                    <strong>标准化项目文档</strong><br>
                    每个项目在 <code>.dev-daily/</code> 文件夹中维护标准化文档
                </div>
            </div>
            <div class="step">
                <div class="step-number">2</div>
                <div>
                    <strong>文档驱动管理</strong><br>
                    通过上传项目文档来加载项目信息，无需直接访问文件系统
                </div>
            </div>
            <div class="step">
                <div class="step-number">3</div>
                <div>
                    <strong>实时数据同步</strong><br>
                    本地文档始终保持最新状态，支持手动同步到在线系统
                </div>
            </div>
        </div>

        <!-- 文档结构展示 -->
        <div class="demo-section">
            <h2>📁 标准项目文档结构</h2>
            <pre style="background: #f3f4f6; padding: 15px; border-radius: 6px; overflow-x: auto;">
project-root/
├── .dev-daily/
│   ├── project.md          # 项目基本信息
│   ├── progress.md         # 开发进度
│   ├── config.json         # 项目配置
│   ├── tasks.md           # 任务列表 (可选)
│   └── notes.md           # 开发笔记 (可选)
├── src/
└── README.md</pre>
        </div>

        <!-- 文件上传演示 -->
        <div class="demo-section highlight">
            <h2>📤 文档上传演示</h2>
            <p>拖拽或选择 Cloudflare 分析项目的文档文件：</p>
            
            <div class="file-upload" id="fileUpload">
                <div style="font-size: 48px; margin-bottom: 15px;">📄</div>
                <p><strong>拖拽文件到此处，或点击选择文件</strong></p>
                <p style="color: #6b7280; font-size: 14px;">
                    支持: project.md, progress.md, config.json
                </p>
                <input type="file" id="fileInput" multiple accept=".md,.json" style="display: none;">
            </div>

            <div class="file-list" id="fileList"></div>

            <div style="text-align: center; margin-top: 20px;">
                <button class="button orange" onclick="loadSampleProject()">
                    📋 加载示例项目
                </button>
                <button class="button green" onclick="processFiles()" id="processBtn" disabled>
                    🚀 处理项目文档
                </button>
            </div>
        </div>

        <!-- 项目预览 -->
        <div class="demo-section" id="projectPreview" style="display: none;">
            <h2>👀 项目信息预览</h2>
            <div class="project-preview" id="previewContent">
                <!-- 项目信息将在这里显示 -->
            </div>
        </div>

        <!-- 操作日志 -->
        <div class="demo-section">
            <h2>📋 操作日志</h2>
            <div class="log" id="log">
                [系统] 文档加载演示页面已就绪<br>
                [提示] 请上传项目文档文件或点击"加载示例项目"<br>
            </div>
        </div>
    </div>

    <script>
        let uploadedFiles = [];
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '[错误]' : type === 'success' ? '[成功]' : '[信息]';
            logDiv.innerHTML += `<span style="color: ${type === 'error' ? '#ef4444' : type === 'success' ? '#10b981' : '#3b82f6'}">${prefix}</span> [${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // 文件上传处理
        const fileUpload = document.getElementById('fileUpload');
        const fileInput = document.getElementById('fileInput');
        
        fileUpload.addEventListener('click', () => fileInput.click());
        fileUpload.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileUpload.classList.add('dragover');
        });
        fileUpload.addEventListener('dragleave', () => {
            fileUpload.classList.remove('dragover');
        });
        fileUpload.addEventListener('drop', (e) => {
            e.preventDefault();
            fileUpload.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });
        
        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });

        function handleFiles(files) {
            Array.from(files).forEach(file => {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const fileName = file.name.toLowerCase();
                    let fileType = 'other';
                    
                    if (fileName.includes('project')) fileType = 'project';
                    else if (fileName.includes('progress')) fileType = 'progress';
                    else if (fileName.includes('config')) fileType = 'config';
                    else if (fileName.includes('tasks')) fileType = 'tasks';
                    else if (fileName.includes('notes')) fileType = 'notes';

                    uploadedFiles.push({
                        name: file.name,
                        content: e.target.result,
                        type: fileType,
                        size: file.size
                    });

                    updateFileList();
                    log(`已上传文件: ${file.name} (${fileType})`);
                };
                reader.readAsText(file);
            });
        }

        function updateFileList() {
            const fileList = document.getElementById('fileList');
            const processBtn = document.getElementById('processBtn');
            
            if (uploadedFiles.length === 0) {
                fileList.innerHTML = '';
                processBtn.disabled = true;
                return;
            }

            fileList.innerHTML = '<h3>已上传文件:</h3>' + uploadedFiles.map((file, index) => `
                <div class="file-item">
                    <div>
                        <strong>${getFileIcon(file.type)} ${file.name}</strong>
                        <span style="color: #6b7280; margin-left: 10px;">(${file.type})</span>
                    </div>
                    <button onclick="removeFile(${index})" style="background: #ef4444; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer;">删除</button>
                </div>
            `).join('');
            
            processBtn.disabled = false;
        }

        function getFileIcon(type) {
            switch (type) {
                case 'project': return '📋';
                case 'progress': return '📈';
                case 'config': return '⚙️';
                case 'tasks': return '✅';
                case 'notes': return '📝';
                default: return '📄';
            }
        }

        function removeFile(index) {
            uploadedFiles.splice(index, 1);
            updateFileList();
            log(`已删除文件`);
        }

        function loadSampleProject() {
            uploadedFiles = [
                {
                    name: 'project.md',
                    type: 'project',
                    content: `# 📊 Cloudflare 用户行为分析系统

## 🎯 项目基本信息
- **项目名称**: Cloudflare 用户行为分析系统
- **项目类型**: 全栈 Web 应用 + Serverless
- **创建时间**: 2025-01-10
- **当前版本**: v1.0.0-alpha
- **开发状态**: 🚧 开发中

## 🏗️ 技术架构
### 核心技术栈
- **前端**: React + TypeScript + Tailwind CSS
- **后端**: Cloudflare Workers + Hono
- **数据库**: Cloudflare D1 (SQLite)`
                },
                {
                    name: 'progress.md',
                    type: 'progress',
                    content: `# 📈 项目开发进度

## 🎯 总体进度: 25%

### 📊 模块完成度
| 模块 | 进度 | 状态 |
|------|------|------|
| 环境与依赖 | 60% | 🚧 进行中 |
| 核心功能 | 15% | ⏳ 计划中 |
| 用户界面 | 0% | ⏳ 计划中 |`
                },
                {
                    name: 'config.json',
                    type: 'config',
                    content: JSON.stringify({
                        "project": {
                            "id": "cloudflare-user-analytics",
                            "name": "Cloudflare 用户行为分析系统",
                            "version": "1.0.0-alpha",
                            "type": "web-application",
                            "status": "development"
                        },
                        "progress": {
                            "overall": 25
                        },
                        "technology": {
                            "frontend": "React + TypeScript",
                            "backend": "Cloudflare Workers"
                        },
                        "quality": {
                            "healthScore": 85
                        }
                    }, null, 2)
                }
            ];
            
            updateFileList();
            log('已加载示例项目文档', 'success');
        }

        function processFiles() {
            if (uploadedFiles.length === 0) {
                log('请先上传文档文件', 'error');
                return;
            }

            log('开始处理项目文档...');
            
            // 解析配置文件
            const configFile = uploadedFiles.find(f => f.type === 'config');
            let config = {};
            
            if (configFile) {
                try {
                    config = JSON.parse(configFile.content);
                    log('配置文件解析成功');
                } catch (e) {
                    log('配置文件解析失败: ' + e.message, 'error');
                    return;
                }
            }

            // 显示项目预览
            showProjectPreview(config);
            log('项目信息解析完成', 'success');
            log('项目已成功加载到系统中', 'success');
        }

        function showProjectPreview(config) {
            const preview = document.getElementById('projectPreview');
            const content = document.getElementById('previewContent');
            
            const project = config.project || {};
            const progress = config.progress || {};
            const technology = config.technology || {};
            const quality = config.quality || {};
            
            content.innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h3>📋 项目信息</h3>
                        <p><strong>名称:</strong> ${project.name || '未知'}</p>
                        <p><strong>类型:</strong> ${project.type || '未知'}</p>
                        <p><strong>版本:</strong> ${project.version || '未知'}</p>
                        <p><strong>状态:</strong> ${project.status || '未知'}</p>
                        
                        <h3>🛠️ 技术栈</h3>
                        <div class="tag">${technology.frontend || 'Unknown'}</div>
                        <div class="tag">${technology.backend || 'Unknown'}</div>
                    </div>
                    <div>
                        <h3>📊 项目进度</h3>
                        <p>总体进度: <strong>${progress.overall || 0}%</strong></p>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${progress.overall || 0}%"></div>
                        </div>
                        
                        <h3>🏥 健康度</h3>
                        <p>项目健康度: <strong>${quality.healthScore || 50}%</strong></p>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${quality.healthScore || 50}%"></div>
                        </div>
                    </div>
                </div>
            `;
            
            preview.style.display = 'block';
        }

        // 初始化
        log('系统已就绪，可以开始上传文档');
    </script>
</body>
</html>
