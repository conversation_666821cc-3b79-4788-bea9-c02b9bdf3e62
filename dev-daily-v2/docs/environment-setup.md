# dev-daily v2 环境配置与依赖版本规范

## 📋 项目概览

dev-daily v2 是基于 Project Tree 架构的智能开发日志管理系统，采用现代化的技术栈和严格的版本管理策略。

## 🌍 A模块: 环境与依赖

### A1: Node.js 环境
- **版本要求**: Node.js >= 18.0.0 (推荐 18.19.0 LTS)
- **包管理器**: npm >= 9.0.0
- **验证命令**:
  ```bash
  node --version  # 应显示 v18.x.x
  npm --version   # 应显示 9.x.x+
  ```

### A2: TypeScript 配置
- **版本**: TypeScript 5.2.2
- **配置**: 启用严格模式
- **特性**: 
  - 严格类型检查
  - 路径映射支持
  - JSX 支持
- **配置文件**: `tsconfig.json`, `tsconfig.node.json`

### A3: React 生态系统
- **核心版本**:
  - React: ^18.2.0
  - React DOM: ^18.2.0
  - React Router DOM: ^6.20.1
- **类型定义**:
  - @types/react: ^18.2.43
  - @types/react-dom: ^18.2.17
- **特性**: 
  - 并发特性支持
  - 严格模式
  - 热重载

### A4: Vite 构建工具
- **版本**: Vite 5.0.8
- **插件**:
  - @vitejs/plugin-react: ^4.2.1
  - vite-tsconfig-paths (通过配置)
- **特性**:
  - 快速热重载
  - 代码分割
  - 资源优化

### A5: Cloudflare 部署
- **平台**: Cloudflare Pages
- **账户**: <EMAIL>
- **URL**: https://0407efeb.dev-daily-v2.pages.dev
- **配置文件**: `wrangler.toml`, `_headers`, `_redirects`

## 📦 核心依赖版本清单

### 生产依赖
```json
{
  "clsx": "^2.0.0",
  "date-fns": "^2.30.0",
  "fuse.js": "^7.0.0",
  "highlight.js": "^11.9.0",
  "lucide-react": "^0.294.0",
  "marked": "^11.1.1",
  "react": "^18.2.0",
  "react-beautiful-dnd": "^13.1.1",
  "react-dom": "^18.2.0",
  "react-hotkeys-hook": "^4.4.1",
  "react-markdown": "^9.0.1",
  "react-router-dom": "^6.20.1",
  "rehype-highlight": "^7.0.0",
  "remark-gfm": "^4.0.0",
  "zustand": "^4.4.7"
}
```

### 开发依赖
```json
{
  "@types/marked": "^6.0.0",
  "@types/react": "^18.2.43",
  "@types/react-beautiful-dnd": "^13.1.8",
  "@types/react-dom": "^18.2.17",
  "@typescript-eslint/eslint-plugin": "^6.14.0",
  "@typescript-eslint/parser": "^6.14.0",
  "@vitejs/plugin-react": "^4.2.1",
  "autoprefixer": "^10.4.21",
  "eslint": "^8.55.0",
  "eslint-plugin-react-hooks": "^4.6.0",
  "eslint-plugin-react-refresh": "^0.4.5",
  "postcss": "^8.5.6",
  "tailwindcss": "^3.4.17",
  "tsx": "^4.6.2",
  "typescript": "^5.2.2",
  "vite": "^5.0.8"
}
```

## 🚀 快速开始

### 1. 环境检查
```bash
# 检查 Node.js 版本
node --version

# 检查 npm 版本
npm --version

# 检查 TypeScript 版本
npx tsc --version
```

### 2. 项目安装
```bash
# 克隆项目
git clone <repository-url>
cd dev-daily-v2

# 安装依赖
npm install

# 类型检查
npm run type-check

# 启动开发服务器
npm run dev
```

### 3. 构建与部署
```bash
# 构建检查
npm run build-check

# 生产构建
npm run build

# 预览构建结果
npm run preview
```

## 🔧 开发工具配置

### VS Code 推荐扩展
- TypeScript and JavaScript Language Features
- ES7+ React/Redux/React-Native snippets
- Tailwind CSS IntelliSense
- ESLint
- Prettier

### 代码质量
- **ESLint**: 代码规范检查
- **TypeScript**: 静态类型检查
- **Prettier**: 代码格式化（推荐）

## 📊 项目结构

```
dev-daily-v2/
├── src/
│   ├── components/     # React 组件
│   ├── services/       # 业务逻辑服务
│   ├── types/          # TypeScript 类型定义
│   └── ...
├── docs/              # 项目文档
├── scripts/           # 工具脚本
├── public/            # 静态资源
└── dist/              # 构建输出
```

## 🔄 版本管理策略

### 语义化版本
- **主版本**: 不兼容的 API 修改
- **次版本**: 向下兼容的功能性新增
- **修订版本**: 向下兼容的问题修正

### 依赖更新策略
- **锁定主要版本**: 避免破坏性更改
- **定期更新**: 每月检查安全更新
- **测试验证**: 更新后完整测试

## 🛡️ 安全与性能

### 安全措施
- 定期依赖安全扫描
- 最小权限原则
- 环境变量管理

### 性能优化
- 代码分割
- 资源压缩
- 缓存策略
- 懒加载

## 📞 支持与维护

### 问题报告
- GitHub Issues
- 详细的错误描述
- 环境信息

### 贡献指南
- Fork 项目
- 创建特性分支
- 提交 Pull Request

---

**最后更新**: 2025-01-10
**维护者**: dev-daily team
**版本**: v2.0.0
