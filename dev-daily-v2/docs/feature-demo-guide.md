# dev-daily v2 功能演示指南

## 🚀 快速演示

### 前置条件
确保开发服务器正在运行：
```bash
cd dev-daily-v2
npm run dev
```
访问地址：http://localhost:5174

## 📖 1. 使用指南页面演示

### 访问路径
1. 打开 http://localhost:5174
2. 点击左侧导航栏的 "📖 使用指南"

### 演示要点
- **项目概览**: 展示dev-daily v2的目标和价值
- **快速开始**: 4步引导流程
- **核心功能**: 详细功能介绍
- **备份策略**: 自动备份机制说明
- **项目日历**: 进度追踪功能
- **最佳实践**: 使用建议

### 交互测试
- ✅ 点击各个章节标题展开/收起内容
- ✅ 查看不同功能模块的图标和描述
- ✅ 测试响应式布局（调整浏览器窗口大小）

## 🛡️ 2. 备份管理功能演示

### 访问路径
1. 点击左侧导航栏的 "🛡️ 备份管理"

### 演示功能

#### 2.1 创建手动备份
1. 点击右上角 "创建备份" 按钮
2. 观察备份创建过程
3. 查看新创建的备份出现在列表中

#### 2.2 备份策略设置
1. 点击右上角的 ⚙️ 设置按钮
2. 查看备份策略配置选项：
   - 节点完成时自动备份
   - 里程碑时自动备份
   - 最大备份数量
   - 启用压缩

#### 2.3 备份操作
- **导出备份**: 点击备份项的 📥 按钮
- **恢复备份**: 点击备份项的 🔄 按钮（会有确认提示）
- **删除备份**: 点击备份项的 🗑️ 按钮（会有确认提示）

#### 2.4 备份导入
1. 点击右上角的 📤 导入按钮
2. 选择之前导出的备份文件
3. 观察导入成功提示

### 演示数据
如果没有备份数据，可以运行：
```bash
npm run generate-test-data
```

## 📅 3. 项目日历功能演示

### 访问路径
1. 点击左侧导航栏的 "📅 项目日历"

### 演示功能

#### 3.1 日历视图
- **月度导航**: 使用 ← → 按钮切换月份
- **日期状态**: 观察不同颜色表示的完成状态
  - 🟢 绿色：完成率 ≥ 80%
  - 🔵 蓝色：完成率 60-80%
  - 🟡 黄色：完成率 30-60%
  - 🔴 红色：完成率 < 30%
  - ⚪ 灰色：无记录

#### 3.2 添加进度记录
1. 点击任意日期
2. 点击右侧 "添加进度记录" 按钮
3. 填写进度信息：
   - 已完成任务数
   - 总任务数
   - 工作时长
   - 遇到的问题
   - 完成的成就
   - 日志笔记

#### 3.3 查看进度详情
1. 点击有数据的日期
2. 在右侧面板查看详细信息
3. 观察任务完成情况、工时统计等

#### 3.4 列表视图
1. 点击右上角 "列表视图" 按钮
2. 查看当月所有进度记录的详细列表
3. 观察成就和问题的分类显示

### 测试数据生成
```bash
npm run generate-test-data
```
这将生成过去30天的模拟数据，包括：
- 11个节点完成记录
- 6个问题报告
- 4个里程碑记录
- 8条日常笔记

## 🔄 4. 自动备份流程演示

### 模拟节点完成
由于自动备份需要在项目树中完成节点，我们可以通过以下方式模拟：

1. 访问 "🌳 项目树" 页面
2. 找到一个未完成的节点
3. 将其状态改为 "已完成"
4. 观察是否弹出备份确认对话框

### 备份确认流程
1. 系统检测到节点完成
2. 弹出备份确认对话框
3. 用户选择是否创建备份
4. 如果确认，自动创建备份并记录

## 📊 5. 数据集成演示

### 项目日志集成
1. 在项目日历中添加进度记录
2. 访问浏览器开发者工具 → Application → Local Storage
3. 查看 `dev-daily-v2-project-logs` 和 `dev-daily-v2-daily-progress` 数据

### 备份数据查看
1. 创建备份后
2. 在 Local Storage 中查看 `dev-daily-v2-backups` 数据
3. 观察备份的完整性和元数据

## 🧪 6. 完整工作流程演示

### 场景：完成一个开发任务

1. **查看使用指南** → 了解系统功能
2. **项目树管理** → 创建或查看任务节点
3. **完成任务** → 标记节点为完成状态
4. **自动备份** → 系统提示创建备份
5. **记录进度** → 在项目日历中记录当日进度
6. **问题追踪** → 记录遇到的问题和解决方案
7. **备份管理** → 查看和管理历史备份

### 数据流转
```
任务完成 → 自动备份提示 → 项目日志记录 → 日历进度更新 → 数据持久化
```

## 🔍 7. 高级功能演示

### 备份策略自定义
1. 访问备份管理页面
2. 打开设置面板
3. 调整备份策略：
   - 关闭自动备份
   - 修改最大备份数量
   - 测试不同配置的效果

### 数据导出导入
1. **导出项目日志**：
   ```javascript
   // 在浏览器控制台执行
   ProjectLogService.exportLogs();
   ```

2. **导出备份数据**：
   - 在备份管理页面点击导出按钮
   - 下载JSON文件

3. **数据恢复测试**：
   - 清除本地数据
   - 重新导入备份
   - 验证数据完整性

## 🎯 8. 性能和稳定性测试

### 大量数据测试
1. 运行测试数据生成脚本多次
2. 观察系统性能表现
3. 测试备份清理功能

### 错误处理测试
1. 尝试导入无效的备份文件
2. 测试网络断开时的行为
3. 验证数据损坏时的恢复机制

## 📝 演示检查清单

### 使用指南页面 ✅
- [ ] 页面正常加载
- [ ] 所有章节可以展开/收起
- [ ] 内容显示完整
- [ ] 响应式布局正常

### 备份管理 ✅
- [ ] 可以创建手动备份
- [ ] 备份列表显示正常
- [ ] 导出功能工作
- [ ] 导入功能工作
- [ ] 设置可以保存

### 项目日历 ✅
- [ ] 日历视图正常显示
- [ ] 可以添加进度记录
- [ ] 列表视图切换正常
- [ ] 日期详情显示正确
- [ ] 数据持久化正常

### 数据集成 ✅
- [ ] 项目日志正确记录
- [ ] 备份数据完整
- [ ] 日历数据同步
- [ ] 本地存储正常

---

**演示准备时间**: 5分钟
**完整演示时间**: 15-20分钟
**建议演示顺序**: 使用指南 → 项目日历 → 备份管理 → 集成流程
