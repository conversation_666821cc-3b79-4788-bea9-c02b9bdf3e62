# 🔧 GitHub Actions 自动部署配置指南

## 🎯 问题诊断

经过检查发现，GitHub Actions配置文件正确，但**GitHub Secrets未正确配置**，导致自动部署失败。

## 📋 当前状态

- ✅ GitHub Actions配置文件正确
- ✅ Wrangler配置正确  
- ✅ 本地构建成功
- ✅ Cloudflare已登录
- ❌ **GitHub Secrets缺失** (主要问题)

## 🔐 必需的GitHub Secrets

需要在GitHub仓库中配置以下Secrets：

### 1. CLOUDFLARE_ACCOUNT_ID
```
值: 74031127ffb3b4595fbdaf41971011c2
```

### 2. CLOUDFLARE_API_TOKEN
需要创建专用的API Token (见下方步骤)

## 🚀 配置步骤

### 步骤1: 创建Cloudflare API Token

1. **访问Cloudflare API Token页面**
   ```
   https://dash.cloudflare.com/profile/api-tokens
   ```

2. **点击"Create Token"**

3. **选择"Custom token"**

4. **配置Token权限**
   ```yaml
   Token name: GitHub Actions - dev-daily-v2
   
   Permissions:
   - Account:Cloudflare Pages:Edit
   - Zone:Zone Settings:Read (可选)
   
   Account Resources:
   - Include: <EMAIL>'s Account
   
   Zone Resources:
   - Include: All zones (或选择特定域名)
   ```

5. **点击"Continue to summary"**

6. **点击"Create Token"**

7. **复制生成的Token** (只显示一次，请妥善保存)

### 步骤2: 配置GitHub Secrets

1. **访问GitHub仓库设置**
   ```
   https://github.com/justpm2099/dev-daily-v2/settings/secrets/actions
   ```

2. **添加第一个Secret**
   - 点击 "New repository secret"
   - Name: `CLOUDFLARE_ACCOUNT_ID`
   - Secret: `74031127ffb3b4595fbdaf41971011c2`
   - 点击 "Add secret"

3. **添加第二个Secret**
   - 点击 "New repository secret"
   - Name: `CLOUDFLARE_API_TOKEN`
   - Secret: `[刚才创建的API Token]`
   - 点击 "Add secret"

### 步骤3: 测试自动部署

配置完成后，有以下几种方式测试：

#### 方法1: 推送新的commit
```bash
# 创建一个空的commit来触发部署
git commit --allow-empty -m "test: trigger GitHub Actions deployment"
git push origin master
```

#### 方法2: 手动触发workflow
1. 访问 https://github.com/justpm2099/dev-daily-v2/actions
2. 选择 "Build and Deploy to Cloudflare Pages"
3. 点击 "Run workflow"
4. 选择 "master" 分支
5. 点击 "Run workflow"

#### 方法3: 修改文件触发
```bash
# 修改README或任何文件
echo "# Updated $(date)" >> README.md
git add README.md
git commit -m "docs: update README to trigger deployment"
git push origin master
```

## 🔍 验证部署成功

### 1. 检查GitHub Actions状态
访问: https://github.com/justpm2099/dev-daily-v2/actions

应该看到：
- ✅ 绿色的checkmark
- 🚀 "Build and Deploy to Cloudflare Pages" 成功

### 2. 检查Cloudflare部署
```bash
# 查看最新部署
wrangler pages deployment list --project-name=dev-daily-v2

# 应该看到最新的commit hash
```

### 3. 验证网站更新
访问: https://dev-daily-v2.pages.dev

检查是否包含最新的更改。

## 🛠️ 故障排除

### 问题1: API Token权限不足
**症状**: GitHub Actions失败，错误信息包含"unauthorized"

**解决方案**:
1. 重新创建API Token
2. 确保权限包含 "Account:Cloudflare Pages:Edit"
3. 更新GitHub Secret中的CLOUDFLARE_API_TOKEN

### 问题2: Account ID错误
**症状**: GitHub Actions失败，错误信息包含"account not found"

**解决方案**:
1. 运行 `wrangler whoami` 确认Account ID
2. 更新GitHub Secret中的CLOUDFLARE_ACCOUNT_ID

### 问题3: 项目名称不匹配
**症状**: 部署到错误的项目或创建新项目

**解决方案**:
1. 检查 `.github/workflows/deploy.yml` 中的 `projectName`
2. 检查 `wrangler.toml` 中的 `name`
3. 确保两者一致

### 问题4: 构建失败
**症状**: GitHub Actions在构建步骤失败

**解决方案**:
1. 本地运行 `npm run type-check`
2. 本地运行 `npm run build`
3. 修复任何TypeScript错误
4. 重新推送代码

## 📊 监控和维护

### 定期检查
```bash
# 检查部署状态
./scripts/monitor-deployment.sh

# 检查GitHub Actions配置
./scripts/test-github-actions.sh
```

### API Token维护
- API Token建议每6个月更新一次
- 如果Token泄露，立即撤销并创建新的
- 定期检查Token权限是否足够

## 🎯 完成后的工作流程

配置完成后，正常的开发流程：

1. **开发代码**
   ```bash
   # 正常开发
   git add .
   git commit -m "feat: add new feature"
   git push origin master
   ```

2. **自动部署**
   - GitHub Actions自动触发
   - 自动构建和部署到Cloudflare Pages
   - 约2-3分钟后部署完成

3. **验证部署**
   - 访问 https://dev-daily-v2.pages.dev
   - 检查新功能是否正常

## 📞 获取帮助

如果遇到问题：

1. **检查GitHub Actions日志**
   https://github.com/justpm2099/dev-daily-v2/actions

2. **运行诊断脚本**
   ```bash
   ./scripts/test-github-actions.sh
   ```

3. **手动部署测试**
   ```bash
   ./scripts/deploy.sh
   ```

4. **查看Cloudflare Pages控制台**
   https://dash.cloudflare.com/pages

---

**配置完成后，GitHub Actions将自动处理所有部署，无需手动干预！** 🎉
