# 🚀 部署指南

本项目使用 GitHub Actions 自动部署到 Cloudflare Pages，实现代码备份与部署的同步执行。

## 📋 部署配置

### 1. GitHub Secrets 配置

在 GitHub 仓库的 Settings → Secrets and variables → Actions 中添加以下 secrets：

```
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token
CLOUDFLARE_ACCOUNT_ID=74031127ffb3b4595fbdaf41971011c2
```

### 2. Cloudflare API Token 获取

1. 访问 [Cloudflare Dashboard](https://dash.cloudflare.com/profile/api-tokens)
2. 点击 "Create Token"
3. 使用 "Custom token" 模板
4. 配置权限：
   - **Account**: `Cloudflare Pages:Edit`
   - **Zone**: `Zone:Read` (如果使用自定义域名)
5. 复制生成的 Token

### 3. 账号 ID 获取

- **当前账号 ID**: `74031127ffb3b4595fbdaf41971011c2`
- 或访问 Cloudflare Dashboard 右侧边栏查看

## 🔄 自动化部署流程

### 分支策略

- **`master`/`main`** → 生产环境部署
- **`develop`** → 预览环境部署  
- **Pull Requests** → PR 预览部署

### 部署触发条件

```yaml
on:
  push:
    branches: [ master, main, develop ]
  pull_request:
    branches: [ master, main ]
```

### 部署步骤

1. **代码检查**
   - TypeScript 类型检查
   - ESLint 代码质量检查
   - 构建测试

2. **构建应用**
   - 安装依赖 (`npm ci`)
   - 构建生产版本 (`npm run build`)
   - 上传构建产物

3. **部署到 Cloudflare Pages**
   - 生产环境: `dev-daily-v2.pages.dev`
   - 预览环境: `dev-daily-v2-preview.pages.dev`
   - PR 预览: 动态生成预览 URL

## 🌐 部署地址

### 生产环境
- **主域名**: https://dev-daily-v2.pages.dev
- **自定义域名**: (待配置)

### 预览环境
- **预览域名**: https://dev-daily-v2-preview.pages.dev

### PR 预览
- 每个 PR 会自动生成独立的预览 URL
- 在 PR 评论中会自动显示预览链接

## 📊 部署状态监控

### GitHub Actions 状态

查看部署状态：
- 访问 GitHub 仓库的 Actions 标签页
- 查看最新的 workflow 运行状态
- 点击具体的 workflow 查看详细日志

### Cloudflare Pages 控制台

查看部署详情：
- 访问 [Cloudflare Pages Dashboard](https://dash.cloudflare.com/pages)
- 选择 `dev-daily-v2` 项目
- 查看部署历史和状态

## 🔧 本地部署测试

### 开发环境
```bash
npm install
npm run dev
```

### 生产构建测试
```bash
npm run build
npm run preview
```

### Cloudflare Pages 本地测试
```bash
npm run build
wrangler pages dev dist
```

## 🚨 故障排除

### 常见问题

1. **部署失败 - API Token 无效**
   - 检查 `CLOUDFLARE_API_TOKEN` 是否正确
   - 确认 Token 权限包含 `Cloudflare Pages:Edit`

2. **部署失败 - 账号 ID 错误**
   - 检查 `CLOUDFLARE_ACCOUNT_ID` 是否正确
   - 当前正确的账号 ID: `74031127ffb3b4595fbdaf41971011c2`

3. **构建失败 - TypeScript 错误**
   - 本地运行 `npm run type-check` 检查类型错误
   - 修复所有 TypeScript 类型问题后重新推送

4. **构建失败 - 依赖问题**
   - 删除 `node_modules` 和 `package-lock.json`
   - 重新运行 `npm install`
   - 确保 `package.json` 中的依赖版本正确

### 调试步骤

1. **查看 GitHub Actions 日志**
   ```
   GitHub 仓库 → Actions → 选择失败的 workflow → 查看详细日志
   ```

2. **查看 Cloudflare Pages 日志**
   ```
   Cloudflare Dashboard → Pages → dev-daily-v2 → 查看部署日志
   ```

3. **本地复现问题**
   ```bash
   # 清理环境
   rm -rf node_modules package-lock.json
   
   # 重新安装
   npm install
   
   # 类型检查
   npm run type-check
   
   # 构建测试
   npm run build
   ```

## 📈 性能优化

### 构建优化

- 使用 `npm ci` 而不是 `npm install` 提高安装速度
- 启用 Node.js 缓存加速依赖安装
- 并行执行类型检查和构建步骤

### 部署优化

- 只在必要时触发部署（推送到指定分支）
- 使用 Cloudflare Pages 的全球 CDN 加速
- 启用 Brotli 压缩减少传输大小

## 🔐 安全考虑

### Secrets 管理

- 所有敏感信息存储在 GitHub Secrets 中
- API Token 具有最小必要权限
- 定期轮换 API Token

### 访问控制

- 只有仓库管理员可以修改 Secrets
- 部署权限通过 GitHub 分支保护规则控制
- Cloudflare Pages 项目访问权限管理

## 📝 更新部署配置

如需修改部署配置：

1. 编辑 `.github/workflows/ci-cd.yml`
2. 提交并推送到 `master` 分支
3. GitHub Actions 会自动使用新配置进行部署

## 🎯 下一步优化

- [ ] 配置自定义域名
- [ ] 启用 Cloudflare Analytics
- [ ] 配置 Web Vitals 监控
- [ ] 设置部署通知（Slack/Discord）
- [ ] 添加自动化测试流程
