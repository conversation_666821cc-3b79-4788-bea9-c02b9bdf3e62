# 🚀 Cloudflare R2 + D1 文档同步存储配置指南

## 📋 概述

本指南将帮助您配置Cloudflare R2对象存储和D1数据库，用于dev-daily v2的文档同步和版本管理功能。

## 🏗️ 架构说明

```mermaid
graph TD
    A[GitHub Repository] --> B[Document Sync Service]
    B --> C[Cloudflare R2]
    B --> D[Cloudflare D1]
    C --> E[文档内容存储]
    D --> F[元数据管理]
    F --> G[版本历史]
    F --> H[同步日志]
```

### 存储分工
- **R2对象存储**: 存储文档内容、版本文件、资源文件
- **D1数据库**: 存储元数据、版本历史、同步日志、配置信息

## 🔧 Cloudflare R2 配置

### 1. 创建R2存储桶

```bash
# 安装Wrangler CLI
npm install -g wrangler

# 登录Cloudflare
wrangler login

# 创建R2存储桶
wrangler r2 bucket create dev-daily-documents
wrangler r2 bucket create dev-daily-backups
wrangler r2 bucket create dev-daily-assets
```

### 2. 配置存储桶结构

```
dev-daily-documents/
├── documents/
│   ├── {projectId}/
│   │   ├── {documentId}/
│   │   │   ├── versions/
│   │   │   │   ├── v2025-01-20T10-30-00-000Z.md
│   │   │   │   └── v2025-01-21T14-15-30-000Z.md
│   │   │   └── current.md
│   │   └── assets/
│   │       ├── images/
│   │       └── files/
│   └── metadata/
│       └── {projectId}.json

dev-daily-backups/
├── daily/
├── weekly/
└── manual/

dev-daily-assets/
├── images/
├── documents/
└── exports/
```

### 3. 设置访问权限

```bash
# 创建API Token (在Cloudflare Dashboard中)
# 权限: Account:Cloudflare R2:Edit
# 资源: Include - All accounts - All R2 buckets
```

### 4. 环境变量配置

```bash
# .env.local
R2_ACCOUNT_ID=your_account_id
R2_ACCESS_KEY_ID=your_access_key_id
R2_SECRET_ACCESS_KEY=your_secret_access_key
R2_ENDPOINT=https://your_account_id.r2.cloudflarestorage.com
R2_BUCKET_DOCUMENTS=dev-daily-documents
R2_BUCKET_BACKUPS=dev-daily-backups
R2_BUCKET_ASSETS=dev-daily-assets
```

## 🗄️ Cloudflare D1 配置

### 1. 创建D1数据库

```bash
# 创建数据库
wrangler d1 create dev-daily-sync

# 输出示例:
# ✅ Successfully created DB 'dev-daily-sync' in region APAC
# Created your database using D1's new storage backend.
# 
# [[d1_databases]]
# binding = "DB" # i.e. available in your Worker on env.DB
# database_name = "dev-daily-sync"
# database_id = "your-database-id"
```

### 2. 数据库表结构

```sql
-- 项目配置表
CREATE TABLE projects (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  github_url TEXT NOT NULL,
  github_token TEXT,
  sync_enabled BOOLEAN DEFAULT true,
  sync_strategy TEXT DEFAULT 'incremental',
  last_sync_at DATETIME,
  last_commit_sha TEXT,
  document_count INTEGER DEFAULT 0,
  total_versions INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 文档元数据表
CREATE TABLE documents (
  id TEXT PRIMARY KEY,
  project_id TEXT NOT NULL,
  file_path TEXT NOT NULL,
  title TEXT,
  content_hash TEXT,
  current_version TEXT,
  total_versions INTEGER DEFAULT 1,
  file_size INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- 版本历史表
CREATE TABLE document_versions (
  id TEXT PRIMARY KEY,
  document_id TEXT NOT NULL,
  version TEXT NOT NULL,
  content_hash TEXT NOT NULL,
  r2_key TEXT NOT NULL,
  commit_sha TEXT,
  author TEXT,
  change_type TEXT CHECK(change_type IN ('created', 'updated', 'deleted')),
  lines_added INTEGER DEFAULT 0,
  lines_removed INTEGER DEFAULT 0,
  lines_modified INTEGER DEFAULT 0,
  file_size INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE
);

-- 同步日志表
CREATE TABLE sync_logs (
  id TEXT PRIMARY KEY,
  project_id TEXT NOT NULL,
  status TEXT CHECK(status IN ('running', 'success', 'error', 'partial')) DEFAULT 'running',
  sync_type TEXT CHECK(sync_type IN ('manual', 'scheduled', 'webhook')) DEFAULT 'manual',
  documents_discovered INTEGER DEFAULT 0,
  documents_processed INTEGER DEFAULT 0,
  versions_created INTEGER DEFAULT 0,
  errors_count INTEGER DEFAULT 0,
  error_message TEXT,
  sync_duration INTEGER DEFAULT 0,
  started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  completed_at DATETIME,
  FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- 同步配置表
CREATE TABLE sync_configs (
  id TEXT PRIMARY KEY,
  project_id TEXT NOT NULL,
  config_key TEXT NOT NULL,
  config_value TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
  UNIQUE(project_id, config_key)
);

-- 创建索引
CREATE INDEX idx_documents_project_id ON documents(project_id);
CREATE INDEX idx_documents_file_path ON documents(file_path);
CREATE INDEX idx_document_versions_document_id ON document_versions(document_id);
CREATE INDEX idx_document_versions_created_at ON document_versions(created_at);
CREATE INDEX idx_sync_logs_project_id ON sync_logs(project_id);
CREATE INDEX idx_sync_logs_started_at ON sync_logs(started_at);
```

### 3. 初始化数据库

```bash
# 创建schema.sql文件，包含上述SQL语句
# 然后执行：
wrangler d1 execute dev-daily-sync --file=./schema.sql
```

### 4. wrangler.toml 配置

```toml
name = "dev-daily-sync"
main = "src/worker.js"
compatibility_date = "2023-12-01"

[[d1_databases]]
binding = "DB"
database_name = "dev-daily-sync"
database_id = "your-database-id"

[[r2_buckets]]
binding = "DOCUMENTS_BUCKET"
bucket_name = "dev-daily-documents"

[[r2_buckets]]
binding = "BACKUPS_BUCKET"
bucket_name = "dev-daily-backups"

[[r2_buckets]]
binding = "ASSETS_BUCKET"
bucket_name = "dev-daily-assets"

[vars]
ENVIRONMENT = "production"
```

## 🔐 安全配置

### 1. API Token权限

创建专用的API Token，最小权限原则：

```yaml
permissions:
  - Account:Cloudflare R2:Edit
  - Account:Cloudflare D1:Edit
  - Zone:Zone Settings:Read (如果使用自定义域名)

resources:
  - Include: All accounts
  - Include: Specific R2 buckets
  - Include: Specific D1 databases
```

### 2. 访问控制

```javascript
// Worker中的访问控制示例
export default {
  async fetch(request, env) {
    // 验证API Key
    const apiKey = request.headers.get('X-API-Key');
    if (!apiKey || !await validateApiKey(apiKey)) {
      return new Response('Unauthorized', { status: 401 });
    }
    
    // 处理请求
    return handleRequest(request, env);
  }
};
```

## 💰 成本估算

### R2存储成本 (月度)
- 存储: $0.015/GB
- Class A操作: $4.50/百万次
- Class B操作: $0.36/百万次

### D1数据库成本 (月度)
- 免费额度: 5GB存储 + 25M行读取
- 付费: $0.75/GB存储

### 预估成本 (100个项目，1000个文档)
- R2存储 (5GB): ~$0.08
- D1数据库: 免费额度内
- **总计**: < $0.10/月

## 🚀 部署步骤

### 1. 准备工作
```bash
# 克隆项目
git clone your-repo
cd dev-daily-v2

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env.local
# 编辑 .env.local 填入配置
```

### 2. 创建云资源
```bash
# 创建R2存储桶
wrangler r2 bucket create dev-daily-documents
wrangler r2 bucket create dev-daily-backups
wrangler r2 bucket create dev-daily-assets

# 创建D1数据库
wrangler d1 create dev-daily-sync

# 初始化数据库表
wrangler d1 execute dev-daily-sync --file=./schema.sql
```

### 3. 部署应用
```bash
# 构建项目
npm run build

# 部署到Cloudflare Pages
wrangler pages deploy dist
```

### 4. 测试验证
```bash
# 测试R2连接
curl -X GET "https://your-worker.your-subdomain.workers.dev/api/test/r2"

# 测试D1连接
curl -X GET "https://your-worker.your-subdomain.workers.dev/api/test/d1"
```

## 🔧 故障排除

### 常见问题

1. **R2访问被拒绝**
   - 检查API Token权限
   - 确认存储桶名称正确
   - 验证Account ID

2. **D1连接失败**
   - 检查database_id是否正确
   - 确认wrangler.toml配置
   - 验证表结构是否创建成功

3. **同步失败**
   - 检查GitHub Token权限
   - 确认网络连接
   - 查看错误日志

### 调试命令

```bash
# 查看R2存储桶列表
wrangler r2 bucket list

# 查看D1数据库列表
wrangler d1 list

# 查看Worker日志
wrangler tail your-worker-name

# 测试D1查询
wrangler d1 execute dev-daily-sync --command="SELECT * FROM projects LIMIT 5"
```

## 📚 相关文档

- [Cloudflare R2 文档](https://developers.cloudflare.com/r2/)
- [Cloudflare D1 文档](https://developers.cloudflare.com/d1/)
- [Wrangler CLI 文档](https://developers.cloudflare.com/workers/wrangler/)
- [GitHub API 文档](https://docs.github.com/en/rest)

---

配置完成后，您就可以使用dev-daily v2的文档同步功能，实现GitHub项目文档的自动同步和版本管理了！
