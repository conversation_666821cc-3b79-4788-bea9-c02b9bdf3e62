# 📚 文档同步与版本管理架构设计

## 🎯 核心目标
专注于GitHub项目的Markdown文档同步，提供版本管理和历史追踪功能，不涉及代码管理。

## 🏗️ 技术架构

### 1. 存储方案：Cloudflare R2 + D1
```yaml
storage_architecture:
  r2_buckets:
    documents: "存储文档内容和版本"
    backups: "存储备份数据"
    assets: "存储文档中的图片等资源"
  
  d1_database:
    document_metadata: "文档元数据表"
    version_history: "版本历史表"
    sync_logs: "同步日志表"
    project_configs: "项目配置表"
```

### 2. 文档同步流程
```mermaid
graph TD
    A[GitHub Repository] --> B[GitHub API]
    B --> C[文档发现服务]
    C --> D[内容提取]
    D --> E[版本比较]
    E --> F{是否有变更?}
    F -->|是| G[创建新版本]
    F -->|否| H[跳过同步]
    G --> I[上传到R2]
    I --> J[更新D1元数据]
    J --> K[同步完成]
```

### 3. 版本管理策略
```typescript
interface DocumentVersion {
  id: string;
  documentId: string;
  version: string;
  content: string;
  contentHash: string;
  createdAt: string;
  author: string;
  commitSha?: string;
  changeType: 'created' | 'updated' | 'deleted';
  changes: {
    added: number;
    removed: number;
    modified: number;
  };
}
```

## 🔧 核心服务设计

### 1. GitHub文档发现服务
```typescript
export class GitHubDocumentDiscovery {
  /**
   * 发现仓库中的所有Markdown文档
   */
  static async discoverDocuments(
    repoUrl: string, 
    token: string
  ): Promise<GitHubDocument[]> {
    // 1. 获取仓库树结构
    // 2. 过滤.md文件
    // 3. 获取文件内容
    // 4. 提取元数据
  }

  /**
   * 检测文档变更
   */
  static async detectChanges(
    repoUrl: string,
    lastSyncCommit: string
  ): Promise<DocumentChange[]> {
    // 1. 获取commits差异
    // 2. 过滤文档相关变更
    // 3. 分析变更类型
  }
}
```

### 2. 文档版本管理服务
```typescript
export class DocumentVersionService {
  /**
   * 创建文档版本
   */
  static async createVersion(
    document: GitHubDocument,
    previousVersion?: DocumentVersion
  ): Promise<DocumentVersion> {
    // 1. 计算内容哈希
    // 2. 比较变更
    // 3. 生成版本号
    // 4. 存储到R2
    // 5. 更新D1元数据
  }

  /**
   * 获取版本历史
   */
  static async getVersionHistory(
    documentId: string
  ): Promise<DocumentVersion[]> {
    // 从D1查询版本历史
  }

  /**
   * 版本比较
   */
  static async compareVersions(
    versionA: string,
    versionB: string
  ): Promise<VersionDiff> {
    // 1. 从R2获取内容
    // 2. 执行diff算法
    // 3. 返回差异结果
  }
}
```

### 3. Cloudflare R2存储服务
```typescript
export class R2DocumentStorage {
  /**
   * 存储文档版本
   */
  static async storeDocumentVersion(
    documentId: string,
    version: string,
    content: string
  ): Promise<string> {
    const key = `documents/${documentId}/versions/${version}.md`;
    // 上传到R2
  }

  /**
   * 获取文档内容
   */
  static async getDocumentContent(
    documentId: string,
    version?: string
  ): Promise<string> {
    // 从R2获取指定版本内容
  }

  /**
   * 存储文档资源
   */
  static async storeAsset(
    documentId: string,
    assetPath: string,
    content: ArrayBuffer
  ): Promise<string> {
    // 存储图片等资源文件
  }
}
```

## 📊 数据库设计 (Cloudflare D1)

### 1. 项目配置表
```sql
CREATE TABLE projects (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  github_url TEXT NOT NULL,
  github_token TEXT,
  sync_enabled BOOLEAN DEFAULT true,
  last_sync_at DATETIME,
  last_commit_sha TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 2. 文档元数据表
```sql
CREATE TABLE documents (
  id TEXT PRIMARY KEY,
  project_id TEXT NOT NULL,
  file_path TEXT NOT NULL,
  title TEXT,
  current_version TEXT,
  total_versions INTEGER DEFAULT 1,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (project_id) REFERENCES projects(id)
);
```

### 3. 版本历史表
```sql
CREATE TABLE document_versions (
  id TEXT PRIMARY KEY,
  document_id TEXT NOT NULL,
  version TEXT NOT NULL,
  content_hash TEXT NOT NULL,
  r2_key TEXT NOT NULL,
  commit_sha TEXT,
  author TEXT,
  change_type TEXT CHECK(change_type IN ('created', 'updated', 'deleted')),
  lines_added INTEGER DEFAULT 0,
  lines_removed INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (document_id) REFERENCES documents(id)
);
```

### 4. 同步日志表
```sql
CREATE TABLE sync_logs (
  id TEXT PRIMARY KEY,
  project_id TEXT NOT NULL,
  status TEXT CHECK(status IN ('success', 'error', 'partial')),
  documents_synced INTEGER DEFAULT 0,
  errors_count INTEGER DEFAULT 0,
  error_message TEXT,
  started_at DATETIME,
  completed_at DATETIME,
  FOREIGN KEY (project_id) REFERENCES projects(id)
);
```

## 🔄 同步策略

### 1. 增量同步
```typescript
interface SyncStrategy {
  type: 'incremental' | 'full';
  schedule: 'manual' | 'hourly' | 'daily';
  conflictResolution: 'remote_wins' | 'local_wins' | 'manual';
  batchSize: number;
}
```

### 2. 冲突处理
```typescript
interface ConflictResolution {
  strategy: 'auto' | 'manual';
  rules: {
    remoteNewer: boolean;
    localModified: boolean;
    preserveLocal: boolean;
  };
}
```

## 🎨 用户界面设计

### 1. 文档同步面板
- 项目列表和同步状态
- 手动同步按钮
- 同步历史和日志
- 错误处理和重试

### 2. 版本管理界面
- 文档版本历史
- 版本比较工具
- 版本回滚功能
- 变更统计图表

### 3. 配置管理
- GitHub仓库配置
- 同步策略设置
- 存储配额监控
- 备份恢复选项

## 💰 成本估算 (Cloudflare)

### R2 存储成本
- 存储: $0.015/GB/月
- 请求: $0.36/百万次请求
- 出站流量: $0.09/GB

### D1 数据库成本
- 免费额度: 5GB存储 + 25M行读取/月
- 付费: $0.75/GB存储/月

### 预估月成本 (100个项目)
- R2存储 (10GB): ~$0.15
- D1数据库: 免费额度内
- **总计**: < $1/月

## 🚀 实施计划

### Phase 1: 基础架构 (1周)
- [ ] 设置Cloudflare R2和D1
- [ ] 实现基础存储服务
- [ ] 创建数据库表结构

### Phase 2: 文档同步 (2周)
- [ ] GitHub API集成
- [ ] 文档发现和提取
- [ ] 增量同步逻辑

### Phase 3: 版本管理 (1周)
- [ ] 版本创建和存储
- [ ] 版本比较算法
- [ ] 历史查询接口

### Phase 4: 用户界面 (1周)
- [ ] 同步管理界面
- [ ] 版本历史展示
- [ ] 配置管理面板

这个方案专注于文档管理，避免了代码管理的复杂性，同时利用Cloudflare的全球CDN和边缘计算能力，提供高性能的文档同步和版本管理服务。
