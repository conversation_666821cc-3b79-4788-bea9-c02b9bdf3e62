name: CI/CD Pipeline

on:
  push:
    branches: [ master, main, develop ]
  pull_request:
    branches: [ master, main ]

env:
  NODE_VERSION: '18'

jobs:
  # 代码质量检查
  quality-check:
    name: Code Quality Check
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: TypeScript type check
      run: npm run type-check
      
    - name: ESLint check
      run: npm run lint
      continue-on-error: true
      
    - name: Build test
      run: npm run build

  # 构建和部署
  build-and-deploy:
    name: Build and Deploy
    runs-on: ubuntu-latest
    needs: quality-check
    if: github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: npm run build
      
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-files
        path: dist/
        retention-days: 30
        
    # 部署到生产环境 (master/main 分支)
    - name: Deploy to Production
      if: github.ref == 'refs/heads/master' || github.ref == 'refs/heads/main'
      uses: cloudflare/pages-action@v1
      with:
        apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
        accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
        projectName: dev-daily-v2
        directory: dist
        
    # 部署到预览环境 (develop 分支)
    - name: Deploy to Preview
      if: github.ref == 'refs/heads/develop'
      uses: cloudflare/pages-action@v1
      with:
        apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
        accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
        projectName: dev-daily-v2-preview
        directory: dist

  # PR 预览部署
  preview-deploy:
    name: Preview Deploy
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: npm run build
      
    - name: Deploy PR Preview
      uses: cloudflare/pages-action@v1
      id: deploy
      with:
        apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
        accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
        projectName: dev-daily-v2
        directory: dist
        
    - name: Comment PR with preview URL
      uses: actions/github-script@v7
      with:
        script: |
          const deploymentUrl = '${{ steps.deploy.outputs.url }}';
          const comment = `🚀 **Preview Deployment Ready!**
          
          📱 **Preview URL**: ${deploymentUrl}
          
          ✅ **Build Status**: Success
          🔍 **Commit**: ${context.sha.substring(0, 7)}
          📝 **Branch**: ${context.payload.pull_request.head.ref}
          
          ---
          
          ### 🧪 Test the following features:
          - [ ] GitHub 多账号管理
          - [ ] 项目发现功能  
          - [ ] 在线平台集成
          - [ ] 性能监控
          - [ ] 离线支持
          
          *This preview will be automatically updated when you push new commits.*`;
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: comment
          });

  # 部署状态通知
  notify-deployment:
    name: Deployment Notification
    runs-on: ubuntu-latest
    needs: [build-and-deploy]
    if: always() && github.event_name == 'push'
    
    steps:
    - name: Deployment Success Notification
      if: needs.build-and-deploy.result == 'success'
      run: |
        echo "🎉 Deployment successful!"
        echo "✅ Branch: ${{ github.ref_name }}"
        echo "📝 Commit: ${{ github.sha }}"
        echo "🌐 Environment: ${{ github.ref == 'refs/heads/master' && 'Production' || github.ref == 'refs/heads/main' && 'Production' || 'Preview' }}"
        
    - name: Deployment Failure Notification
      if: needs.build-and-deploy.result == 'failure'
      run: |
        echo "❌ Deployment failed!"
        echo "🔍 Please check the logs for details"
        exit 1
