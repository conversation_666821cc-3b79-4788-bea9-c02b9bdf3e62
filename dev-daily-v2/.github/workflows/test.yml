name: Test GitHub Actions

on:
  push:
    branches: [ master, main ]
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      
    - name: Test GitHub Actions
      run: |
        echo "🚀 GitHub Actions is working!"
        echo "Repository: ${{ github.repository }}"
        echo "Branch: ${{ github.ref_name }}"
        echo "Commit: ${{ github.sha }}"
        
    - name: Test Secrets
      run: |
        echo "Testing secrets availability..."
        if [ -n "${{ secrets.CLOUDFLARE_ACCOUNT_ID }}" ]; then
          echo "✅ CLOUDFLARE_ACCOUNT_ID is set"
        else
          echo "❌ CLOUDFLARE_ACCOUNT_ID is missing"
        fi
        
        if [ -n "${{ secrets.CLOUDFLARE_API_TOKEN }}" ]; then
          echo "✅ CLOUDFLARE_API_TOKEN is set"
        else
          echo "❌ CLOUDFLARE_API_TOKEN is missing"
        fi
