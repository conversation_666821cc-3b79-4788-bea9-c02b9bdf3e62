import fs from 'fs';
import path from 'path';

// 修复类型导入路径
function fixImports(dir, rootDir = './src') {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      fixImports(filePath, rootDir);
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      let content = fs.readFileSync(filePath, 'utf8');

      // 计算相对于src的路径
      const relativePath = path.relative(rootDir, dir);
      const depth = relativePath ? relativePath.split(path.sep).length : 0;
      const prefix = depth > 0 ? '../'.repeat(depth) : './';

      // 修复类型导入
      content = content.replace(
        /from ['"]@types\/index['"]/g,
        `from '${prefix}types/index'`
      );

      // 修复服务导入
      content = content.replace(
        /from ['"]@services\/([^'"]+)['"]/g,
        `from '${prefix}services/$1'`
      );

      // 修复组件导入
      content = content.replace(
        /from ['"]@components\/([^'"]+)['"]/g,
        `from '${prefix}components/$1'`
      );

      // 修复已经被错误修复的路径
      content = content.replace(
        /from ['"]\.\.\/types\/index['"]/g,
        `from '${prefix}types/index'`
      );

      content = content.replace(
        /from ['"]\.\.\/services\/([^'"]+)['"]/g,
        `from '${prefix}services/$1'`
      );

      content = content.replace(
        /from ['"]\.\.\/components\/([^'"]+)['"]/g,
        `from '${prefix}components/$1'`
      );

      fs.writeFileSync(filePath, content);
      console.log(`Fixed imports in: ${filePath} (depth: ${depth})`);
    }
  });
}

// 修复src目录下的所有文件
fixImports('./src');
console.log('Import fixes completed!');
