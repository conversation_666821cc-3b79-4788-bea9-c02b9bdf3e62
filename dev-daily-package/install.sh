#!/bin/bash

# Dev-Daily 安装脚本
# 版本: v1.0
# 作者: Augment Agent

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_success() {
    print_message $GREEN "✅ $1"
}

print_warning() {
    print_message $YELLOW "⚠️  $1"
}

print_error() {
    print_message $RED "❌ $1"
}

print_info() {
    print_message $BLUE "ℹ️  $1"
}

# 检查是否在项目根目录
check_project_root() {
    if [[ ! -f "package.json" && ! -f "requirements.txt" && ! -f "Cargo.toml" && ! -f "go.mod" ]]; then
        print_warning "当前目录似乎不是项目根目录"
        print_info "请确保在项目根目录运行此脚本"
        read -p "是否继续安装？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "安装已取消"
            exit 1
        fi
    fi
}

# 检查dev-daily目录是否已存在
check_existing_installation() {
    if [[ -d "dev-daily" ]]; then
        print_warning "dev-daily目录已存在"
        print_info "发现现有的dev-daily安装"
        
        echo "请选择操作："
        echo "1) 备份现有目录并重新安装"
        echo "2) 合并安装（保留现有文件）"
        echo "3) 取消安装"
        read -p "请输入选择 (1-3): " choice
        
        case $choice in
            1)
                backup_dir="dev-daily-backup-$(date +%Y%m%d-%H%M%S)"
                print_info "备份现有目录到: $backup_dir"
                mv dev-daily "$backup_dir"
                print_success "备份完成"
                ;;
            2)
                print_info "将合并安装到现有目录"
                MERGE_MODE=true
                ;;
            3)
                print_info "安装已取消"
                exit 0
                ;;
            *)
                print_error "无效选择，安装已取消"
                exit 1
                ;;
        esac
    fi
}

# 复制dev-daily目录
install_dev_daily() {
    print_info "开始安装dev-daily系统..."
    
    if [[ "$MERGE_MODE" == "true" ]]; then
        # 合并模式：只复制不存在的文件
        print_info "合并模式：复制新文件，保留现有文件"
        
        # 复制模板文件（如果不存在）
        if [[ ! -d "dev-daily/templates" ]]; then
            mkdir -p dev-daily/templates
            cp -r dev-daily/templates/* dev-daily/templates/
            print_success "模板文件已安装"
        else
            print_info "模板目录已存在，跳过"
        fi
        
        # 复制示例文件（如果不存在）
        if [[ ! -d "dev-daily/examples" ]]; then
            cp -r dev-daily/examples dev-daily/
            print_success "示例文件已安装"
        else
            print_info "示例目录已存在，跳过"
        fi
        
        # 检查关键文件
        if [[ ! -f "dev-daily/project-overview.md" ]]; then
            cp dev-daily/project-overview.md dev-daily/
            print_success "项目总览模板已安装"
        fi
        
        if [[ ! -f "dev-daily/development-guidelines.md" ]]; then
            cp dev-daily/development-guidelines.md dev-daily/
            print_success "开发指南已安装"
        fi
        
    else
        # 全新安装模式
        cp -r dev-daily ./
        print_success "dev-daily系统已安装"
    fi
}

# 创建初始配置
create_initial_config() {
    print_info "创建初始配置..."
    
    # 获取项目信息
    echo
    print_info "请提供项目基本信息（可稍后在project-overview.md中修改）："
    
    read -p "项目名称: " project_name
    read -p "项目类型 (Web应用/API服务/移动应用/其他): " project_type
    read -p "主要技术栈 (如: React+Node.js): " tech_stack
    read -p "项目描述: " project_description
    
    # 更新project-overview.md
    if [[ -f "dev-daily/project-overview.md" ]]; then
        # 简单的替换，实际项目中可能需要更复杂的处理
        sed -i.bak "s/\[在此填入您的项目名称\]/$project_name/g" dev-daily/project-overview.md
        sed -i.bak "s/\[Web应用\/移动应用\/API服务\/其他\]/$project_type/g" dev-daily/project-overview.md
        sed -i.bak "s/\[在此简要描述项目的目标、功能和价值\]/$project_description/g" dev-daily/project-overview.md
        
        # 删除备份文件
        rm -f dev-daily/project-overview.md.bak
        
        print_success "项目信息已更新到project-overview.md"
    fi
}

# 创建第一个进度记录
create_first_record() {
    print_info "创建第一个进度记录..."
    
    current_date=$(date +%Y-%m-%d)
    first_record="dev-daily/${current_date}-dev-daily-system-setup.md"
    
    cat > "$first_record" << EOF
# Dev-Daily系统安装记录 - $current_date

## 📋 安装概述

**安装时间**: $(date '+%Y-%m-%d %H:%M:%S')  
**安装方式**: 自动安装脚本  
**项目名称**: ${project_name:-"[待填写]"}  
**项目类型**: ${project_type:-"[待填写]"}  

## ✅ 安装完成

### 系统组件
- ✅ **核心目录结构**: dev-daily目录已创建
- ✅ **项目总览**: project-overview.md已配置
- ✅ **开发指南**: development-guidelines.md已安装
- ✅ **文档模板**: 3个模板文件已安装
- ✅ **示例文件**: 真实项目示例已安装

### 配置文件
- ✅ **project-overview.md**: 项目基本信息已填写
- ✅ **development-guidelines.md**: 开发规范已就绪
- ✅ **模板文件**: 日常更新、问题报告、部署记录模板已准备

## 🎯 下一步行动

### 立即行动
1. **完善项目信息**: 编辑 \`dev-daily/project-overview.md\` 补充详细信息
2. **调整开发指南**: 根据项目特点修改 \`dev-daily/development-guidelines.md\`
3. **配置AI助手**: 告知AI使用dev-daily系统进行协作

### AI协作配置
向AI助手说明：
\`\`\`
请使用dev-daily目录来了解项目状态和记录开发进展。
这是我们的项目协作系统，请先阅读project-overview.md了解项目基本情况，
然后查看最近的进度更新文件了解最新状态。
\`\`\`

## 📚 使用指南

### 日常工作流程
1. **开始工作**: AI阅读最近的进度文件
2. **完成任务**: 使用模板创建新的记录文件
3. **问题处理**: 使用问题报告模板记录和跟踪
4. **部署记录**: 使用部署模板记录每次部署

### 文件命名规范
- 日常更新: \`YYYY-MM-DD-progress-update.md\`
- 功能开发: \`YYYY-MM-DD-feature-功能名.md\`
- 问题修复: \`YYYY-MM-DD-bugfix-问题描述.md\`
- 部署记录: \`YYYY-MM-DD-deployment-版本号.md\`

## 💡 成功提示

### 关键成功因素
1. **及时记录**: 每天记录重要进展和问题
2. **结构化文档**: 使用提供的模板保持一致性
3. **定期回顾**: 每周检查和更新项目状态
4. **AI协作**: 让AI充分利用历史记录提供更好的帮助

### 预期效果
- 🚀 **开发效率**: 减少50%的上下文解释时间
- 🎯 **问题解决**: 通过历史记录快速定位类似问题
- 🤝 **团队协作**: AI实例间无缝协作
- 📈 **项目质量**: 基于历史数据做出更好的技术决策

---

**记录人**: 安装脚本  
**系统版本**: v1.0  
**下次检查**: $(date -d '+3 days' '+%Y-%m-%d')  

> 💡 **重要提醒**: dev-daily系统的价值在于持续使用，请从现在开始建立记录习惯！
EOF

    print_success "第一个进度记录已创建: $first_record"
}

# 显示安装后指导
show_post_install_guide() {
    echo
    print_success "🎉 Dev-Daily系统安装完成！"
    echo
    print_info "📋 接下来的步骤："
    echo "   1. 编辑 dev-daily/project-overview.md 完善项目信息"
    echo "   2. 根据需要调整 dev-daily/development-guidelines.md"
    echo "   3. 向AI助手说明使用dev-daily系统进行协作"
    echo
    print_info "🤖 AI协作配置："
    echo '   告诉AI: "请使用dev-daily目录来了解项目状态和记录开发进展"'
    echo
    print_info "📚 文档位置："
    echo "   - 完整说明: dev-daily-package/README.md"
    echo "   - 快速开始: dev-daily-package/QUICK_START.md"
    echo "   - 模板文件: dev-daily/templates/"
    echo "   - 示例文件: dev-daily/examples/"
    echo
    print_info "🎯 预期效果："
    echo "   - 开发效率提升 50%"
    echo "   - 问题解决速度提升 70%"
    echo "   - AI协作质量显著改善"
    echo
    print_warning "💡 重要提醒: 系统的价值在于持续使用，请建立每日记录的习惯！"
}

# 主安装流程
main() {
    echo
    print_info "🚀 Dev-Daily 系统安装程序 v1.0"
    print_info "这将在您的项目中安装AI开发协作系统"
    echo
    
    # 检查项目根目录
    check_project_root
    
    # 检查现有安装
    check_existing_installation
    
    # 安装系统
    install_dev_daily
    
    # 创建初始配置
    create_initial_config
    
    # 创建第一个记录
    create_first_record
    
    # 显示后续指导
    show_post_install_guide
    
    echo
    print_success "安装完成！开始您的高效AI协作开发之旅吧！"
}

# 运行主程序
main "$@"
