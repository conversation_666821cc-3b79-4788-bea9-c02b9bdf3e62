# Dev-Daily V2 部署和开发指南

## 🚀 项目已成功推送到GitHub

**仓库地址**: https://github.com/justpm2099/dev-daily-v2

## 📋 在另一台电脑上继续开发

### 1. 环境准备
确保新电脑上已安装：
- **Node.js** (v16或更高版本)
- **Git**
- **VSCode** (推荐)

### 2. 克隆项目
```bash
# 克隆仓库
git clone https://github.com/justpm2099/dev-daily-v2.git

# 进入项目目录
cd dev-daily-v2
```

### 3. 安装依赖
```bash
# 进入主项目目录
cd dev-daily-v2
npm install

# 如果有其他子项目，也需要安装依赖
cd ../cloudflare-user-analytics
npm install

cd ../personal-dashboard/web-interface
npm install
```

### 4. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，添加您的API密钥
# VITE_OPENAI_API_KEY=sk-proj-your-actual-api-key-here
```

### 5. 启动开发服务器
```bash
# 启动dev-daily-v2
cd dev-daily-v2
npm run dev

# 项目将在 http://localhost:5173 运行
```

## 🔧 开发工作流

### 日常开发
```bash
# 拉取最新代码
git pull origin master

# 创建新功能分支
git checkout -b feature/new-feature

# 开发完成后提交
git add .
git commit -m "feat: add new feature"

# 推送到远程仓库
git push origin feature/new-feature
```

### 部署到Cloudflare Pages
```bash
# 构建项目
npm run build

# 使用Wrangler部署
npx wrangler pages deploy dist --project-name=dev-daily-v2
```

## 📁 项目结构说明

```
dev-daily-V2/
├── dev-daily-v2/              # 主项目 (React + TypeScript)
├── cloudflare-user-analytics/  # Cloudflare分析项目
├── personal-dashboard/         # 个人仪表板
├── dev-daily-package/         # 打包工具
├── disaster-recovery/         # 灾难恢复
└── docs/                      # 文档
```

## 🔑 重要配置

### API密钥配置
- OpenAI API密钥：用于AI功能
- Cloudflare API令牌：用于部署和分析

### 安全注意事项
- ⚠️ 绝不要将API密钥提交到Git
- 使用 `.env` 文件存储敏感信息
- `.env` 文件已被添加到 `.gitignore`

## 🛠️ 常用命令

```bash
# 开发模式
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview

# 类型检查
npm run type-check

# 代码格式化
npm run format
```

## 📞 支持

如果在设置过程中遇到问题：
1. 检查 `SETUP.md` 文件
2. 查看项目文档
3. 确保所有依赖都已正确安装
4. 验证环境变量配置

---

**下一步**: 在新电脑上按照上述步骤设置完成后，您就可以继续开发了！
