{"project_info": {"name": "Personal Dashboard", "current_version": "v1.0", "last_updated": "2025-07-09T10:00:00Z", "total_modules": 8}, "module_status": {"ROOT": {"status": "active", "progress": 0, "start_date": "2025-07-09", "estimated_completion": "2025-07-30", "notes": "项目启动"}, "A": {"status": "planned", "progress": 0, "start_date": null, "estimated_completion": "2025-07-15", "notes": "用户模块组"}, "A1": {"status": "planned", "progress": 0, "start_date": null, "estimated_completion": "2025-07-12", "assigned_to": null, "estimated_hours": 16, "actual_hours": 0, "notes": "核心认证功能，优先实现", "blockers": [], "dependencies_ready": true}, "A2": {"status": "planned", "progress": 0, "start_date": null, "estimated_completion": "2025-07-15", "assigned_to": null, "estimated_hours": 12, "actual_hours": 0, "notes": "依赖A1完成", "blockers": [], "dependencies_ready": false}, "B": {"status": "planned", "progress": 0, "start_date": null, "estimated_completion": "2025-07-22", "notes": "数据展示模块组"}, "B1": {"status": "planned", "progress": 0, "start_date": null, "estimated_completion": "2025-07-18", "assigned_to": null, "estimated_hours": 8, "actual_hours": 0, "notes": "需要集成天气API", "blockers": [], "dependencies_ready": false, "external_dependencies": ["OpenWeatherMap API Key"]}, "B2": {"status": "planned", "progress": 0, "start_date": null, "estimated_completion": "2025-07-20", "assigned_to": null, "estimated_hours": 20, "actual_hours": 0, "notes": "核心功能之一", "blockers": [], "dependencies_ready": false}, "B3": {"status": "planned", "progress": 0, "start_date": null, "estimated_completion": "2025-07-25", "assigned_to": null, "estimated_hours": 16, "actual_hours": 0, "notes": "低优先级，可延后", "blockers": [], "dependencies_ready": false}, "C": {"status": "planned", "progress": 0, "start_date": null, "estimated_completion": "2025-07-28", "notes": "设置管理模块组"}, "C1": {"status": "planned", "progress": 0, "start_date": null, "estimated_completion": "2025-07-26", "assigned_to": null, "estimated_hours": 6, "actual_hours": 0, "notes": "简单的主题切换", "blockers": [], "dependencies_ready": false}, "C2": {"status": "planned", "progress": 0, "start_date": null, "estimated_completion": "2025-07-28", "assigned_to": null, "estimated_hours": 8, "actual_hours": 0, "notes": "通知系统配置", "blockers": [], "dependencies_ready": false}}, "status_definitions": {"planned": "已规划，等待开始", "active": "正在开发中", "testing": "开发完成，测试中", "completed": "已完成", "blocked": "被阻塞", "paused": "暂停开发", "deprecated": "已废弃"}, "project_metrics": {"total_estimated_hours": 86, "completed_hours": 0, "remaining_hours": 86, "completion_percentage": 0, "modules_completed": 0, "modules_in_progress": 0, "modules_planned": 8, "average_module_size": 10.75}, "risk_assessment": {"high_risk_modules": [], "medium_risk_modules": ["B1"], "low_risk_modules": ["A1", "A2", "B2", "B3", "C1", "C2"], "external_dependencies": [{"name": "OpenWeatherMap API", "status": "pending", "impact": "B1模块无法完成"}]}, "change_history": [{"timestamp": "2025-07-09T10:00:00Z", "action": "project_created", "description": "初始化项目结构", "affected_modules": ["ROOT"]}]}