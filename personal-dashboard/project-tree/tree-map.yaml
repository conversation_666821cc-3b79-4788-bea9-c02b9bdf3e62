# Personal Dashboard 项目树结构定义
# 定义项目的完整模块层次结构和依赖关系

project_info:
  name: "Personal Dashboard"
  version: "1.0.0"
  description: "个人仪表板 - Project Tree验证项目"
  created_date: "2025-07-09"
  platform: "cloudflare"

# 项目树结构定义
tree_structure:
  root:
    id: "ROOT"
    name: "Personal Dashboard"
    type: "project_root"
    description: "个人仪表板项目根节点"
    
  modules:
    # A. 用户模块
    A:
      id: "A"
      name: "用户模块"
      type: "feature_group"
      description: "用户认证和个人信息管理"
      parent: "ROOT"
      children: ["A1", "A2"]
      priority: "high"
      
    A1:
      id: "A1"
      name: "登录认证"
      type: "feature_module"
      description: "用户登录、注册、密码重置功能"
      parent: "A"
      children: []
      dependencies: []
      interfaces:
        input: ["username", "password", "email"]
        output: ["jwt_token", "user_info"]
      priority: "high"
      
    A2:
      id: "A2"
      name: "个人资料"
      type: "feature_module"
      description: "用户个人信息查看和编辑"
      parent: "A"
      children: []
      dependencies: ["A1"]
      interfaces:
        input: ["user_id", "profile_data"]
        output: ["updated_profile"]
      priority: "medium"
      
    # B. 数据展示模块
    B:
      id: "B"
      name: "数据展示"
      type: "feature_group"
      description: "各类信息展示和数据可视化"
      parent: "ROOT"
      children: ["B1", "B2", "B3"]
      priority: "high"
      
    B1:
      id: "B1"
      name: "天气信息"
      type: "feature_module"
      description: "获取和展示当前天气信息"
      parent: "B"
      children: []
      dependencies: ["A1"]  # 需要用户位置信息
      interfaces:
        input: ["location", "api_key"]
        output: ["weather_data"]
      external_apis: ["OpenWeatherMap API"]
      priority: "medium"
      
    B2:
      id: "B2"
      name: "待办事项"
      type: "feature_module"
      description: "个人任务管理和待办清单"
      parent: "B"
      children: []
      dependencies: ["A1"]  # 需要用户认证
      interfaces:
        input: ["task_data", "user_id"]
        output: ["task_list", "task_status"]
      priority: "high"
      
    B3:
      id: "B3"
      name: "日历视图"
      type: "feature_module"
      description: "日程安排和日历展示"
      parent: "B"
      children: []
      dependencies: ["A1", "B2"]  # 依赖认证和待办事项
      interfaces:
        input: ["date_range", "events"]
        output: ["calendar_view"]
      priority: "low"
      
    # C. 设置管理模块
    C:
      id: "C"
      name: "设置管理"
      type: "feature_group"
      description: "应用设置和个性化配置"
      parent: "ROOT"
      children: ["C1", "C2"]
      priority: "medium"
      
    C1:
      id: "C1"
      name: "主题设置"
      type: "feature_module"
      description: "明暗主题切换和界面个性化"
      parent: "C"
      children: []
      dependencies: ["A1"]
      interfaces:
        input: ["theme_preference", "user_id"]
        output: ["applied_theme"]
      priority: "low"
      
    C2:
      id: "C2"
      name: "通知配置"
      type: "feature_module"
      description: "通知偏好和提醒设置"
      parent: "C"
      children: []
      dependencies: ["A1"]
      interfaces:
        input: ["notification_settings", "user_id"]
        output: ["notification_config"]
      priority: "low"

# 模块类型定义
module_types:
  project_root:
    description: "项目根节点"
    color: "#1f2937"
    
  feature_group:
    description: "功能组，包含多个相关模块"
    color: "#3b82f6"
    
  feature_module:
    description: "具体功能模块"
    color: "#10b981"

# 优先级定义
priority_levels:
  high:
    description: "核心功能，必须实现"
    color: "#ef4444"
    order: 1
    
  medium:
    description: "重要功能，优先实现"
    color: "#f59e0b"
    order: 2
    
  low:
    description: "增强功能，可选实现"
    color: "#6b7280"
    order: 3

# 开发阶段映射
development_phases:
  phase1:
    name: "基础架构"
    modules: ["A1", "A2"]
    
  phase2:
    name: "核心功能"
    modules: ["B1", "B2"]
    
  phase3:
    name: "增强功能"
    modules: ["B3", "C1", "C2"]
