# Cloudflare平台配置文件
# 定义Personal Dashboard项目的CF平台标准配置

platform:
  name: "cloudflare"
  version: "2023.7.0"
  coverage: "100%"  # 此项目完全基于CF平台

# Cloudflare服务配置
services:
  pages:
    enabled: true
    build_command: "npm run build"
    build_output_directory: "dist"
    root_directory: "web-interface"
    
  workers:
    enabled: true
    compatibility_date: "2023-07-10"
    main: "src/worker.js"
    
  kv:
    enabled: true
    namespaces:
      - name: "USER_DATA"
        binding: "USER_KV"
      - name: "APP_CACHE"
        binding: "CACHE_KV"
        
  d1:
    enabled: true
    databases:
      - name: "dashboard_db"
        binding: "DB"
        
  r2:
    enabled: false  # 暂不需要对象存储

# 环境配置
environments:
  development:
    domain: "dashboard-dev.pages.dev"
    workers_subdomain: "dashboard-dev"
    
  staging:
    domain: "dashboard-staging.pages.dev"
    workers_subdomain: "dashboard-staging"
    
  production:
    domain: "dashboard.example.com"
    workers_subdomain: "dashboard-api"
    custom_domain: true

# 性能限制
limits:
  worker_memory: "128MB"
  worker_cpu_time: "50ms"
  kv_read_operations: "100000/day"
  kv_write_operations: "1000/day"
  d1_queries: "5000/day"

# 安全配置
security:
  cors:
    allowed_origins: ["https://dashboard.example.com"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE"]
    
  rate_limiting:
    requests_per_minute: 60
    burst_size: 10
    
  authentication:
    method: "jwt"
    secret_binding: "JWT_SECRET"

# 监控和日志
monitoring:
  analytics: true
  real_user_monitoring: true
  web_analytics: true
  
logging:
  level: "info"
  destinations: ["console", "logpush"]
