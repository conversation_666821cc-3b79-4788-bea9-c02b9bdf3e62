# Cloudflare Workers 配置文件
# Personal Dashboard 项目的 Wrangler 配置

name = "personal-dashboard"
main = "src/worker.js"
compatibility_date = "2023-07-10"
compatibility_flags = ["nodejs_compat"]

# 环境配置
[env.development]
name = "personal-dashboard-dev"
route = { pattern = "dashboard-dev.pages.dev/api/*", zone_name = "pages.dev" }

[env.staging]
name = "personal-dashboard-staging"
route = { pattern = "dashboard-staging.pages.dev/api/*", zone_name = "pages.dev" }

[env.production]
name = "personal-dashboard-prod"
route = { pattern = "dashboard.example.com/api/*", zone_name = "example.com" }

# KV 命名空间配置
[[kv_namespaces]]
binding = "USER_KV"
id = "your-user-kv-namespace-id"
preview_id = "your-user-kv-preview-id"

[[kv_namespaces]]
binding = "CACHE_KV"
id = "your-cache-kv-namespace-id"
preview_id = "your-cache-kv-preview-id"

# D1 数据库配置
[[d1_databases]]
binding = "DB"
database_name = "dashboard_db"
database_id = "your-d1-database-id"

# 环境变量
[vars]
ENVIRONMENT = "development"
API_BASE_URL = "https://dashboard-dev.pages.dev/api"

# 生产环境变量 (通过 wrangler secret 设置)
# JWT_SECRET = "your-jwt-secret-key"
# WEATHER_API_KEY = "your-openweather-api-key"
# EMAIL_API_KEY = "your-email-service-api-key"

# 构建配置
[build]
command = "npm run build:worker"
cwd = "."
watch_dir = "src"

# 部署配置
[triggers]
crons = []

# 资源限制
[limits]
cpu_ms = 50
memory_mb = 128

# 兼容性设置
[compatibility_flags]
# 启用 Node.js 兼容性 (用于某些库)
nodejs_compat = true

# 开发配置
[dev]
ip = "127.0.0.1"
port = 8787
local_protocol = "http"

# 上传配置
[upload]
format = "modules"
dir = "./dist"
main = "./worker.js"

# 规则配置
[[rules]]
type = "ESModule"
globs = ["**/*.js", "**/*.mjs"]

# 持久化配置
[durable_objects]
# 如果需要 Durable Objects，在这里配置

# 分析配置
[analytics_engine_datasets]
# 如果需要分析引擎，在这里配置

# 队列配置
[[queues.producers]]
# 如果需要队列，在这里配置

[[queues.consumers]]
# 队列消费者配置

# 服务绑定
[[services]]
# 如果需要服务绑定，在这里配置

# R2 存储桶配置
[[r2_buckets]]
# 如果需要 R2 存储，在这里配置

# 环境特定配置
[env.development.vars]
ENVIRONMENT = "development"
API_BASE_URL = "http://localhost:8787/api"
DEBUG = "true"

[env.staging.vars]
ENVIRONMENT = "staging"
API_BASE_URL = "https://dashboard-staging.pages.dev/api"
DEBUG = "false"

[env.production.vars]
ENVIRONMENT = "production"
API_BASE_URL = "https://dashboard.example.com/api"
DEBUG = "false"

# 开发环境 KV
[env.development.kv_namespaces]
[[env.development.kv_namespaces]]
binding = "USER_KV"
id = "dev-user-kv-id"
preview_id = "dev-user-kv-preview-id"

[[env.development.kv_namespaces]]
binding = "CACHE_KV"
id = "dev-cache-kv-id"
preview_id = "dev-cache-kv-preview-id"

# 开发环境 D1
[env.development.d1_databases]
[[env.development.d1_databases]]
binding = "DB"
database_name = "dashboard_db_dev"
database_id = "dev-d1-database-id"

# 生产环境 KV
[env.production.kv_namespaces]
[[env.production.kv_namespaces]]
binding = "USER_KV"
id = "prod-user-kv-id"

[[env.production.kv_namespaces]]
binding = "CACHE_KV"
id = "prod-cache-kv-id"

# 生产环境 D1
[env.production.d1_databases]
[[env.production.d1_databases]]
binding = "DB"
database_name = "dashboard_db_prod"
database_id = "prod-d1-database-id"
