{"name": "personal-dashboard", "version": "1.0.0", "description": "Personal Dashboard - Project Tree验证项目", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:worker\" \"npm run dev:web\"", "dev:worker": "wrangler dev", "dev:web": "cd web-interface && npm run dev", "build": "npm run build:worker && npm run build:web", "build:worker": "esbuild src/worker.ts --bundle --format=esm --outfile=dist/worker.js --platform=neutral --target=es2022", "build:web": "cd web-interface && npm run build", "deploy": "npm run deploy:worker && npm run deploy:web", "deploy:worker": "wrangler deploy", "deploy:web": "cd web-interface && npm run deploy", "test": "vitest", "test:worker": "vitest --config vitest.worker.config.ts", "test:web": "cd web-interface && npm run test", "lint": "eslint src --ext .ts,.js", "lint:fix": "eslint src --ext .ts,.js --fix", "type-check": "tsc --noEmit", "db:migrate": "wrangler d1 migrations apply dashboard_db", "db:seed": "wrangler d1 execute dashboard_db --file=./database/seed.sql", "db:reset": "wrangler d1 execute dashboard_db --file=./database/reset.sql", "setup": "npm install && cd web-interface && npm install", "clean": "rm -rf dist && rm -rf web-interface/dist"}, "dependencies": {"@cloudflare/workers-types": "^4.20230710.0", "hono": "^3.4.1", "jose": "^4.14.4", "bcryptjs": "^2.4.3", "zod": "^3.21.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "concurrently": "^8.2.0", "esbuild": "^0.18.20", "eslint": "^8.45.0", "miniflare": "^3.20230718.0", "typescript": "^5.1.6", "vitest": "^0.34.1", "wrangler": "^3.5.1"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/personal-dashboard.git"}, "keywords": ["cloudflare", "workers", "dashboard", "project-tree", "typescript"], "author": "Your Name", "license": "MIT", "workspaces": ["web-interface"]}