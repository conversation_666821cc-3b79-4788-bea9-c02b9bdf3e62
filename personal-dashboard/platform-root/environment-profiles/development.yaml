# 开发环境配置文件
# Personal Dashboard 开发环境的详细配置

environment:
  name: "development"
  description: "本地开发环境配置"
  active: true

# Cloudflare 服务配置
cloudflare:
  account_id: "your-account-id"
  zone_id: "your-zone-id"
  
  workers:
    name: "personal-dashboard-dev"
    subdomain: "dashboard-dev"
    custom_domain: false
    
  pages:
    project_name: "personal-dashboard-dev"
    domain: "dashboard-dev.pages.dev"
    build_command: "npm run build"
    build_output_directory: "dist"
    
  kv:
    user_namespace:
      name: "USER_DATA_DEV"
      binding: "USER_KV"
      id: "dev-user-kv-namespace-id"
      
    cache_namespace:
      name: "APP_CACHE_DEV"
      binding: "CACHE_KV"
      id: "dev-cache-kv-namespace-id"
      
  d1:
    database:
      name: "dashboard_db_dev"
      binding: "DB"
      id: "dev-d1-database-id"

# 应用配置
application:
  debug: true
  log_level: "debug"
  api_base_url: "http://localhost:8787/api"
  web_base_url: "http://localhost:5173"
  
  cors:
    enabled: true
    origins: 
      - "http://localhost:5173"
      - "http://localhost:3000"
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    headers: ["Content-Type", "Authorization"]
    
  rate_limiting:
    enabled: false  # 开发环境禁用限流
    requests_per_minute: 1000
    
  authentication:
    jwt_expiry: "24h"
    refresh_token_expiry: "7d"
    password_min_length: 6  # 开发环境降低要求
    max_login_attempts: 10  # 开发环境放宽限制
    lockout_duration: "5m"

# 外部服务配置
external_services:
  weather_api:
    provider: "openweathermap"
    base_url: "https://api.openweathermap.org/data/2.5"
    timeout: 5000
    cache_duration: "10m"
    
  email_service:
    provider: "mock"  # 开发环境使用模拟服务
    from_address: "<EMAIL>"

# 数据库配置
database:
  auto_migrate: true
  seed_data: true
  backup_enabled: false
  
  connection:
    timeout: 5000
    retry_attempts: 3
    
  migrations:
    auto_run: true
    directory: "./database/migrations"
    
  seeds:
    auto_run: true
    directory: "./database/seeds"

# 缓存配置
cache:
  default_ttl: 300  # 5分钟
  max_size: "10MB"
  
  strategies:
    user_data: "memory"
    weather_data: "kv"
    static_content: "browser"

# 监控配置
monitoring:
  enabled: true
  metrics:
    - "request_count"
    - "response_time"
    - "error_rate"
    
  alerts:
    enabled: false  # 开发环境禁用告警
    
  logging:
    console: true
    file: false
    remote: false

# 开发工具配置
development:
  hot_reload: true
  source_maps: true
  
  mock_data:
    enabled: true
    users: 5
    todos: 20
    
  test_data:
    reset_on_start: true
    auto_generate: true

# 安全配置
security:
  https_only: false  # 开发环境允许HTTP
  
  headers:
    x_frame_options: "DENY"
    x_content_type_options: "nosniff"
    x_xss_protection: "1; mode=block"
    
  csrf:
    enabled: false  # 开发环境简化
    
  content_security_policy:
    enabled: false  # 开发环境禁用以便调试

# 性能配置
performance:
  bundle_size_limit: "2MB"  # 开发环境放宽限制
  response_timeout: 30000   # 30秒
  
  optimization:
    minify: false
    compress: false
    tree_shaking: false

# 功能开关
feature_flags:
  weather_module: true
  todo_module: true
  calendar_module: true
  theme_switching: true
  notifications: false  # 开发阶段暂时禁用
  
  experimental:
    ai_suggestions: false
    real_time_sync: false

# 本地开发服务器
dev_server:
  worker:
    port: 8787
    host: "127.0.0.1"
    
  web:
    port: 5173
    host: "127.0.0.1"
    open_browser: true
    
  proxy:
    api_prefix: "/api"
    target: "http://localhost:8787"

# 环境变量映射
environment_variables:
  ENVIRONMENT: "development"
  DEBUG: "true"
  API_BASE_URL: "http://localhost:8787/api"
  WEB_BASE_URL: "http://localhost:5173"
  LOG_LEVEL: "debug"
