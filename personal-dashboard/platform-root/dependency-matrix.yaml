# Cloudflare兼容依赖版本矩阵
# 确保所有依赖都与CF Workers/Pages兼容

# 前端依赖 (Cloudflare Pages)
frontend_dependencies:
  core:
    react: "18.2.0"              # 稳定版本，CF Pages完全支持
    react-dom: "18.2.0"
    typescript: "5.1.6"          # CF构建环境支持
    
  build_tools:
    vite: "4.4.5"               # CF Pages推荐构建工具
    "@vitejs/plugin-react": "4.0.3"
    esbuild: "0.18.20"          # CF内部使用的构建工具
    
  ui_framework:
    tailwindcss: "3.3.3"       # 轻量级CSS框架
    "@headlessui/react": "1.7.15"  # 无样式组件库
    "lucide-react": "0.263.1"   # 图标库
    
  routing:
    "react-router-dom": "6.14.2"  # 客户端路由
    
  state_management:
    zustand: "4.4.1"            # 轻量级状态管理
    
  utilities:
    clsx: "2.0.0"               # 条件类名工具
    "date-fns": "2.30.0"        # 日期处理库

# 后端依赖 (Cloudflare Workers)
worker_dependencies:
  runtime:
    "@cloudflare/workers-types": "4.********.0"
    typescript: "5.1.6"
    
  framework:
    hono: "3.4.1"               # 轻量级Web框架，CF优化
    
  database:
    "@cloudflare/d1": "1.0.0"   # CF D1 SQLite客户端
    
  storage:
    "@cloudflare/kv-asset-handler": "0.3.0"
    
  utilities:
    "jose": "4.14.4"            # JWT处理 (Web标准API)
    "bcryptjs": "2.4.3"         # 密码哈希 (纯JS实现)
    
  validation:
    zod: "3.21.4"               # 类型验证库

# 开发工具
dev_dependencies:
  testing:
    vitest: "0.34.1"            # 测试框架
    "@testing-library/react": "13.4.0"
    "@testing-library/jest-dom": "5.17.0"
    
  linting:
    eslint: "8.45.0"
    "@typescript-eslint/eslint-plugin": "6.2.1"
    "@typescript-eslint/parser": "6.2.1"
    
  cloudflare_tools:
    wrangler: "3.5.1"           # CF官方CLI工具
    miniflare: "3.20230718.0"   # 本地开发环境
    
  type_checking:
    "@types/react": "18.2.15"
    "@types/react-dom": "18.2.7"
    "@types/bcryptjs": "2.4.2"

# 兼容性检查规则
compatibility_rules:
  forbidden_apis:
    - "fs"                      # 文件系统API
    - "path"                    # Node.js路径API
    - "crypto.randomBytes"      # Node.js crypto API
    - "process.env"             # 使用CF环境变量替代
    
  allowed_web_apis:
    - "fetch"                   # 网络请求
    - "crypto.subtle"           # Web Crypto API
    - "TextEncoder/TextDecoder" # 文本编码
    - "URL"                     # URL处理
    - "Headers"                 # HTTP头处理
    
  bundle_size_limits:
    worker_script: "1MB"        # Worker脚本大小限制
    single_chunk: "500KB"       # 单个代码块限制
    
  performance_requirements:
    cold_start: "<10ms"         # 冷启动时间
    memory_usage: "<64MB"       # 内存使用限制
    cpu_time: "<30ms"           # CPU时间限制

# 版本更新策略
update_policy:
  major_versions:
    approval: "manual"          # 主版本需要手动审核
    testing: "comprehensive"    # 全面测试
    
  minor_versions:
    approval: "automatic"       # 次版本自动更新
    testing: "regression"       # 回归测试
    
  patch_versions:
    approval: "automatic"       # 补丁版本自动更新
    testing: "smoke"            # 冒烟测试
    
  security_updates:
    approval: "immediate"       # 安全更新立即应用
    testing: "priority"         # 优先测试
