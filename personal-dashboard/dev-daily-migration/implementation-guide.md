# dev-daily升级实施指南

## 🚀 快速开始

基于Personal Dashboard的成功验证，本指南将帮助你将Project Tree管理概念迁移到dev-daily工具中。

### 前置条件
- 现有dev-daily工具正常运行
- Node.js 18+ 环境
- 完整的数据备份

## 📦 核心组件迁移

### 1. Project Tree管理器

#### 复制核心文件
```bash
# 从Personal Dashboard复制核心组件
cp personal-dashboard/web-interface/src/components/DynamicProjectTree.tsx dev-daily/src/components/
cp personal-dashboard/web-interface/src/components/EnhancedProjectTree.tsx dev-daily/src/components/

# 复制项目树配置
cp personal-dashboard/project-tree/ dev-daily/project-tree/
```

#### 适配dev-daily数据结构
```typescript
// dev-daily/src/adapters/ProjectTreeAdapter.ts
export class ProjectTreeAdapter {
  // 将dev-daily的项目数据转换为Project Tree格式
  static convertToTreeStructure(dailyProjects: DailyProject[]): TreeModule {
    return {
      id: 'ROOT',
      name: 'Dev Daily Projects',
      description: '开发日志项目管理',
      status: 'active',
      priority: 'high',
      progress: this.calculateOverallProgress(dailyProjects),
      children: dailyProjects.map(project => this.convertProject(project))
    };
  }
  
  // 将单个项目转换为树节点
  static convertProject(project: DailyProject): TreeModule {
    return {
      id: project.id,
      name: project.name,
      description: project.description,
      status: this.mapStatus(project.status),
      priority: this.mapPriority(project.priority),
      progress: project.progress || 0,
      children: project.tasks?.map(task => this.convertTask(task)) || []
    };
  }
  
  // 将任务转换为子节点
  static convertTask(task: DailyTask): TreeModule {
    return {
      id: task.id,
      name: task.title,
      description: task.description,
      status: task.completed ? 'completed' : 'active',
      priority: 'medium',
      progress: task.completed ? 100 : 0,
      estimatedHours: task.estimatedHours,
      actualHours: task.actualHours
    };
  }
}
```

### 2. AI助手集成

#### 复制AI组件
```bash
# 复制AI助手相关文件
cp personal-dashboard/web-interface/src/components/AIAssistant.tsx dev-daily/src/components/
cp personal-dashboard/web-interface/src/components/CodeGenerator.tsx dev-daily/src/components/
```

#### 适配dev-daily场景
```typescript
// dev-daily/src/services/DevDailyAIService.ts
export class DevDailyAIService {
  // 基于dev-daily数据生成智能建议
  generateSuggestions(dailyData: DailyData): AISuggestion[] {
    const suggestions: AISuggestion[] = [];
    
    // 分析工作模式
    if (this.detectLongWorkingSessions(dailyData)) {
      suggestions.push({
        id: 'work-life-balance',
        type: 'optimization',
        title: '建议优化工作节奏',
        description: '检测到连续长时间工作，建议增加休息时间',
        impact: 'medium',
        confidence: 85
      });
    }
    
    // 分析项目进度
    if (this.detectStagnantProjects(dailyData)) {
      suggestions.push({
        id: 'project-review',
        type: 'refactor',
        title: '建议回顾停滞项目',
        description: '发现有项目长时间无进展，建议重新评估优先级',
        impact: 'high',
        confidence: 90
      });
    }
    
    return suggestions;
  }
  
  // 生成dev-daily特定的代码模板
  generateDailyTemplates(): CodeTemplate[] {
    return [
      {
        id: 'daily-log-template',
        name: '日志模板',
        description: '生成结构化的日志记录模板',
        category: 'template'
      },
      {
        id: 'project-summary',
        name: '项目总结',
        description: '基于日志自动生成项目进度总结',
        category: 'report'
      }
    ];
  }
}
```

### 3. 备份恢复系统

#### 复制备份脚本
```bash
# 复制备份相关文件
cp personal-dashboard/disaster-recovery/ dev-daily/backup-system/
```

#### 适配dev-daily数据
```typescript
// dev-daily/src/services/DevDailyBackupService.ts
export class DevDailyBackupService {
  async createBackup(type: 'daily' | 'weekly' | 'milestone'): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupId = `devdaily-${type}-${timestamp}`;
    
    try {
      // 1. 备份日志数据
      const logs = await this.exportDailyLogs();
      
      // 2. 备份项目数据
      const projects = await this.exportProjects();
      
      // 3. 备份设置
      const settings = await this.exportSettings();
      
      // 4. 创建备份包
      const backupData = {
        id: backupId,
        type,
        timestamp,
        data: { logs, projects, settings },
        version: this.getCurrentVersion()
      };
      
      await this.saveBackup(backupData);
      return backupId;
      
    } catch (error) {
      throw new Error(`备份失败: ${error.message}`);
    }
  }
  
  async restoreFromBackup(backupId: string): Promise<void> {
    try {
      const backup = await this.loadBackup(backupId);
      
      // 1. 验证备份完整性
      await this.validateBackup(backup);
      
      // 2. 创建当前状态备份
      await this.createBackup('emergency');
      
      // 3. 恢复数据
      await this.restoreDailyLogs(backup.data.logs);
      await this.restoreProjects(backup.data.projects);
      await this.restoreSettings(backup.data.settings);
      
      // 4. 验证恢复结果
      await this.validateRestore();
      
    } catch (error) {
      throw new Error(`恢复失败: ${error.message}`);
    }
  }
}
```

## 🔄 数据迁移步骤

### Step 1: 数据备份
```bash
# 创建完整备份
node scripts/backup-current-data.js

# 验证备份完整性
node scripts/verify-backup.js
```

### Step 2: 安装新依赖
```bash
# 安装Project Tree相关依赖
npm install lucide-react clsx date-fns

# 安装AI功能依赖（如果需要）
npm install openai @types/openai
```

### Step 3: 数据结构升级
```typescript
// scripts/migrate-data.ts
import { DataMigration } from '../src/services/DataMigration';

async function main() {
  const migration = new DataMigration();
  
  console.log('开始数据迁移...');
  
  // 1. 备份现有数据
  await migration.backupCurrentData();
  
  // 2. 转换数据格式
  await migration.convertToTreeStructure();
  
  // 3. 验证迁移结果
  await migration.validateMigration();
  
  console.log('数据迁移完成！');
}

main().catch(console.error);
```

### Step 4: 界面集成
```typescript
// dev-daily/src/App.tsx
import { MainDashboard } from './components/MainDashboard';
import { ProjectTreeProvider } from './contexts/ProjectTreeContext';

function App() {
  return (
    <ProjectTreeProvider>
      <MainDashboard />
    </ProjectTreeProvider>
  );
}
```

## 🧪 测试验证

### 功能测试清单
```bash
# 运行测试套件
npm test

# 测试项目树功能
npm run test:project-tree

# 测试AI功能
npm run test:ai

# 测试备份恢复
npm run test:backup
```

### 手动测试步骤
1. **基础功能验证**
   - [ ] 现有日志记录功能正常
   - [ ] 项目管理功能正常
   - [ ] 设置保存和加载正常

2. **新功能验证**
   - [ ] 项目树可视化正常显示
   - [ ] 拖拽重组功能工作
   - [ ] AI建议生成正常
   - [ ] 代码生成器工作
   - [ ] 备份恢复功能正常

3. **性能验证**
   - [ ] 启动时间 < 3秒
   - [ ] 界面响应 < 500ms
   - [ ] 数据加载 < 1秒

## 📱 用户界面更新

### 主界面布局
```typescript
// dev-daily/src/components/DevDailyDashboard.tsx
export const DevDailyDashboard: React.FC = () => {
  const [activeView, setActiveView] = useState<'daily-logs' | 'project-tree' | 'ai-assistant'>('daily-logs');
  
  return (
    <div className="flex h-screen">
      {/* 侧边栏导航 */}
      <Sidebar activeView={activeView} onViewChange={setActiveView} />
      
      {/* 主内容区 */}
      <main className="flex-1">
        {activeView === 'daily-logs' && <DailyLogsView />}
        {activeView === 'project-tree' && <EnhancedProjectTree />}
        {activeView === 'ai-assistant' && <AIAssistant />}
      </main>
    </div>
  );
};
```

### 兼容性层
```typescript
// dev-daily/src/components/CompatibilityLayer.tsx
export const CompatibilityLayer: React.FC = () => {
  const [showLegacyView, setShowLegacyView] = useState(false);
  
  return (
    <div>
      {/* 切换按钮 */}
      <button onClick={() => setShowLegacyView(!showLegacyView)}>
        {showLegacyView ? '切换到新版界面' : '切换到经典界面'}
      </button>
      
      {/* 条件渲染 */}
      {showLegacyView ? <LegacyDailyView /> : <DevDailyDashboard />}
    </div>
  );
};
```

## 🔧 配置和部署

### 环境配置
```yaml
# dev-daily/config/development.yaml
app:
  name: "dev-daily-v2"
  version: "2.0.0"
  
features:
  project_tree: true
  ai_assistant: true
  backup_system: true
  legacy_compatibility: true
  
ai:
  provider: "openai"  # 或 "local"
  model: "gpt-3.5-turbo"
  
backup:
  auto_backup: true
  backup_interval: "daily"
  retention_days: 30
```

### 部署脚本
```bash
#!/bin/bash
# deploy.sh

echo "开始部署dev-daily v2..."

# 1. 备份当前版本
npm run backup:current

# 2. 构建新版本
npm run build

# 3. 运行迁移脚本
npm run migrate

# 4. 启动新版本
npm start

echo "部署完成！"
```

## 📚 用户指南

### 快速上手
1. **启动应用**: 运行 `npm start`
2. **数据迁移**: 首次启动会自动迁移数据
3. **界面导航**: 使用左侧导航切换功能
4. **项目树**: 在项目树视图中管理项目结构
5. **AI助手**: 使用AI助手获取智能建议

### 常见问题
**Q: 如何回到旧版界面？**
A: 点击右上角的"经典界面"按钮

**Q: 数据迁移失败怎么办？**
A: 运行 `npm run restore:backup` 恢复备份

**Q: AI功能不工作？**
A: 检查配置文件中的AI设置，确保API密钥正确

## 🎯 下一步计划

1. **用户反馈收集**: 收集用户使用反馈
2. **功能优化**: 基于反馈优化功能
3. **性能调优**: 持续优化性能表现
4. **文档完善**: 补充详细的用户文档

---

**实施指南版本**: v1.0  
**适用版本**: dev-daily v2.0.0  
**最后更新**: 2025-07-09
