import React, { useState } from 'react';
import { 
  Code, 
  Download, 
  Copy, 
  CheckCircle, 
  Settings, 
  Database,
  Globe,
  Zap,
  FileText,
  Play
} from 'lucide-react';
import clsx from 'clsx';

// 代码模板类型
interface CodeTemplate {
  id: string;
  name: string;
  description: string;
  category: 'component' | 'api' | 'database' | 'config' | 'test';
  framework: 'react' | 'workers' | 'general';
  complexity: 'basic' | 'intermediate' | 'advanced';
  estimatedTime: string;
}

// 生成的代码文件
interface GeneratedCode {
  filename: string;
  content: string;
  language: string;
  description: string;
}

// 代码模板数据
const codeTemplates: CodeTemplate[] = [
  {
    id: 'react-module-component',
    name: 'React模块组件',
    description: '基于Project Tree模块生成React组件模板',
    category: 'component',
    framework: 'react',
    complexity: 'intermediate',
    estimatedTime: '15分钟'
  },
  {
    id: 'workers-api-endpoint',
    name: 'Workers API端点',
    description: '生成Cloudflare Workers API路由和处理函数',
    category: 'api',
    framework: 'workers',
    complexity: 'intermediate',
    estimatedTime: '20分钟'
  },
  {
    id: 'd1-database-schema',
    name: 'D1数据库模式',
    description: '根据模块需求生成D1数据库表结构',
    category: 'database',
    framework: 'general',
    complexity: 'basic',
    estimatedTime: '10分钟'
  },
  {
    id: 'wrangler-config',
    name: 'Wrangler配置',
    description: '生成完整的wrangler.toml配置文件',
    category: 'config',
    framework: 'workers',
    complexity: 'basic',
    estimatedTime: '5分钟'
  },
  {
    id: 'vitest-test-suite',
    name: 'Vitest测试套件',
    description: '为模块生成完整的单元测试',
    category: 'test',
    framework: 'general',
    complexity: 'advanced',
    estimatedTime: '30分钟'
  }
];

// 代码生成器
const generateCode = (templateId: string, options: any): GeneratedCode[] => {
  switch (templateId) {
    case 'react-module-component':
      return generateReactComponent(options);
    case 'workers-api-endpoint':
      return generateWorkersAPI(options);
    case 'd1-database-schema':
      return generateDatabaseSchema(options);
    case 'wrangler-config':
      return generateWranglerConfig(options);
    case 'vitest-test-suite':
      return generateTestSuite(options);
    default:
      return [];
  }
};

// React组件生成器
const generateReactComponent = (options: any): GeneratedCode[] => {
  const { moduleName = 'ExampleModule', moduleDescription = '示例模块' } = options;
  
  const componentCode = `import React, { useState, useEffect } from 'react';
import { ${moduleName}Props } from './types';

interface ${moduleName}State {
  loading: boolean;
  data: any[];
  error: string | null;
}

export const ${moduleName}: React.FC<${moduleName}Props> = ({ 
  onDataChange,
  initialData = []
}) => {
  const [state, setState] = useState<${moduleName}State>({
    loading: false,
    data: initialData,
    error: null
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      // TODO: 实现数据加载逻辑
      const response = await fetch('/api/${moduleName.toLowerCase()}');
      const data = await response.json();
      
      setState(prev => ({ ...prev, data, loading: false }));
      onDataChange?.(data);
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : '加载失败',
        loading: false 
      }));
    }
  };

  const handleRefresh = () => {
    loadData();
  };

  if (state.loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">加载中...</span>
      </div>
    );
  }

  if (state.error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <h3 className="text-red-800 font-medium">加载错误</h3>
        <p className="text-red-600 text-sm mt-1">{state.error}</p>
        <button 
          onClick={handleRefresh}
          className="mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
        >
          重试
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200">
      <div className="p-4 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <h2 className="text-lg font-bold text-gray-900">${moduleDescription}</h2>
          <button
            onClick={handleRefresh}
            className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            刷新
          </button>
        </div>
      </div>
      
      <div className="p-4">
        {state.data.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <p>暂无数据</p>
          </div>
        ) : (
          <div className="space-y-2">
            {state.data.map((item, index) => (
              <div key={index} className="p-3 border border-gray-200 rounded">
                {/* TODO: 根据实际数据结构渲染内容 */}
                <pre className="text-sm">{JSON.stringify(item, null, 2)}</pre>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};`;

  const typesCode = `export interface ${moduleName}Props {
  onDataChange?: (data: any[]) => void;
  initialData?: any[];
}

export interface ${moduleName}Data {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}`;

  return [
    {
      filename: `${moduleName}.tsx`,
      content: componentCode,
      language: 'typescript',
      description: `${moduleDescription}的React组件实现`
    },
    {
      filename: `types.ts`,
      content: typesCode,
      language: 'typescript',
      description: '类型定义文件'
    }
  ];
};

// Workers API生成器
const generateWorkersAPI = (options: any): GeneratedCode[] => {
  const { moduleName = 'example', methods = ['GET', 'POST'] } = options;
  
  const apiCode = `import { Hono } from 'hono';
import { cors } from 'hono/cors';

const app = new Hono();

// 启用CORS
app.use('*', cors());

// ${moduleName} API路由
const ${moduleName}Router = new Hono();

${methods.includes('GET') ? `
// 获取${moduleName}列表
${moduleName}Router.get('/', async (c) => {
  try {
    // TODO: 从D1数据库获取数据
    const db = c.env.DB;
    const result = await db.prepare('SELECT * FROM ${moduleName}s ORDER BY created_at DESC').all();
    
    return c.json({
      success: true,
      data: result.results
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error.message
    }, 500);
  }
});

// 获取单个${moduleName}
${moduleName}Router.get('/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const db = c.env.DB;
    
    const result = await db.prepare('SELECT * FROM ${moduleName}s WHERE id = ?').bind(id).first();
    
    if (!result) {
      return c.json({
        success: false,
        error: '${moduleName} not found'
      }, 404);
    }
    
    return c.json({
      success: true,
      data: result
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error.message
    }, 500);
  }
});` : ''}

${methods.includes('POST') ? `
// 创建${moduleName}
${moduleName}Router.post('/', async (c) => {
  try {
    const body = await c.req.json();
    const db = c.env.DB;
    
    // TODO: 验证输入数据
    const { name, description } = body;
    
    const result = await db.prepare(
      'INSERT INTO ${moduleName}s (id, name, description, created_at) VALUES (?, ?, ?, ?)'
    ).bind(
      crypto.randomUUID(),
      name,
      description,
      new Date().toISOString()
    ).run();
    
    return c.json({
      success: true,
      data: { id: result.meta.last_row_id }
    }, 201);
  } catch (error) {
    return c.json({
      success: false,
      error: error.message
    }, 500);
  }
});` : ''}

${methods.includes('PUT') ? `
// 更新${moduleName}
${moduleName}Router.put('/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const body = await c.req.json();
    const db = c.env.DB;
    
    const { name, description } = body;
    
    const result = await db.prepare(
      'UPDATE ${moduleName}s SET name = ?, description = ?, updated_at = ? WHERE id = ?'
    ).bind(name, description, new Date().toISOString(), id).run();
    
    if (result.changes === 0) {
      return c.json({
        success: false,
        error: '${moduleName} not found'
      }, 404);
    }
    
    return c.json({
      success: true,
      message: '${moduleName} updated successfully'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error.message
    }, 500);
  }
});` : ''}

${methods.includes('DELETE') ? `
// 删除${moduleName}
${moduleName}Router.delete('/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const db = c.env.DB;
    
    const result = await db.prepare('DELETE FROM ${moduleName}s WHERE id = ?').bind(id).run();
    
    if (result.changes === 0) {
      return c.json({
        success: false,
        error: '${moduleName} not found'
      }, 404);
    }
    
    return c.json({
      success: true,
      message: '${moduleName} deleted successfully'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error.message
    }, 500);
  }
});` : ''}

// 注册路由
app.route('/api/${moduleName}', ${moduleName}Router);

export default app;`;

  return [
    {
      filename: `${moduleName}-api.ts`,
      content: apiCode,
      language: 'typescript',
      description: `${moduleName}模块的Cloudflare Workers API实现`
    }
  ];
};

// 数据库模式生成器
const generateDatabaseSchema = (options: any): GeneratedCode[] => {
  const { tableName = 'example', fields = [] } = options;
  
  const schemaCode = `-- ${tableName}表结构
CREATE TABLE IF NOT EXISTS ${tableName}s (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  status TEXT DEFAULT 'active',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_${tableName}s_status ON ${tableName}s(status);
CREATE INDEX IF NOT EXISTS idx_${tableName}s_created_at ON ${tableName}s(created_at);

-- 插入示例数据
INSERT OR IGNORE INTO ${tableName}s (id, name, description) VALUES 
  ('${tableName}-1', '示例${tableName}1', '这是一个示例${tableName}'),
  ('${tableName}-2', '示例${tableName}2', '这是另一个示例${tableName}');`;

  return [
    {
      filename: `${tableName}-schema.sql`,
      content: schemaCode,
      language: 'sql',
      description: `${tableName}表的数据库模式定义`
    }
  ];
};

// Wrangler配置生成器
const generateWranglerConfig = (options: any): GeneratedCode[] => {
  const { projectName = 'my-project', services = {} } = options;
  
  const configCode = `name = "${projectName}"
main = "src/worker.ts"
compatibility_date = "2023-07-10"
compatibility_flags = ["nodejs_compat"]

[vars]
ENVIRONMENT = "development"
DEBUG = "true"

${services.kv ? `
[[kv_namespaces]]
binding = "KV"
id = "your-kv-namespace-id"
preview_id = "your-kv-preview-id"
` : ''}

${services.d1 ? `
[[d1_databases]]
binding = "DB"
database_name = "${projectName}_db"
database_id = "your-d1-database-id"
` : ''}

${services.r2 ? `
[[r2_buckets]]
binding = "BUCKET"
bucket_name = "${projectName}-bucket"
` : ''}

[build]
command = "npm run build"
cwd = "."
watch_dir = "src"

[env.production]
name = "${projectName}-prod"
vars = { ENVIRONMENT = "production", DEBUG = "false" }`;

  return [
    {
      filename: 'wrangler.toml',
      content: configCode,
      language: 'toml',
      description: 'Cloudflare Workers配置文件'
    }
  ];
};

// 测试套件生成器
const generateTestSuite = (options: any): GeneratedCode[] => {
  const { moduleName = 'Example' } = options;
  
  const testCode = `import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ${moduleName} } from './${moduleName}';

// Mock fetch
global.fetch = vi.fn();

describe('${moduleName}', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('应该正确渲染组件', () => {
    render(<${moduleName} />);
    expect(screen.getByText('${moduleName}')).toBeInTheDocument();
  });

  it('应该显示加载状态', () => {
    (fetch as any).mockImplementation(() => 
      new Promise(resolve => setTimeout(resolve, 100))
    );
    
    render(<${moduleName} />);
    expect(screen.getByText('加载中...')).toBeInTheDocument();
  });

  it('应该处理加载错误', async () => {
    (fetch as any).mockRejectedValue(new Error('网络错误'));
    
    render(<${moduleName} />);
    
    await waitFor(() => {
      expect(screen.getByText('加载错误')).toBeInTheDocument();
      expect(screen.getByText('网络错误')).toBeInTheDocument();
    });
  });

  it('应该成功加载数据', async () => {
    const mockData = [
      { id: '1', name: '测试项目1' },
      { id: '2', name: '测试项目2' }
    ];
    
    (fetch as any).mockResolvedValue({
      json: () => Promise.resolve(mockData)
    });
    
    render(<${moduleName} />);
    
    await waitFor(() => {
      expect(screen.getByText('测试项目1')).toBeInTheDocument();
      expect(screen.getByText('测试项目2')).toBeInTheDocument();
    });
  });

  it('应该支持刷新功能', async () => {
    (fetch as any).mockResolvedValue({
      json: () => Promise.resolve([])
    });
    
    render(<${moduleName} />);
    
    const refreshButton = screen.getByText('刷新');
    fireEvent.click(refreshButton);
    
    expect(fetch).toHaveBeenCalledTimes(2); // 初始加载 + 刷新
  });

  it('应该调用onDataChange回调', async () => {
    const mockData = [{ id: '1', name: '测试' }];
    const onDataChange = vi.fn();
    
    (fetch as any).mockResolvedValue({
      json: () => Promise.resolve(mockData)
    });
    
    render(<${moduleName} onDataChange={onDataChange} />);
    
    await waitFor(() => {
      expect(onDataChange).toHaveBeenCalledWith(mockData);
    });
  });
});`;

  return [
    {
      filename: `${moduleName}.test.tsx`,
      content: testCode,
      language: 'typescript',
      description: `${moduleName}组件的完整测试套件`
    }
  ];
};

// 主代码生成器组件
export const CodeGenerator: React.FC = () => {
  const [selectedTemplate, setSelectedTemplate] = useState<CodeTemplate | null>(null);
  const [generatedCode, setGeneratedCode] = useState<GeneratedCode[]>([]);
  const [options, setOptions] = useState<any>({});
  const [copiedFile, setCopiedFile] = useState<string | null>(null);

  const handleGenerateCode = () => {
    if (!selectedTemplate) return;
    
    const code = generateCode(selectedTemplate.id, options);
    setGeneratedCode(code);
  };

  const copyToClipboard = async (content: string, filename: string) => {
    try {
      await navigator.clipboard.writeText(content);
      setCopiedFile(filename);
      setTimeout(() => setCopiedFile(null), 2000);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  const downloadFile = (file: GeneratedCode) => {
    const blob = new Blob([file.content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = file.filename;
    link.click();
    URL.revokeObjectURL(url);
  };

  const getCategoryIcon = (category: CodeTemplate['category']) => {
    switch (category) {
      case 'component': return <Code size={16} className="text-blue-500" />;
      case 'api': return <Globe size={16} className="text-green-500" />;
      case 'database': return <Database size={16} className="text-purple-500" />;
      case 'config': return <Settings size={16} className="text-gray-500" />;
      case 'test': return <CheckCircle size={16} className="text-orange-500" />;
    }
  };

  const getLanguageIcon = (language: string) => {
    switch (language) {
      case 'typescript': return <Code size={16} className="text-blue-500" />;
      case 'sql': return <Database size={16} className="text-purple-500" />;
      case 'toml': return <Settings size={16} className="text-gray-500" />;
      default: return <FileText size={16} className="text-gray-500" />;
    }
  };

  return (
    <div className="h-full bg-white border border-gray-200 rounded-lg flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-bold flex items-center gap-2">
          <Code size={20} className="text-blue-500" />
          AI代码生成器
        </h3>
        <p className="text-sm text-gray-600 mt-1">基于模块需求自动生成代码模板</p>
      </div>

      <div className="flex-1 overflow-hidden flex">
        {/* 左侧模板选择 */}
        <div className="w-1/3 border-r border-gray-200 overflow-y-auto">
          <div className="p-4">
            <h4 className="font-medium text-gray-900 mb-3">选择代码模板</h4>
            <div className="space-y-2">
              {codeTemplates.map((template) => (
                <button
                  key={template.id}
                  onClick={() => setSelectedTemplate(template)}
                  className={clsx(
                    'w-full text-left p-3 rounded-lg border transition-colors',
                    selectedTemplate?.id === template.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:bg-gray-50'
                  )}
                >
                  <div className="flex items-center gap-2 mb-1">
                    {getCategoryIcon(template.category)}
                    <span className="font-medium text-sm">{template.name}</span>
                  </div>
                  <p className="text-xs text-gray-600 mb-2">{template.description}</p>
                  <div className="flex items-center gap-2 text-xs text-gray-500">
                    <span className="px-2 py-1 bg-gray-100 rounded">{template.framework}</span>
                    <span>{template.estimatedTime}</span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* 右侧配置和生成 */}
        <div className="flex-1 flex flex-col">
          {selectedTemplate ? (
            <>
              {/* 配置选项 */}
              <div className="p-4 border-b border-gray-200">
                <h4 className="font-medium text-gray-900 mb-3">配置选项</h4>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      模块名称
                    </label>
                    <input
                      type="text"
                      value={options.moduleName || ''}
                      onChange={(e) => setOptions(prev => ({ ...prev, moduleName: e.target.value }))}
                      placeholder="例如: UserAuth"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  {selectedTemplate.category === 'component' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        模块描述
                      </label>
                      <input
                        type="text"
                        value={options.moduleDescription || ''}
                        onChange={(e) => setOptions(prev => ({ ...prev, moduleDescription: e.target.value }))}
                        placeholder="例如: 用户认证管理"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  )}
                </div>
                
                <button
                  onClick={handleGenerateCode}
                  disabled={!options.moduleName}
                  className="mt-4 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                >
                  <Play size={16} />
                  生成代码
                </button>
              </div>

              {/* 生成的代码 */}
              <div className="flex-1 overflow-y-auto p-4">
                {generatedCode.length > 0 ? (
                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">生成的代码文件</h4>
                    {generatedCode.map((file, index) => (
                      <div key={index} className="border border-gray-200 rounded-lg">
                        <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
                          <div className="flex items-center gap-2">
                            {getLanguageIcon(file.language)}
                            <span className="font-medium text-gray-900">{file.filename}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => copyToClipboard(file.content, file.filename)}
                              className="p-1 text-gray-600 hover:text-gray-800"
                              title="复制到剪贴板"
                            >
                              {copiedFile === file.filename ? (
                                <CheckCircle size={16} className="text-green-500" />
                              ) : (
                                <Copy size={16} />
                              )}
                            </button>
                            <button
                              onClick={() => downloadFile(file)}
                              className="p-1 text-gray-600 hover:text-gray-800"
                              title="下载文件"
                            >
                              <Download size={16} />
                            </button>
                          </div>
                        </div>
                        <div className="p-3">
                          <p className="text-sm text-gray-600 mb-3">{file.description}</p>
                          <pre className="text-sm bg-gray-50 p-3 rounded overflow-x-auto">
                            <code>{file.content}</code>
                          </pre>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-gray-500 py-8">
                    <Code size={48} className="mx-auto mb-4 text-gray-300" />
                    <p>配置选项并点击生成代码</p>
                  </div>
                )}
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center text-gray-500">
                <Code size={48} className="mx-auto mb-4 text-gray-300" />
                <p>选择一个代码模板开始生成</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
