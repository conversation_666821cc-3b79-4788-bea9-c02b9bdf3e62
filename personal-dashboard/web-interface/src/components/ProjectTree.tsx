import React, { useState } from 'react';
import { ChevronDown, ChevronRight, Circle, CheckCircle, Clock, AlertCircle } from 'lucide-react';
import clsx from 'clsx';

// 模块状态类型定义
type ModuleStatus = 'planned' | 'active' | 'testing' | 'completed' | 'blocked' | 'paused';
type ModulePriority = 'high' | 'medium' | 'low';

interface TreeModule {
  id: string;
  name: string;
  description: string;
  status: ModuleStatus;
  priority: ModulePriority;
  progress: number;
  children?: TreeModule[];
  estimatedHours?: number;
  actualHours?: number;
}

// 示例数据
const treeData: TreeModule = {
  id: 'ROOT',
  name: 'Personal Dashboard',
  description: '个人仪表板项目',
  status: 'active',
  priority: 'high',
  progress: 15,
  children: [
    {
      id: 'A',
      name: '用户模块',
      description: '用户认证和个人信息管理',
      status: 'active',
      priority: 'high',
      progress: 30,
      children: [
        {
          id: 'A1',
          name: '登录认证',
          description: '用户登录、注册、密码重置功能',
          status: 'active',
          priority: 'high',
          progress: 60,
          estimatedHours: 16,
          actualHours: 8
        },
        {
          id: 'A2',
          name: '个人资料',
          description: '用户个人信息查看和编辑',
          status: 'planned',
          priority: 'medium',
          progress: 0,
          estimatedHours: 12,
          actualHours: 0
        }
      ]
    },
    {
      id: 'B',
      name: '数据展示',
      description: '各类信息展示和数据可视化',
      status: 'planned',
      priority: 'high',
      progress: 0,
      children: [
        {
          id: 'B1',
          name: '天气信息',
          description: '获取和展示当前天气信息',
          status: 'planned',
          priority: 'medium',
          progress: 0,
          estimatedHours: 8,
          actualHours: 0
        },
        {
          id: 'B2',
          name: '待办事项',
          description: '个人任务管理和待办清单',
          status: 'planned',
          priority: 'high',
          progress: 0,
          estimatedHours: 20,
          actualHours: 0
        },
        {
          id: 'B3',
          name: '日历视图',
          description: '日程安排和日历展示',
          status: 'planned',
          priority: 'low',
          progress: 0,
          estimatedHours: 16,
          actualHours: 0
        }
      ]
    },
    {
      id: 'C',
      name: '设置管理',
      description: '应用设置和个性化配置',
      status: 'planned',
      priority: 'medium',
      progress: 0,
      children: [
        {
          id: 'C1',
          name: '主题设置',
          description: '明暗主题切换和界面个性化',
          status: 'planned',
          priority: 'low',
          progress: 0,
          estimatedHours: 6,
          actualHours: 0
        },
        {
          id: 'C2',
          name: '通知配置',
          description: '通知偏好和提醒设置',
          status: 'planned',
          priority: 'low',
          progress: 0,
          estimatedHours: 8,
          actualHours: 0
        }
      ]
    }
  ]
};

// 状态图标组件
const StatusIcon: React.FC<{ status: ModuleStatus }> = ({ status }) => {
  const iconProps = { size: 16 };
  
  switch (status) {
    case 'completed':
      return <CheckCircle {...iconProps} className="text-green-500" />;
    case 'active':
      return <Clock {...iconProps} className="text-blue-500" />;
    case 'blocked':
      return <AlertCircle {...iconProps} className="text-red-500" />;
    case 'paused':
      return <Circle {...iconProps} className="text-yellow-500" />;
    default:
      return <Circle {...iconProps} className="text-gray-400" />;
  }
};

// 优先级标签组件
const PriorityBadge: React.FC<{ priority: ModulePriority }> = ({ priority }) => {
  const colors = {
    high: 'bg-red-100 text-red-800 border-red-200',
    medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    low: 'bg-gray-100 text-gray-800 border-gray-200'
  };
  
  return (
    <span className={clsx(
      'px-2 py-1 text-xs font-medium rounded-full border',
      colors[priority]
    )}>
      {priority}
    </span>
  );
};

// 进度条组件
const ProgressBar: React.FC<{ progress: number }> = ({ progress }) => {
  return (
    <div className="w-full bg-gray-200 rounded-full h-2">
      <div 
        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
        style={{ width: `${progress}%` }}
      />
    </div>
  );
};

// 树节点组件
const TreeNode: React.FC<{ 
  module: TreeModule; 
  level: number;
  onModuleClick: (module: TreeModule) => void;
}> = ({ module, level, onModuleClick }) => {
  const [isExpanded, setIsExpanded] = useState(level < 2);
  const hasChildren = module.children && module.children.length > 0;
  
  return (
    <div className="select-none">
      <div 
        className={clsx(
          'flex items-center py-2 px-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors',
          level === 0 && 'bg-blue-50 border border-blue-200'
        )}
        style={{ marginLeft: `${level * 20}px` }}
        onClick={() => onModuleClick(module)}
      >
        {/* 展开/收起按钮 */}
        <button
          className="mr-2 p-1 hover:bg-gray-200 rounded"
          onClick={(e) => {
            e.stopPropagation();
            setIsExpanded(!isExpanded);
          }}
        >
          {hasChildren ? (
            isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />
          ) : (
            <div className="w-4 h-4" />
          )}
        </button>
        
        {/* 状态图标 */}
        <StatusIcon status={module.status} />
        
        {/* 模块信息 */}
        <div className="flex-1 ml-3">
          <div className="flex items-center gap-2">
            <span className="font-medium text-gray-900">{module.name}</span>
            <PriorityBadge priority={module.priority} />
          </div>
          <p className="text-sm text-gray-600 mt-1">{module.description}</p>
          
          {/* 进度信息 */}
          <div className="flex items-center gap-4 mt-2">
            <div className="flex-1">
              <ProgressBar progress={module.progress} />
            </div>
            <span className="text-sm text-gray-500">{module.progress}%</span>
            {module.estimatedHours && (
              <span className="text-sm text-gray-500">
                {module.actualHours || 0}h / {module.estimatedHours}h
              </span>
            )}
          </div>
        </div>
      </div>
      
      {/* 子节点 */}
      {hasChildren && isExpanded && (
        <div className="mt-1">
          {module.children!.map((child) => (
            <TreeNode
              key={child.id}
              module={child}
              level={level + 1}
              onModuleClick={onModuleClick}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// 主组件
export const ProjectTree: React.FC = () => {
  const [selectedModule, setSelectedModule] = useState<TreeModule | null>(null);

  const handleModuleClick = (module: TreeModule) => {
    setSelectedModule(module);
  };

  return (
    <div className="flex h-full bg-white">
      {/* 左侧树结构 */}
      <div className="w-1/2 p-4 border-r border-gray-200 overflow-y-auto">
        <div className="mb-4">
          <h2 className="text-xl font-bold text-gray-900">项目树结构</h2>
          <p className="text-sm text-gray-600 mt-1">Personal Dashboard - Project Tree 验证</p>
        </div>
        <TreeNode
          module={treeData}
          level={0}
          onModuleClick={handleModuleClick}
        />
      </div>

      {/* 右侧详情面板 */}
      <div className="w-1/2 p-4 overflow-y-auto">
        {selectedModule ? (
          <div>
            <div className="border-b border-gray-200 pb-4 mb-4">
              <h3 className="text-lg font-bold text-gray-900">{selectedModule.name}</h3>
              <p className="text-gray-600 mt-1">{selectedModule.description}</p>
              <p className="text-sm text-gray-500 mt-2">模块ID: {selectedModule.id}</p>
            </div>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">状态</label>
                <div className="flex items-center gap-2">
                  <StatusIcon status={selectedModule.status} />
                  <span className="capitalize text-gray-900">{selectedModule.status}</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">优先级</label>
                <PriorityBadge priority={selectedModule.priority} />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">开发进度</label>
                <div className="space-y-2">
                  <ProgressBar progress={selectedModule.progress} />
                  <span className="text-sm text-gray-600">
                    {selectedModule.progress}% 完成
                  </span>
                </div>
              </div>

              {selectedModule.estimatedHours && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">工时统计</label>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">预估工时:</span>
                      <span className="font-medium">{selectedModule.estimatedHours}h</span>
                    </div>
                    <div className="flex justify-between text-sm mt-1">
                      <span className="text-gray-600">实际工时:</span>
                      <span className="font-medium">{selectedModule.actualHours || 0}h</span>
                    </div>
                    <div className="flex justify-between text-sm mt-1 pt-2 border-t border-gray-200">
                      <span className="text-gray-600">剩余工时:</span>
                      <span className="font-medium">
                        {selectedModule.estimatedHours - (selectedModule.actualHours || 0)}h
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {selectedModule.children && selectedModule.children.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">子模块</label>
                  <div className="space-y-2">
                    {selectedModule.children.map((child) => (
                      <div key={child.id} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                        <StatusIcon status={child.status} />
                        <span className="text-sm font-medium">{child.name}</span>
                        <span className="text-xs text-gray-500 ml-auto">{child.progress}%</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-500">
              <Circle size={48} className="mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium">选择模块</p>
              <p className="text-sm">点击左侧模块查看详细信息</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
