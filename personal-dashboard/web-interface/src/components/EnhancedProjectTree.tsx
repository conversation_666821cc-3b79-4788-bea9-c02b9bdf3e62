import React, { useState } from 'react';
import { History, Download, Upload, RotateCcw, GitBranch } from 'lucide-react';
import { 
  useTreeManager, 
  EditModuleDialog, 
  DraggableTreeNode,
  TreeModule,
  ChangeRecord 
} from './DynamicProjectTree';

// 示例数据
const initialTreeData: TreeModule = {
  id: 'ROOT',
  name: 'Personal Dashboard',
  description: '个人仪表板项目',
  status: 'active',
  priority: 'high',
  progress: 25,
  children: [
    {
      id: 'A',
      name: '用户模块',
      description: '用户认证和个人信息管理',
      status: 'active',
      priority: 'high',
      progress: 40,
      parent: 'ROOT',
      children: [
        {
          id: 'A1',
          name: '登录认证',
          description: '用户登录、注册、密码重置功能',
          status: 'active',
          priority: 'high',
          progress: 70,
          estimatedHours: 16,
          actualHours: 10,
          parent: 'A'
        },
        {
          id: 'A2',
          name: '个人资料',
          description: '用户个人信息查看和编辑',
          status: 'planned',
          priority: 'medium',
          progress: 0,
          estimatedHours: 12,
          actualHours: 0,
          parent: 'A'
        }
      ]
    },
    {
      id: 'B',
      name: '数据展示',
      description: '各类信息展示和数据可视化',
      status: 'planned',
      priority: 'high',
      progress: 10,
      parent: 'ROOT',
      children: [
        {
          id: 'B1',
          name: '天气信息',
          description: '获取和展示当前天气信息',
          status: 'planned',
          priority: 'medium',
          progress: 0,
          estimatedHours: 8,
          actualHours: 0,
          parent: 'B'
        },
        {
          id: 'B2',
          name: '待办事项',
          description: '个人任务管理和待办清单',
          status: 'planned',
          priority: 'high',
          progress: 20,
          estimatedHours: 20,
          actualHours: 2,
          parent: 'B'
        }
      ]
    }
  ]
};

// 变更历史组件
const ChangeHistoryPanel: React.FC<{ 
  changes: ChangeRecord[];
  onRevert?: (changeId: string) => void;
}> = ({ changes, onRevert }) => {
  const getActionColor = (action: ChangeRecord['action']) => {
    switch (action) {
      case 'create': return 'text-green-600 bg-green-50';
      case 'update': return 'text-blue-600 bg-blue-50';
      case 'delete': return 'text-red-600 bg-red-50';
      case 'move': return 'text-purple-600 bg-purple-50';
      case 'split': return 'text-orange-600 bg-orange-50';
      case 'merge': return 'text-indigo-600 bg-indigo-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getActionIcon = (action: ChangeRecord['action']) => {
    switch (action) {
      case 'create': return '+';
      case 'update': return '✏️';
      case 'delete': return '🗑️';
      case 'move': return '↔️';
      case 'split': return '🔀';
      case 'merge': return '🔗';
      default: return '•';
    }
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-bold flex items-center gap-2">
          <History size={20} />
          变更历史
        </h3>
        <p className="text-sm text-gray-600 mt-1">最近的项目结构变更记录</p>
      </div>
      
      <div className="p-4">
        {changes.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <History size={48} className="mx-auto mb-4 text-gray-300" />
            <p>暂无变更记录</p>
          </div>
        ) : (
          <div className="space-y-3">
            {changes.map((change) => (
              <div key={change.id} className="border border-gray-200 rounded-lg p-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className={`px-2 py-1 text-xs font-medium rounded ${getActionColor(change.action)}`}>
                        {getActionIcon(change.action)} {change.action.toUpperCase()}
                      </span>
                      <span className="text-sm text-gray-500">
                        {new Date(change.timestamp).toLocaleString()}
                      </span>
                    </div>
                    <p className="text-sm text-gray-900">{change.details}</p>
                    <p className="text-xs text-gray-500 mt-1">模块ID: {change.moduleId}</p>
                  </div>
                  {onRevert && (
                    <button
                      onClick={() => onRevert(change.id)}
                      className="ml-2 p-1 text-gray-400 hover:text-gray-600"
                      title="撤销此变更"
                    >
                      <RotateCcw size={16} />
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// 添加模块对话框
const AddModuleDialog: React.FC<{
  isOpen: boolean;
  parentId: string;
  onClose: () => void;
  onAdd: (parentId: string, module: Omit<TreeModule, 'id'>) => void;
}> = ({ isOpen, parentId, onClose, onAdd }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    status: 'planned' as const,
    priority: 'medium' as const,
    progress: 0,
    estimatedHours: 0
  });

  if (!isOpen) return null;

  const handleAdd = () => {
    if (formData.name.trim()) {
      onAdd(parentId, {
        ...formData,
        children: []
      });
      setFormData({
        name: '',
        description: '',
        status: 'planned',
        priority: 'medium',
        progress: 0,
        estimatedHours: 0
      });
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-96">
        <h3 className="text-lg font-bold mb-4">添加新模块</h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">模块名称 *</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="输入模块名称"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">描述</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="输入模块描述"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">优先级</label>
              <select
                value={formData.priority}
                onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="high">高</option>
                <option value="medium">中</option>
                <option value="low">低</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">预估工时</label>
              <input
                type="number"
                min="0"
                value={formData.estimatedHours}
                onChange={(e) => setFormData(prev => ({ ...prev, estimatedHours: parseInt(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-2 mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            取消
          </button>
          <button
            onClick={handleAdd}
            disabled={!formData.name.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            添加
          </button>
        </div>
      </div>
    </div>
  );
};

// 主组件
export const EnhancedProjectTree: React.FC = () => {
  const {
    treeData,
    changeHistory,
    draggedModule,
    setDraggedModule,
    updateModule,
    addModule,
    deleteModule,
    moveModule
  } = useTreeManager(initialTreeData);

  const [selectedModule, setSelectedModule] = useState<TreeModule | null>(null);
  const [editingModule, setEditingModule] = useState<TreeModule | null>(null);
  const [addingToParent, setAddingToParent] = useState<string | null>(null);
  const [showHistory, setShowHistory] = useState(false);

  const handleModuleClick = (module: TreeModule) => {
    setSelectedModule(module);
  };

  const handleEditModule = (module: TreeModule) => {
    setEditingModule(module);
  };

  const handleSaveModule = (updates: Partial<TreeModule>) => {
    if (editingModule) {
      updateModule(editingModule.id, updates);
      setEditingModule(null);
    }
  };

  const handleDeleteModule = (id: string) => {
    if (confirm('确定要删除这个模块吗？此操作不可撤销。')) {
      deleteModule(id);
      if (selectedModule?.id === id) {
        setSelectedModule(null);
      }
    }
  };

  const handleAddChild = (parentId: string) => {
    setAddingToParent(parentId);
  };

  const exportTreeData = () => {
    const dataStr = JSON.stringify(treeData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `project-tree-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="flex h-full bg-white">
      {/* 左侧树结构 */}
      <div className="w-1/2 border-r border-gray-200 flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-xl font-bold text-gray-900">动态项目树</h2>
            <div className="flex gap-2">
              <button
                onClick={() => setShowHistory(!showHistory)}
                className={`p-2 rounded-md transition-colors ${
                  showHistory ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'
                }`}
                title="变更历史"
              >
                <History size={20} />
              </button>
              <button
                onClick={exportTreeData}
                className="p-2 text-gray-600 hover:bg-gray-100 rounded-md"
                title="导出数据"
              >
                <Download size={20} />
              </button>
            </div>
          </div>
          <p className="text-sm text-gray-600">支持拖拽重组、实时编辑、变更追踪</p>
        </div>
        
        <div className="flex-1 overflow-y-auto p-4">
          <DraggableTreeNode
            module={treeData}
            level={0}
            onModuleClick={handleModuleClick}
            onEditModule={handleEditModule}
            onDeleteModule={handleDeleteModule}
            onAddChild={handleAddChild}
            draggedModule={draggedModule}
            setDraggedModule={setDraggedModule}
            onMoveModule={moveModule}
          />
        </div>
      </div>

      {/* 右侧详情/历史面板 */}
      <div className="w-1/2">
        {showHistory ? (
          <ChangeHistoryPanel changes={changeHistory} />
        ) : (
          <div className="p-4 overflow-y-auto h-full">
            {selectedModule ? (
              <div>
                <div className="border-b border-gray-200 pb-4 mb-4">
                  <h3 className="text-lg font-bold text-gray-900">{selectedModule.name}</h3>
                  <p className="text-gray-600 mt-1">{selectedModule.description}</p>
                  <p className="text-sm text-gray-500 mt-2">模块ID: {selectedModule.id}</p>
                </div>
                
                {/* 模块详情内容保持不变 */}
                <div className="space-y-6">
                  {/* 这里可以添加更多详情内容 */}
                  <div className="text-center text-gray-500">
                    <p>模块详情面板</p>
                    <p className="text-sm mt-1">点击编辑按钮修改模块信息</p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center text-gray-500">
                  <GitBranch size={48} className="mx-auto mb-4 text-gray-300" />
                  <p className="text-lg font-medium">动态项目管理</p>
                  <p className="text-sm">拖拽模块重组结构，点击查看详情</p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* 对话框 */}
      <EditModuleDialog
        module={editingModule}
        isOpen={!!editingModule}
        onClose={() => setEditingModule(null)}
        onSave={handleSaveModule}
      />

      <AddModuleDialog
        isOpen={!!addingToParent}
        parentId={addingToParent || ''}
        onClose={() => setAddingToParent(null)}
        onAdd={addModule}
      />
    </div>
  );
};
