import React, { useState } from 'react';
import { 
  Settings, 
  Download, 
  Copy, 
  CheckCircle, 
  FileText, 
  Code, 
  Database,
  Key,
  Globe,
  Zap
} from 'lucide-react';
import clsx from 'clsx';

// 部署配置类型
interface DeploymentConfig {
  projectName: string;
  environment: 'development' | 'staging' | 'production';
  services: {
    workers: boolean;
    pages: boolean;
    d1: boolean;
    kv: boolean;
    r2: boolean;
  };
  customDomain?: string;
  environmentVars: Record<string, string>;
}

// 生成的配置文件
interface GeneratedConfig {
  filename: string;
  content: string;
  type: 'toml' | 'json' | 'yaml' | 'js';
}

const DeploymentConfigGenerator: React.FC = () => {
  const [config, setConfig] = useState<DeploymentConfig>({
    projectName: 'personal-dashboard',
    environment: 'development',
    services: {
      workers: true,
      pages: true,
      d1: true,
      kv: true,
      r2: false
    },
    environmentVars: {
      'ENVIRONMENT': 'development',
      'API_BASE_URL': 'http://localhost:8787/api',
      'DEBUG': 'true'
    }
  });

  const [generatedConfigs, setGeneratedConfigs] = useState<GeneratedConfig[]>([]);
  const [copiedConfig, setCopiedConfig] = useState<string | null>(null);

  // 生成wrangler.toml配置
  const generateWranglerConfig = (config: DeploymentConfig): string => {
    const envSuffix = config.environment === 'production' ? '' : `-${config.environment}`;
    
    let tomlContent = `# Cloudflare Workers configuration
name = "${config.projectName}${envSuffix}"
main = "src/worker.js"
compatibility_date = "2023-07-10"
compatibility_flags = ["nodejs_compat"]

`;

    // 环境变量
    if (Object.keys(config.environmentVars).length > 0) {
      tomlContent += `[vars]\n`;
      Object.entries(config.environmentVars).forEach(([key, value]) => {
        tomlContent += `${key} = "${value}"\n`;
      });
      tomlContent += '\n';
    }

    // KV命名空间
    if (config.services.kv) {
      tomlContent += `[[kv_namespaces]]
binding = "USER_KV"
id = "your-user-kv-namespace-id"
preview_id = "your-user-kv-preview-id"

[[kv_namespaces]]
binding = "CACHE_KV"
id = "your-cache-kv-namespace-id"
preview_id = "your-cache-kv-preview-id"

`;
    }

    // D1数据库
    if (config.services.d1) {
      tomlContent += `[[d1_databases]]
binding = "DB"
database_name = "${config.projectName}_db${envSuffix}"
database_id = "your-d1-database-id"

`;
    }

    // R2存储桶
    if (config.services.r2) {
      tomlContent += `[[r2_buckets]]
binding = "BUCKET"
bucket_name = "${config.projectName}-bucket${envSuffix}"

`;
    }

    // 自定义域名
    if (config.customDomain && config.environment === 'production') {
      tomlContent += `[env.production]
route = { pattern = "${config.customDomain}/api/*", zone_name = "${config.customDomain.split('.').slice(-2).join('.')}" }

`;
    }

    return tomlContent;
  };

  // 生成package.json脚本
  const generatePackageScripts = (config: DeploymentConfig): string => {
    const scripts = {
      "dev": "concurrently \"npm run dev:worker\" \"npm run dev:web\"",
      "dev:worker": "wrangler dev",
      "dev:web": "cd web-interface && npm run dev",
      "build": "npm run build:worker && npm run build:web",
      "build:worker": "esbuild src/worker.ts --bundle --format=esm --outfile=dist/worker.js --platform=neutral --target=es2022",
      "build:web": "cd web-interface && npm run build",
      "deploy": `npm run deploy:${config.environment}`,
      [`deploy:${config.environment}`]: config.environment === 'production' 
        ? "npm run build && wrangler deploy --env production"
        : `npm run build && wrangler deploy --env ${config.environment}`,
      "test": "vitest",
      "lint": "eslint src --ext .ts,.js",
      "type-check": "tsc --noEmit"
    };

    if (config.services.d1) {
      scripts["db:migrate"] = `wrangler d1 migrations apply ${config.projectName}_db${config.environment === 'production' ? '' : `_${config.environment}`}`;
      scripts["db:seed"] = `wrangler d1 execute ${config.projectName}_db${config.environment === 'production' ? '' : `_${config.environment}`} --file=./database/seed.sql`;
    }

    return JSON.stringify({ scripts }, null, 2);
  };

  // 生成GitHub Actions工作流
  const generateGitHubActions = (config: DeploymentConfig): string => {
    return `name: Deploy to Cloudflare ${config.environment}

on:
  push:
    branches: [${config.environment === 'production' ? 'main' : config.environment}]
  pull_request:
    branches: [${config.environment === 'production' ? 'main' : config.environment}]

jobs:
  deploy:
    runs-on: ubuntu-latest
    name: Deploy
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run tests
        run: npm test
        
      - name: Build project
        run: npm run build
        
      - name: Deploy to Cloudflare
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: \${{ secrets.CLOUDFLARE_API_TOKEN }}
          environment: ${config.environment}
          ${config.services.d1 ? `
          preCommands: |
            wrangler d1 migrations apply ${config.projectName}_db_${config.environment} --env ${config.environment}` : ''}
`;
  };

  // 生成环境配置文件
  const generateEnvConfig = (config: DeploymentConfig): string => {
    const envConfig = {
      environment: {
        name: config.environment,
        description: `${config.environment} environment configuration`
      },
      cloudflare: {
        services: config.services,
        custom_domain: config.customDomain || null
      },
      application: {
        debug: config.environment !== 'production',
        log_level: config.environment === 'production' ? 'info' : 'debug',
        api_base_url: config.environmentVars.API_BASE_URL || '',
        cors: {
          enabled: true,
          origins: config.environment === 'production' 
            ? [config.customDomain || ''] 
            : ['http://localhost:5173', 'http://localhost:3000']
        }
      }
    };

    return JSON.stringify(envConfig, null, 2);
  };

  const generateConfigs = () => {
    const configs: GeneratedConfig[] = [
      {
        filename: 'wrangler.toml',
        content: generateWranglerConfig(config),
        type: 'toml'
      },
      {
        filename: 'package.json (scripts)',
        content: generatePackageScripts(config),
        type: 'json'
      },
      {
        filename: `.github/workflows/deploy-${config.environment}.yml`,
        content: generateGitHubActions(config),
        type: 'yaml'
      },
      {
        filename: `environment-${config.environment}.json`,
        content: generateEnvConfig(config),
        type: 'json'
      }
    ];

    setGeneratedConfigs(configs);
  };

  const copyToClipboard = async (content: string, filename: string) => {
    try {
      await navigator.clipboard.writeText(content);
      setCopiedConfig(filename);
      setTimeout(() => setCopiedConfig(null), 2000);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  const downloadConfig = (config: GeneratedConfig) => {
    const blob = new Blob([config.content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = config.filename;
    link.click();
    URL.revokeObjectURL(url);
  };

  const getFileIcon = (type: GeneratedConfig['type']) => {
    switch (type) {
      case 'toml':
        return <Settings size={16} className="text-blue-500" />;
      case 'json':
        return <Code size={16} className="text-green-500" />;
      case 'yaml':
        return <FileText size={16} className="text-purple-500" />;
      case 'js':
        return <Zap size={16} className="text-yellow-500" />;
      default:
        return <FileText size={16} className="text-gray-500" />;
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200">
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-bold flex items-center gap-2">
          <Settings size={20} className="text-blue-500" />
          部署配置生成器
        </h3>
        <p className="text-sm text-gray-600 mt-1">自动生成Cloudflare部署配置文件</p>
      </div>

      <div className="p-4 space-y-6">
        {/* 配置表单 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">项目名称</label>
            <input
              type="text"
              value={config.projectName}
              onChange={(e) => setConfig(prev => ({ ...prev, projectName: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">环境</label>
            <select
              value={config.environment}
              onChange={(e) => setConfig(prev => ({ ...prev, environment: e.target.value as any }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="development">开发环境</option>
              <option value="staging">测试环境</option>
              <option value="production">生产环境</option>
            </select>
          </div>
        </div>

        {/* 服务选择 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">Cloudflare服务</label>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
            {Object.entries(config.services).map(([service, enabled]) => (
              <label key={service} className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={enabled}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    services: { ...prev.services, [service]: e.target.checked }
                  }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700 capitalize">{service}</span>
              </label>
            ))}
          </div>
        </div>

        {/* 自定义域名 */}
        {config.environment === 'production' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">自定义域名 (可选)</label>
            <input
              type="text"
              value={config.customDomain || ''}
              onChange={(e) => setConfig(prev => ({ ...prev, customDomain: e.target.value }))}
              placeholder="example.com"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        )}

        {/* 环境变量 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">环境变量</label>
          <div className="space-y-2">
            {Object.entries(config.environmentVars).map(([key, value]) => (
              <div key={key} className="grid grid-cols-2 gap-2">
                <input
                  type="text"
                  value={key}
                  onChange={(e) => {
                    const newVars = { ...config.environmentVars };
                    delete newVars[key];
                    newVars[e.target.value] = value;
                    setConfig(prev => ({ ...prev, environmentVars: newVars }));
                  }}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="变量名"
                />
                <input
                  type="text"
                  value={value}
                  onChange={(e) => setConfig(prev => ({
                    ...prev,
                    environmentVars: { ...prev.environmentVars, [key]: e.target.value }
                  }))}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="变量值"
                />
              </div>
            ))}
          </div>
        </div>

        {/* 生成按钮 */}
        <button
          onClick={generateConfigs}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 flex items-center justify-center gap-2"
        >
          <Settings size={20} />
          生成配置文件
        </button>

        {/* 生成的配置文件 */}
        {generatedConfigs.length > 0 && (
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">生成的配置文件</h4>
            {generatedConfigs.map((configFile, index) => (
              <div key={index} className="border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
                  <div className="flex items-center gap-2">
                    {getFileIcon(configFile.type)}
                    <span className="font-medium text-gray-900">{configFile.filename}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => copyToClipboard(configFile.content, configFile.filename)}
                      className="p-1 text-gray-600 hover:text-gray-800"
                      title="复制到剪贴板"
                    >
                      {copiedConfig === configFile.filename ? (
                        <CheckCircle size={16} className="text-green-500" />
                      ) : (
                        <Copy size={16} />
                      )}
                    </button>
                    <button
                      onClick={() => downloadConfig(configFile)}
                      className="p-1 text-gray-600 hover:text-gray-800"
                      title="下载文件"
                    >
                      <Download size={16} />
                    </button>
                  </div>
                </div>
                <div className="p-3">
                  <pre className="text-sm text-gray-800 bg-gray-50 p-3 rounded overflow-x-auto">
                    <code>{configFile.content}</code>
                  </pre>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export { DeploymentConfigGenerator };
