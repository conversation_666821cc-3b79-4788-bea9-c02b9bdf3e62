import React, { useState, useEffect } from 'react';
import { 
  Cloud, 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Monitor, 
  Database, 
  Zap,
  Globe,
  Shield,
  Activity,
  Settings,
  RefreshCw
} from 'lucide-react';
import clsx from 'clsx';

// 兼容性检查结果类型
interface CompatibilityCheck {
  id: string;
  name: string;
  category: 'runtime' | 'dependencies' | 'storage' | 'performance' | 'security';
  status: 'pass' | 'warning' | 'error';
  message: string;
  suggestion?: string;
  details?: string;
}

// Cloudflare服务状态
interface CloudflareService {
  name: string;
  status: 'operational' | 'degraded' | 'outage';
  region: string;
  responseTime: number;
  uptime: number;
}

// 兼容性检查器组件
const CompatibilityChecker: React.FC = () => {
  const [checks, setChecks] = useState<CompatibilityCheck[]>([]);
  const [isChecking, setIsChecking] = useState(false);

  const runCompatibilityChecks = async () => {
    setIsChecking(true);
    
    // 模拟兼容性检查
    const mockChecks: CompatibilityCheck[] = [
      {
        id: 'runtime-nodejs',
        name: 'Node.js API 兼容性',
        category: 'runtime',
        status: 'pass',
        message: '未检测到不兼容的Node.js API',
        details: '所有使用的API都与Cloudflare Workers兼容'
      },
      {
        id: 'runtime-memory',
        name: '内存使用检查',
        category: 'runtime',
        status: 'warning',
        message: '预估内存使用可能接近限制',
        suggestion: '考虑优化大型对象的使用',
        details: '当前预估: 95MB / 128MB限制'
      },
      {
        id: 'deps-bundle-size',
        name: '依赖包大小',
        category: 'dependencies',
        status: 'pass',
        message: '打包大小在限制范围内',
        details: '当前大小: 850KB / 1MB限制'
      },
      {
        id: 'deps-external',
        name: '外部依赖检查',
        category: 'dependencies',
        status: 'error',
        message: '检测到不兼容的依赖',
        suggestion: '替换为Web标准API或CF兼容库',
        details: 'fs, path 模块不支持'
      },
      {
        id: 'storage-d1',
        name: 'D1 数据库配置',
        category: 'storage',
        status: 'pass',
        message: 'D1数据库配置正确',
        details: '数据库绑定和查询语法检查通过'
      },
      {
        id: 'perf-cold-start',
        name: '冷启动性能',
        category: 'performance',
        status: 'warning',
        message: '冷启动时间可能较长',
        suggestion: '减少初始化代码复杂度',
        details: '预估冷启动时间: 45ms'
      }
    ];

    // 模拟检查延迟
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setChecks(mockChecks);
    setIsChecking(false);
  };

  useEffect(() => {
    runCompatibilityChecks();
  }, []);

  const getStatusIcon = (status: CompatibilityCheck['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircle size={20} className="text-green-500" />;
      case 'warning':
        return <AlertTriangle size={20} className="text-yellow-500" />;
      case 'error':
        return <XCircle size={20} className="text-red-500" />;
    }
  };

  const getCategoryIcon = (category: CompatibilityCheck['category']) => {
    switch (category) {
      case 'runtime':
        return <Zap size={16} className="text-blue-500" />;
      case 'dependencies':
        return <Database size={16} className="text-purple-500" />;
      case 'storage':
        return <Database size={16} className="text-green-500" />;
      case 'performance':
        return <Activity size={16} className="text-orange-500" />;
      case 'security':
        return <Shield size={16} className="text-red-500" />;
    }
  };

  const groupedChecks = checks.reduce((acc, check) => {
    if (!acc[check.category]) {
      acc[check.category] = [];
    }
    acc[check.category].push(check);
    return acc;
  }, {} as Record<string, CompatibilityCheck[]>);

  const categoryNames = {
    runtime: '运行时环境',
    dependencies: '依赖管理',
    storage: '存储服务',
    performance: '性能指标',
    security: '安全检查'
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200">
      <div className="p-4 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-bold flex items-center gap-2">
            <Cloud size={20} className="text-blue-500" />
            Cloudflare 兼容性检查
          </h3>
          <button
            onClick={runCompatibilityChecks}
            disabled={isChecking}
            className="flex items-center gap-2 px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            <RefreshCw size={16} className={isChecking ? 'animate-spin' : ''} />
            {isChecking ? '检查中...' : '重新检查'}
          </button>
        </div>
      </div>

      <div className="p-4">
        {isChecking ? (
          <div className="text-center py-8">
            <RefreshCw size={32} className="mx-auto mb-4 text-blue-500 animate-spin" />
            <p className="text-gray-600">正在检查Cloudflare兼容性...</p>
          </div>
        ) : (
          <div className="space-y-6">
            {Object.entries(groupedChecks).map(([category, categoryChecks]) => (
              <div key={category}>
                <h4 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                  {getCategoryIcon(category as CompatibilityCheck['category'])}
                  {categoryNames[category as keyof typeof categoryNames]}
                </h4>
                <div className="space-y-3">
                  {categoryChecks.map((check) => (
                    <div key={check.id} className="border border-gray-200 rounded-lg p-3">
                      <div className="flex items-start gap-3">
                        {getStatusIcon(check.status)}
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium text-gray-900">{check.name}</span>
                            <span className={clsx(
                              'px-2 py-1 text-xs font-medium rounded-full',
                              check.status === 'pass' && 'bg-green-100 text-green-800',
                              check.status === 'warning' && 'bg-yellow-100 text-yellow-800',
                              check.status === 'error' && 'bg-red-100 text-red-800'
                            )}>
                              {check.status === 'pass' && '通过'}
                              {check.status === 'warning' && '警告'}
                              {check.status === 'error' && '错误'}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{check.message}</p>
                          {check.suggestion && (
                            <p className="text-sm text-blue-600 mb-2">
                              💡 建议: {check.suggestion}
                            </p>
                          )}
                          {check.details && (
                            <p className="text-xs text-gray-500">{check.details}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// Cloudflare服务状态监控
const ServiceStatusMonitor: React.FC = () => {
  const [services, setServices] = useState<CloudflareService[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // 模拟获取服务状态
    const mockServices: CloudflareService[] = [
      {
        name: 'Cloudflare Workers',
        status: 'operational',
        region: 'Global',
        responseTime: 45,
        uptime: 99.98
      },
      {
        name: 'Cloudflare Pages',
        status: 'operational',
        region: 'Global',
        responseTime: 120,
        uptime: 99.95
      },
      {
        name: 'Cloudflare D1',
        status: 'operational',
        region: 'Global',
        responseTime: 25,
        uptime: 99.92
      },
      {
        name: 'Cloudflare KV',
        status: 'degraded',
        region: 'Asia Pacific',
        responseTime: 180,
        uptime: 99.85
      }
    ];

    setTimeout(() => {
      setServices(mockServices);
      setIsLoading(false);
    }, 1000);
  }, []);

  const getStatusColor = (status: CloudflareService['status']) => {
    switch (status) {
      case 'operational':
        return 'text-green-600 bg-green-100';
      case 'degraded':
        return 'text-yellow-600 bg-yellow-100';
      case 'outage':
        return 'text-red-600 bg-red-100';
    }
  };

  const getStatusText = (status: CloudflareService['status']) => {
    switch (status) {
      case 'operational':
        return '正常';
      case 'degraded':
        return '降级';
      case 'outage':
        return '故障';
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200">
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-bold flex items-center gap-2">
          <Monitor size={20} className="text-green-500" />
          服务状态监控
        </h3>
        <p className="text-sm text-gray-600 mt-1">实时监控Cloudflare服务状态</p>
      </div>

      <div className="p-4">
        {isLoading ? (
          <div className="text-center py-8">
            <Activity size={32} className="mx-auto mb-4 text-blue-500 animate-pulse" />
            <p className="text-gray-600">正在获取服务状态...</p>
          </div>
        ) : (
          <div className="space-y-3">
            {services.map((service, index) => (
              <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                <div className="flex items-center gap-3">
                  <Globe size={20} className="text-blue-500" />
                  <div>
                    <h4 className="font-medium text-gray-900">{service.name}</h4>
                    <p className="text-sm text-gray-500">{service.region}</p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <div className="text-right">
                    <p className="text-sm text-gray-600">响应时间</p>
                    <p className="font-medium">{service.responseTime}ms</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-600">可用性</p>
                    <p className="font-medium">{service.uptime}%</p>
                  </div>
                  <span className={clsx(
                    'px-3 py-1 text-sm font-medium rounded-full',
                    getStatusColor(service.status)
                  )}>
                    {getStatusText(service.status)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// 主集成组件
export const CloudflareIntegration: React.FC = () => {
  return (
    <div className="space-y-6">
      <CompatibilityChecker />
      <ServiceStatusMonitor />
    </div>
  );
};
