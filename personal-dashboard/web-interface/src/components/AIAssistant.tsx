import React, { useState, useEffect } from 'react';
import {
  Brain,
  Lightbulb,
  Code,
  FileText,
  AlertTriangle,
  CheckCircle,
  Zap,
  GitBranch,
  Settings,
  MessageSquare,
  Send,
  Sparkles
} from 'lucide-react';
import clsx from 'clsx';
import { TreeModule } from './DynamicProjectTree';
import { CodeGenerator } from './CodeGenerator';

// AI建议类型
interface AISuggestion {
  id: string;
  type: 'optimization' | 'refactor' | 'split' | 'merge' | 'dependency' | 'performance' | 'security';
  title: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  confidence: number;
  moduleId?: string;
  actionable: boolean;
  estimatedTime?: string;
  benefits?: string[];
  risks?: string[];
}

// AI聊天消息
interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: string;
  suggestions?: AISuggestion[];
}

// 智能建议生成器
const generateSmartSuggestions = (treeData: TreeModule): AISuggestion[] => {
  const suggestions: AISuggestion[] = [];

  // 模拟AI分析项目结构并生成建议
  suggestions.push({
    id: 'suggest-1',
    type: 'split',
    title: '建议拆分A1登录认证模块',
    description: 'A1模块包含登录、注册、密码重置等多个功能，建议拆分为独立的子模块以提高可维护性。',
    impact: 'medium',
    confidence: 85,
    moduleId: 'A1',
    actionable: true,
    estimatedTime: '2-3小时',
    benefits: [
      '提高代码可维护性',
      '便于独立测试',
      '支持并行开发'
    ],
    risks: [
      '增加模块间通信复杂度',
      '需要重新设计接口'
    ]
  });

  suggestions.push({
    id: 'suggest-2',
    type: 'optimization',
    title: '优化B2待办事项模块性能',
    description: '检测到B2模块可能存在性能瓶颈，建议添加缓存机制和分页功能。',
    impact: 'high',
    confidence: 78,
    moduleId: 'B2',
    actionable: true,
    estimatedTime: '4-6小时',
    benefits: [
      '提升用户体验',
      '减少服务器负载',
      '支持大量数据'
    ]
  });

  suggestions.push({
    id: 'suggest-3',
    type: 'dependency',
    title: '更新Cloudflare Workers依赖',
    description: '发现新版本的@cloudflare/workers-types，建议更新以获得最新的类型支持。',
    impact: 'low',
    confidence: 95,
    actionable: true,
    estimatedTime: '30分钟',
    benefits: [
      '获得最新API支持',
      '改善开发体验',
      '修复已知问题'
    ]
  });

  suggestions.push({
    id: 'suggest-4',
    type: 'security',
    title: '加强用户认证安全性',
    description: '建议在A1模块中添加多因素认证和登录失败限制功能。',
    impact: 'high',
    confidence: 90,
    moduleId: 'A1',
    actionable: true,
    estimatedTime: '1-2天',
    benefits: [
      '提高系统安全性',
      '防止暴力破解',
      '符合安全最佳实践'
    ]
  });

  suggestions.push({
    id: 'suggest-5',
    type: 'merge',
    title: '考虑合并C1和C2设置模块',
    description: 'C1主题设置和C2通知配置功能相对简单，可以考虑合并为统一的设置管理模块。',
    impact: 'low',
    confidence: 70,
    moduleId: 'C1',
    actionable: true,
    estimatedTime: '1-2小时',
    benefits: [
      '简化项目结构',
      '减少维护成本',
      '统一用户体验'
    ],
    risks: [
      '可能影响模块独立性'
    ]
  });

  return suggestions;
};

// 建议卡片组件
const SuggestionCard: React.FC<{
  suggestion: AISuggestion;
  onApply: (id: string) => void;
  onDismiss: (id: string) => void;
}> = ({ suggestion, onApply, onDismiss }) => {
  const getTypeIcon = (type: AISuggestion['type']) => {
    switch (type) {
      case 'optimization': return <Zap size={16} className="text-yellow-500" />;
      case 'refactor': return <Code size={16} className="text-blue-500" />;
      case 'split': return <GitBranch size={16} className="text-purple-500" />;
      case 'merge': return <GitBranch size={16} className="text-green-500" />;
      case 'dependency': return <Settings size={16} className="text-gray-500" />;
      case 'performance': return <Zap size={16} className="text-orange-500" />;
      case 'security': return <AlertTriangle size={16} className="text-red-500" />;
    }
  };

  const getImpactColor = (impact: AISuggestion['impact']) => {
    switch (impact) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
    }
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          {getTypeIcon(suggestion.type)}
          <span className="text-sm font-medium text-gray-600 capitalize">
            {suggestion.type}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getImpactColor(suggestion.impact)}`}>
            {suggestion.impact} impact
          </span>
          <span className="text-xs text-gray-500">
            {suggestion.confidence}% 置信度
          </span>
        </div>
      </div>

      <h3 className="font-bold text-gray-900 mb-2">{suggestion.title}</h3>
      <p className="text-sm text-gray-600 mb-3">{suggestion.description}</p>

      {suggestion.estimatedTime && (
        <div className="text-xs text-gray-500 mb-3">
          预估时间: {suggestion.estimatedTime}
        </div>
      )}

      {suggestion.benefits && (
        <div className="mb-3">
          <h4 className="text-sm font-medium text-gray-700 mb-1">优势:</h4>
          <ul className="text-xs text-gray-600 space-y-1">
            {suggestion.benefits.map((benefit, index) => (
              <li key={index} className="flex items-center gap-1">
                <CheckCircle size={12} className="text-green-500" />
                {benefit}
              </li>
            ))}
          </ul>
        </div>
      )}

      {suggestion.risks && (
        <div className="mb-3">
          <h4 className="text-sm font-medium text-gray-700 mb-1">风险:</h4>
          <ul className="text-xs text-gray-600 space-y-1">
            {suggestion.risks.map((risk, index) => (
              <li key={index} className="flex items-center gap-1">
                <AlertTriangle size={12} className="text-yellow-500" />
                {risk}
              </li>
            ))}
          </ul>
        </div>
      )}

      <div className="flex justify-end gap-2">
        <button
          onClick={() => onDismiss(suggestion.id)}
          className="px-3 py-1 text-sm text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
        >
          忽略
        </button>
        {suggestion.actionable && (
          <button
            onClick={() => onApply(suggestion.id)}
            className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            应用建议
          </button>
        )}
      </div>
    </div>
  );
};

// AI聊天界面
const AIChatInterface: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'assistant',
      content: '你好！我是你的AI开发助手。我可以帮助你分析项目结构、生成代码、优化性能，以及回答技术问题。有什么我可以帮助你的吗？',
      timestamp: new Date().toISOString()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  const sendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    // 模拟AI响应
    setTimeout(() => {
      const aiResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: generateAIResponse(inputMessage),
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);
    }, 1500);
  };

  const generateAIResponse = (userInput: string): string => {
    const input = userInput.toLowerCase();
    
    if (input.includes('优化') || input.includes('性能')) {
      return '基于你的项目分析，我建议以下优化方案：\n\n1. 在B2待办事项模块添加虚拟滚动来处理大量数据\n2. 实现智能缓存策略减少API调用\n3. 使用Web Workers处理复杂计算\n\n需要我为你生成具体的实现代码吗？';
    }
    
    if (input.includes('模块') || input.includes('拆分')) {
      return '关于模块结构，我注意到A1登录认证模块功能较为复杂。建议拆分为：\n\n• A1a: 基础认证（登录/登出）\n• A1b: 用户注册\n• A1c: 密码管理\n\n这样可以提高代码可维护性和测试覆盖率。要我帮你生成拆分后的模块结构吗？';
    }
    
    if (input.includes('cloudflare') || input.includes('部署')) {
      return '关于Cloudflare部署，我建议：\n\n1. 使用Wrangler CLI进行自动化部署\n2. 配置多环境（dev/staging/prod）\n3. 启用D1数据库和KV存储\n4. 设置自定义域名和SSL\n\n需要我生成完整的wrangler.toml配置文件吗？';
    }
    
    return '我理解你的问题。基于当前项目状态，我建议你可以：\n\n1. 查看智能建议面板获取具体优化建议\n2. 使用Cloudflare集成功能检查兼容性\n3. 通过项目树管理功能调整模块结构\n\n有什么具体的技术问题我可以帮你解决吗？';
  };

  return (
    <div className="flex flex-col h-full">
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={clsx(
                'max-w-xs lg:max-w-md px-4 py-2 rounded-lg',
                message.type === 'user'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-900'
              )}
            >
              <p className="text-sm whitespace-pre-line">{message.content}</p>
              <p className="text-xs opacity-75 mt-1">
                {new Date(message.timestamp).toLocaleTimeString()}
              </p>
            </div>
          </div>
        ))}
        
        {isTyping && (
          <div className="flex justify-start">
            <div className="bg-gray-100 text-gray-900 px-4 py-2 rounded-lg">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="border-t border-gray-200 p-4">
        <div className="flex gap-2">
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
            placeholder="询问AI助手..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            onClick={sendMessage}
            disabled={!inputMessage.trim() || isTyping}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Send size={16} />
          </button>
        </div>
      </div>
    </div>
  );
};

// 主AI助手组件
export const AIAssistant: React.FC<{ treeData?: TreeModule }> = ({ treeData }) => {
  const [activeTab, setActiveTab] = useState<'suggestions' | 'chat' | 'codegen'>('suggestions');
  const [suggestions, setSuggestions] = useState<AISuggestion[]>([]);

  useEffect(() => {
    if (treeData) {
      const newSuggestions = generateSmartSuggestions(treeData);
      setSuggestions(newSuggestions);
    }
  }, [treeData]);

  const handleApplySuggestion = (id: string) => {
    console.log('Applying suggestion:', id);
    // 这里可以实现具体的建议应用逻辑
    setSuggestions(prev => prev.filter(s => s.id !== id));
  };

  const handleDismissSuggestion = (id: string) => {
    setSuggestions(prev => prev.filter(s => s.id !== id));
  };

  return (
    <div className="h-full bg-white border border-gray-200 rounded-lg flex flex-col">
      {/* 标签页头部 */}
      <div className="border-b border-gray-200">
        <div className="flex items-center justify-between p-4">
          <h3 className="text-lg font-bold flex items-center gap-2">
            <Brain size={20} className="text-purple-500" />
            AI开发助手
          </h3>
          <Sparkles size={16} className="text-purple-500" />
        </div>
        
        <div className="flex">
          <button
            onClick={() => setActiveTab('suggestions')}
            className={clsx(
              'flex-1 py-2 px-4 text-sm font-medium border-b-2 transition-colors',
              activeTab === 'suggestions'
                ? 'border-purple-500 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            )}
          >
            <Lightbulb size={16} className="inline mr-2" />
            智能建议
          </button>
          <button
            onClick={() => setActiveTab('chat')}
            className={clsx(
              'flex-1 py-2 px-4 text-sm font-medium border-b-2 transition-colors',
              activeTab === 'chat'
                ? 'border-purple-500 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            )}
          >
            <MessageSquare size={16} className="inline mr-2" />
            AI对话
          </button>
          <button
            onClick={() => setActiveTab('codegen')}
            className={clsx(
              'flex-1 py-2 px-4 text-sm font-medium border-b-2 transition-colors',
              activeTab === 'codegen'
                ? 'border-purple-500 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            )}
          >
            <Code size={16} className="inline mr-2" />
            代码生成
          </button>
        </div>
      </div>

      {/* 标签页内容 */}
      <div className="flex-1 overflow-hidden">
        {activeTab === 'suggestions' ? (
          <div className="p-4 overflow-y-auto h-full">
            {suggestions.length > 0 ? (
              <div className="space-y-4">
                {suggestions.map((suggestion) => (
                  <SuggestionCard
                    key={suggestion.id}
                    suggestion={suggestion}
                    onApply={handleApplySuggestion}
                    onDismiss={handleDismissSuggestion}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center text-gray-500 py-8">
                <Lightbulb size={48} className="mx-auto mb-4 text-gray-300" />
                <p>暂无智能建议</p>
                <p className="text-sm mt-1">AI正在分析你的项目结构...</p>
              </div>
            )}
          </div>
        ) : activeTab === 'chat' ? (
          <AIChatInterface />
        ) : (
          <CodeGenerator />
        )}
      </div>
    </div>
  );
};
