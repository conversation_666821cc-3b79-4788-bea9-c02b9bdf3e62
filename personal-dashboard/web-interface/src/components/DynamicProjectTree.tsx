import React, { useState, useCallback } from 'react';
import { 
  ChevronDown, 
  ChevronRight, 
  Circle, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Edit3,
  Plus,
  Trash2,
  GitBranch,
  GitMerge,
  Save,
  X
} from 'lucide-react';
import clsx from 'clsx';

// 扩展的模块类型定义
type ModuleStatus = 'planned' | 'active' | 'testing' | 'completed' | 'blocked' | 'paused';
type ModulePriority = 'high' | 'medium' | 'low';

interface TreeModule {
  id: string;
  name: string;
  description: string;
  status: ModuleStatus;
  priority: ModulePriority;
  progress: number;
  children?: TreeModule[];
  estimatedHours?: number;
  actualHours?: number;
  parent?: string;
}

interface ChangeRecord {
  id: string;
  timestamp: string;
  action: 'create' | 'update' | 'delete' | 'move' | 'split' | 'merge';
  moduleId: string;
  details: string;
  oldValue?: any;
  newValue?: any;
}

// 动态树管理Hook
const useTreeManager = (initialData: TreeModule) => {
  const [treeData, setTreeData] = useState<TreeModule>(initialData);
  const [changeHistory, setChangeHistory] = useState<ChangeRecord[]>([]);
  const [draggedModule, setDraggedModule] = useState<string | null>(null);

  const addChangeRecord = useCallback((action: ChangeRecord['action'], moduleId: string, details: string, oldValue?: any, newValue?: any) => {
    const record: ChangeRecord = {
      id: `change-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      action,
      moduleId,
      details,
      oldValue,
      newValue
    };
    setChangeHistory(prev => [record, ...prev.slice(0, 49)]); // 保留最近50条记录
  }, []);

  const findModuleById = useCallback((id: string, node: TreeModule = treeData): TreeModule | null => {
    if (node.id === id) return node;
    if (node.children) {
      for (const child of node.children) {
        const found = findModuleById(id, child);
        if (found) return found;
      }
    }
    return null;
  }, [treeData]);

  const updateModule = useCallback((id: string, updates: Partial<TreeModule>) => {
    const updateNode = (node: TreeModule): TreeModule => {
      if (node.id === id) {
        const oldValue = { ...node };
        const newValue = { ...node, ...updates };
        addChangeRecord('update', id, `Updated module: ${Object.keys(updates).join(', ')}`, oldValue, newValue);
        return newValue;
      }
      if (node.children) {
        return {
          ...node,
          children: node.children.map(updateNode)
        };
      }
      return node;
    };
    setTreeData(updateNode);
  }, [addChangeRecord]);

  const addModule = useCallback((parentId: string, newModule: Omit<TreeModule, 'id'>) => {
    const moduleWithId = {
      ...newModule,
      id: `module-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      parent: parentId
    };

    const addToNode = (node: TreeModule): TreeModule => {
      if (node.id === parentId) {
        const updatedNode = {
          ...node,
          children: [...(node.children || []), moduleWithId]
        };
        addChangeRecord('create', moduleWithId.id, `Created new module under ${node.name}`, null, moduleWithId);
        return updatedNode;
      }
      if (node.children) {
        return {
          ...node,
          children: node.children.map(addToNode)
        };
      }
      return node;
    };
    setTreeData(addToNode);
  }, [addChangeRecord]);

  const deleteModule = useCallback((id: string) => {
    const moduleToDelete = findModuleById(id);
    if (!moduleToDelete) return;

    const deleteFromNode = (node: TreeModule): TreeModule => {
      if (node.children) {
        const filteredChildren = node.children.filter(child => child.id !== id);
        if (filteredChildren.length !== node.children.length) {
          addChangeRecord('delete', id, `Deleted module: ${moduleToDelete.name}`, moduleToDelete, null);
        }
        return {
          ...node,
          children: filteredChildren.map(deleteFromNode)
        };
      }
      return node;
    };
    setTreeData(deleteFromNode);
  }, [findModuleById, addChangeRecord]);

  const moveModule = useCallback((moduleId: string, newParentId: string) => {
    const moduleToMove = findModuleById(moduleId);
    if (!moduleToMove || moduleId === newParentId) return;

    // 首先从原位置删除
    const removeFromNode = (node: TreeModule): TreeModule => {
      if (node.children) {
        return {
          ...node,
          children: node.children.filter(child => child.id !== moduleId).map(removeFromNode)
        };
      }
      return node;
    };

    // 然后添加到新位置
    const addToNode = (node: TreeModule): TreeModule => {
      if (node.id === newParentId) {
        return {
          ...node,
          children: [...(node.children || []), { ...moduleToMove, parent: newParentId }]
        };
      }
      if (node.children) {
        return {
          ...node,
          children: node.children.map(addToNode)
        };
      }
      return node;
    };

    const tempTree = removeFromNode(treeData);
    const finalTree = addToNode(tempTree);
    
    setTreeData(finalTree);
    addChangeRecord('move', moduleId, `Moved ${moduleToMove.name} to new parent`, moduleToMove.parent, newParentId);
  }, [findModuleById, treeData, addChangeRecord]);

  return {
    treeData,
    changeHistory,
    draggedModule,
    setDraggedModule,
    updateModule,
    addModule,
    deleteModule,
    moveModule,
    findModuleById
  };
};

// 编辑模块对话框组件
const EditModuleDialog: React.FC<{
  module: TreeModule | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (updates: Partial<TreeModule>) => void;
}> = ({ module, isOpen, onClose, onSave }) => {
  const [formData, setFormData] = useState<Partial<TreeModule>>({});

  React.useEffect(() => {
    if (module) {
      setFormData({
        name: module.name,
        description: module.description,
        status: module.status,
        priority: module.priority,
        progress: module.progress,
        estimatedHours: module.estimatedHours
      });
    }
  }, [module]);

  if (!isOpen || !module) return null;

  const handleSave = () => {
    onSave(formData);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-96 max-h-[80vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-bold">编辑模块</h3>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={20} />
          </button>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">模块名称</label>
            <input
              type="text"
              value={formData.name || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">描述</label>
            <textarea
              value={formData.description || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">状态</label>
              <select
                value={formData.status || 'planned'}
                onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as ModuleStatus }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="planned">计划中</option>
                <option value="active">进行中</option>
                <option value="testing">测试中</option>
                <option value="completed">已完成</option>
                <option value="blocked">阻塞</option>
                <option value="paused">暂停</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">优先级</label>
              <select
                value={formData.priority || 'medium'}
                onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as ModulePriority }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="high">高</option>
                <option value="medium">中</option>
                <option value="low">低</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">进度 (%)</label>
              <input
                type="number"
                min="0"
                max="100"
                value={formData.progress || 0}
                onChange={(e) => setFormData(prev => ({ ...prev, progress: parseInt(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">预估工时</label>
              <input
                type="number"
                min="0"
                value={formData.estimatedHours || 0}
                onChange={(e) => setFormData(prev => ({ ...prev, estimatedHours: parseInt(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-2 mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            取消
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center gap-2"
          >
            <Save size={16} />
            保存
          </button>
        </div>
      </div>
    </div>
  );
};

// 状态图标组件
const StatusIcon: React.FC<{ status: ModuleStatus }> = ({ status }) => {
  const iconProps = { size: 16 };

  switch (status) {
    case 'completed':
      return <CheckCircle {...iconProps} className="text-green-500" />;
    case 'active':
      return <Clock {...iconProps} className="text-blue-500" />;
    case 'blocked':
      return <AlertCircle {...iconProps} className="text-red-500" />;
    case 'paused':
      return <Circle {...iconProps} className="text-yellow-500" />;
    default:
      return <Circle {...iconProps} className="text-gray-400" />;
  }
};

// 优先级标签组件
const PriorityBadge: React.FC<{ priority: ModulePriority }> = ({ priority }) => {
  const colors = {
    high: 'bg-red-100 text-red-800 border-red-200',
    medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    low: 'bg-gray-100 text-gray-800 border-gray-200'
  };

  return (
    <span className={clsx(
      'px-2 py-1 text-xs font-medium rounded-full border',
      colors[priority]
    )}>
      {priority}
    </span>
  );
};

// 进度条组件
const ProgressBar: React.FC<{ progress: number }> = ({ progress }) => {
  return (
    <div className="w-full bg-gray-200 rounded-full h-2">
      <div
        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
        style={{ width: `${progress}%` }}
      />
    </div>
  );
};

// 可拖拽的树节点组件
const DraggableTreeNode: React.FC<{
  module: TreeModule;
  level: number;
  onModuleClick: (module: TreeModule) => void;
  onEditModule: (module: TreeModule) => void;
  onDeleteModule: (id: string) => void;
  onAddChild: (parentId: string) => void;
  draggedModule: string | null;
  setDraggedModule: (id: string | null) => void;
  onMoveModule: (moduleId: string, newParentId: string) => void;
}> = ({
  module,
  level,
  onModuleClick,
  onEditModule,
  onDeleteModule,
  onAddChild,
  draggedModule,
  setDraggedModule,
  onMoveModule
}) => {
  const [isExpanded, setIsExpanded] = useState(level < 2);
  const [isDragOver, setIsDragOver] = useState(false);
  const hasChildren = module.children && module.children.length > 0;

  const handleDragStart = (e: React.DragEvent) => {
    setDraggedModule(module.id);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setIsDragOver(true);
  };

  const handleDragLeave = () => {
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    if (draggedModule && draggedModule !== module.id) {
      onMoveModule(draggedModule, module.id);
    }
    setDraggedModule(null);
  };

  const handleDragEnd = () => {
    setDraggedModule(null);
  };

  return (
    <div className="select-none">
      <div
        draggable
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onDragEnd={handleDragEnd}
        className={clsx(
          'flex items-center py-2 px-3 rounded-lg cursor-pointer transition-all group',
          level === 0 && 'bg-blue-50 border border-blue-200',
          isDragOver && 'bg-green-100 border-green-300',
          draggedModule === module.id && 'opacity-50',
          'hover:bg-gray-50'
        )}
        style={{ marginLeft: `${level * 20}px` }}
        onClick={() => onModuleClick(module)}
      >
        {/* 展开/收起按钮 */}
        <button
          className="mr-2 p-1 hover:bg-gray-200 rounded"
          onClick={(e) => {
            e.stopPropagation();
            setIsExpanded(!isExpanded);
          }}
        >
          {hasChildren ? (
            isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />
          ) : (
            <div className="w-4 h-4" />
          )}
        </button>

        {/* 状态图标 */}
        <StatusIcon status={module.status} />

        {/* 模块信息 */}
        <div className="flex-1 ml-3">
          <div className="flex items-center gap-2">
            <span className="font-medium text-gray-900">{module.name}</span>
            <PriorityBadge priority={module.priority} />
          </div>
          <p className="text-sm text-gray-600 mt-1">{module.description}</p>

          {/* 进度信息 */}
          <div className="flex items-center gap-4 mt-2">
            <div className="flex-1">
              <ProgressBar progress={module.progress} />
            </div>
            <span className="text-sm text-gray-500">{module.progress}%</span>
            {module.estimatedHours && (
              <span className="text-sm text-gray-500">
                {module.actualHours || 0}h / {module.estimatedHours}h
              </span>
            )}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onAddChild(module.id);
            }}
            className="p-1 text-green-600 hover:bg-green-100 rounded"
            title="添加子模块"
          >
            <Plus size={16} />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onEditModule(module);
            }}
            className="p-1 text-blue-600 hover:bg-blue-100 rounded"
            title="编辑模块"
          >
            <Edit3 size={16} />
          </button>
          {level > 0 && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDeleteModule(module.id);
              }}
              className="p-1 text-red-600 hover:bg-red-100 rounded"
              title="删除模块"
            >
              <Trash2 size={16} />
            </button>
          )}
        </div>
      </div>

      {/* 子节点 */}
      {hasChildren && isExpanded && (
        <div className="mt-1">
          {module.children!.map((child) => (
            <DraggableTreeNode
              key={child.id}
              module={child}
              level={level + 1}
              onModuleClick={onModuleClick}
              onEditModule={onEditModule}
              onDeleteModule={onDeleteModule}
              onAddChild={onAddChild}
              draggedModule={draggedModule}
              setDraggedModule={setDraggedModule}
              onMoveModule={onMoveModule}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export { useTreeManager, EditModuleDialog, DraggableTreeNode, StatusIcon, PriorityBadge, ProgressBar };
export type { TreeModule, ChangeRecord };
