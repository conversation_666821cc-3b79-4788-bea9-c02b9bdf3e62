import React, { useState } from 'react';
import { 
  Cloud, 
  Settings, 
  Monitor, 
  CheckCircle, 
  AlertTriangle,
  Activity,
  Database,
  Globe,
  Zap,
  Shield
} from 'lucide-react';
import { CloudflareIntegration } from './CloudflareIntegration';
import { DeploymentConfigGenerator } from './DeploymentConfigGenerator';

// 标签页类型
type TabType = 'overview' | 'compatibility' | 'deployment' | 'monitoring';

// 概览统计数据
interface OverviewStats {
  totalChecks: number;
  passedChecks: number;
  warnings: number;
  errors: number;
  servicesOnline: number;
  totalServices: number;
  avgResponseTime: number;
  uptime: number;
}

// 概览面板组件
const OverviewPanel: React.FC = () => {
  const stats: OverviewStats = {
    totalChecks: 12,
    passedChecks: 8,
    warnings: 3,
    errors: 1,
    servicesOnline: 3,
    totalServices: 4,
    avgResponseTime: 92,
    uptime: 99.94
  };

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    subtitle?: string;
    icon: React.ReactNode;
    color: 'blue' | 'green' | 'yellow' | 'red' | 'purple';
  }> = ({ title, value, subtitle, icon, color }) => {
    const colorClasses = {
      blue: 'bg-blue-50 text-blue-600 border-blue-200',
      green: 'bg-green-50 text-green-600 border-green-200',
      yellow: 'bg-yellow-50 text-yellow-600 border-yellow-200',
      red: 'bg-red-50 text-red-600 border-red-200',
      purple: 'bg-purple-50 text-purple-600 border-purple-200'
    };

    return (
      <div className={`p-4 rounded-lg border ${colorClasses[color]}`}>
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-white">
            {icon}
          </div>
          <div>
            <h3 className="text-lg font-bold">{value}</h3>
            <p className="text-sm font-medium">{title}</p>
            {subtitle && <p className="text-xs opacity-75">{subtitle}</p>}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="兼容性检查"
          value={`${stats.passedChecks}/${stats.totalChecks}`}
          subtitle="通过/总计"
          icon={<CheckCircle size={24} />}
          color="green"
        />
        <StatCard
          title="服务状态"
          value={`${stats.servicesOnline}/${stats.totalServices}`}
          subtitle="在线/总计"
          icon={<Monitor size={24} />}
          color="blue"
        />
        <StatCard
          title="平均响应时间"
          value={`${stats.avgResponseTime}ms`}
          subtitle="全球平均"
          icon={<Activity size={24} />}
          color="purple"
        />
        <StatCard
          title="系统可用性"
          value={`${stats.uptime}%`}
          subtitle="30天平均"
          icon={<Shield size={24} />}
          color="green"
        />
      </div>

      {/* 快速状态概览 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 兼容性状态 */}
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h3 className="text-lg font-bold mb-4 flex items-center gap-2">
            <CheckCircle size={20} className="text-green-500" />
            兼容性状态
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">运行时环境</span>
              <div className="flex items-center gap-2">
                <CheckCircle size={16} className="text-green-500" />
                <span className="text-sm font-medium">2/2 通过</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">依赖管理</span>
              <div className="flex items-center gap-2">
                <AlertTriangle size={16} className="text-red-500" />
                <span className="text-sm font-medium">1/2 通过</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">存储服务</span>
              <div className="flex items-center gap-2">
                <CheckCircle size={16} className="text-green-500" />
                <span className="text-sm font-medium">3/3 通过</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">性能指标</span>
              <div className="flex items-center gap-2">
                <AlertTriangle size={16} className="text-yellow-500" />
                <span className="text-sm font-medium">2/3 通过</span>
              </div>
            </div>
          </div>
        </div>

        {/* 服务状态 */}
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h3 className="text-lg font-bold mb-4 flex items-center gap-2">
            <Globe size={20} className="text-blue-500" />
            服务状态
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Zap size={16} className="text-blue-500" />
                <span className="text-sm text-gray-600">Workers</span>
              </div>
              <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                正常
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Globe size={16} className="text-blue-500" />
                <span className="text-sm text-gray-600">Pages</span>
              </div>
              <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                正常
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Database size={16} className="text-green-500" />
                <span className="text-sm text-gray-600">D1</span>
              </div>
              <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                正常
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Database size={16} className="text-purple-500" />
                <span className="text-sm text-gray-600">KV</span>
              </div>
              <span className="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                降级
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 快速操作 */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h3 className="text-lg font-bold mb-4">快速操作</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
            <CheckCircle size={20} className="text-green-500 mb-2" />
            <h4 className="font-medium text-gray-900">运行兼容性检查</h4>
            <p className="text-sm text-gray-600">检查代码与CF平台的兼容性</p>
          </button>
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
            <Settings size={20} className="text-blue-500 mb-2" />
            <h4 className="font-medium text-gray-900">生成部署配置</h4>
            <p className="text-sm text-gray-600">自动生成wrangler.toml等配置</p>
          </button>
          <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
            <Monitor size={20} className="text-purple-500 mb-2" />
            <h4 className="font-medium text-gray-900">查看服务监控</h4>
            <p className="text-sm text-gray-600">实时监控CF服务状态</p>
          </button>
        </div>
      </div>
    </div>
  );
};

// 主仪表板组件
export const CloudflareDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('overview');

  const tabs = [
    { id: 'overview', name: '概览', icon: <Cloud size={20} /> },
    { id: 'compatibility', name: '兼容性检查', icon: <CheckCircle size={20} /> },
    { id: 'deployment', name: '部署配置', icon: <Settings size={20} /> },
    { id: 'monitoring', name: '服务监控', icon: <Monitor size={20} /> }
  ] as const;

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <OverviewPanel />;
      case 'compatibility':
      case 'monitoring':
        return <CloudflareIntegration />;
      case 'deployment':
        return <DeploymentConfigGenerator />;
      default:
        return <OverviewPanel />;
    }
  };

  return (
    <div className="h-full bg-gray-50">
      {/* 标签页导航 */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-4">
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.icon}
                {tab.name}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* 标签页内容 */}
      <div className="p-6 overflow-y-auto" style={{ height: 'calc(100% - 73px)' }}>
        {renderTabContent()}
      </div>
    </div>
  );
};
