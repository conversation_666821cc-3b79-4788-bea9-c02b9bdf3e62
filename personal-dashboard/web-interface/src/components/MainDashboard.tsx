import React, { useState } from 'react';
import {
  GitBranch,
  Cloud,
  Settings,
  FileText,
  Activity,
  Menu,
  X,
  Brain
} from 'lucide-react';
import { EnhancedProjectTree } from './EnhancedProjectTree';
import { CloudflareDashboard } from './CloudflareDashboard';
import { AIAssistant } from './AIAssistant';

// 主视图类型
type MainView = 'project-tree' | 'cloudflare' | 'documentation' | 'analytics' | 'ai-assistant';

// 侧边栏导航项
const navigationItems = [
  {
    id: 'project-tree',
    name: '项目树管理',
    icon: <GitBranch size={20} />,
    description: '动态项目结构管理'
  },
  {
    id: 'cloudflare',
    name: 'Cloudflare集成',
    icon: <Cloud size={20} />,
    description: '平台兼容性和部署'
  },
  {
    id: 'documentation',
    name: '项目文档',
    icon: <FileText size={20} />,
    description: '蓝图和技术文档'
  },
  {
    id: 'analytics',
    name: '项目分析',
    icon: <Activity size={20} />,
    description: '进度和性能分析'
  },
  {
    id: 'ai-assistant',
    name: 'AI助手',
    icon: <Brain size={20} />,
    description: '智能建议和代码生成'
  }
] as const;

// 文档视图组件
const DocumentationView: React.FC = () => {
  const documents = [
    {
      title: '项目蓝图大纲',
      description: '完整的项目架构设计和技术选型',
      path: 'project-blueprint/project-blueprint.md',
      category: '架构设计',
      lastModified: '2025-07-09'
    },
    {
      title: '技术归档与灾难恢复',
      description: '备份策略和恢复流程文档',
      path: 'disaster-recovery/backup-strategy.md',
      category: '运维管理',
      lastModified: '2025-07-09'
    },
    {
      title: '模块开发文档',
      description: 'A1登录认证模块详细设计',
      path: 'project-tree/modules/A1-auth.md',
      category: '模块设计',
      lastModified: '2025-07-09'
    },
    {
      title: 'Cloudflare配置指南',
      description: '平台配置和依赖管理',
      path: 'platform-root/cloudflare-config.yaml',
      category: '平台配置',
      lastModified: '2025-07-09'
    }
  ];

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">项目文档</h2>
        <p className="text-gray-600">项目相关的所有技术文档和设计资料</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {documents.map((doc, index) => (
          <div key={index} className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between mb-3">
              <div>
                <h3 className="text-lg font-bold text-gray-900 mb-1">{doc.title}</h3>
                <span className="inline-block px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                  {doc.category}
                </span>
              </div>
              <FileText size={24} className="text-gray-400" />
            </div>
            <p className="text-gray-600 mb-3">{doc.description}</p>
            <div className="flex items-center justify-between text-sm text-gray-500">
              <span>路径: {doc.path}</span>
              <span>更新: {doc.lastModified}</span>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-lg font-bold text-blue-900 mb-2">文档说明</h3>
        <ul className="text-blue-800 space-y-1">
          <li>• 项目蓝图大纲：包含完整的架构设计和技术决策</li>
          <li>• 技术归档：备份恢复策略，确保项目安全</li>
          <li>• 模块文档：每个功能模块的详细设计和实现</li>
          <li>• 平台配置：Cloudflare集成的配置和依赖管理</li>
        </ul>
      </div>
    </div>
  );
};

// 分析视图组件
const AnalyticsView: React.FC = () => {
  const metrics = {
    totalModules: 8,
    completedModules: 1,
    inProgressModules: 2,
    plannedModules: 5,
    totalEstimatedHours: 86,
    actualHours: 12,
    completionRate: 14,
    averageModuleSize: 10.75
  };

  const recentActivity = [
    { action: '创建恢复点', details: 'Phase 1 Complete - Basic Architecture', time: '2小时前' },
    { action: '更新模块', details: 'A1登录认证 - 进度更新到70%', time: '3小时前' },
    { action: '添加模块', details: '新增B3日历视图模块', time: '1天前' },
    { action: '兼容性检查', details: 'Cloudflare Workers兼容性验证', time: '1天前' }
  ];

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">项目分析</h2>
        <p className="text-gray-600">项目进度、性能指标和开发活动分析</p>
      </div>

      {/* 关键指标 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h3 className="text-lg font-bold text-gray-900">{metrics.completionRate}%</h3>
          <p className="text-sm text-gray-600">项目完成度</p>
          <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
            <div 
              className="bg-blue-600 h-2 rounded-full" 
              style={{ width: `${metrics.completionRate}%` }}
            />
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h3 className="text-lg font-bold text-gray-900">{metrics.actualHours}h</h3>
          <p className="text-sm text-gray-600">已投入工时</p>
          <p className="text-xs text-gray-500 mt-1">总预估: {metrics.totalEstimatedHours}h</p>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h3 className="text-lg font-bold text-gray-900">{metrics.inProgressModules}</h3>
          <p className="text-sm text-gray-600">进行中模块</p>
          <p className="text-xs text-gray-500 mt-1">总计: {metrics.totalModules}个</p>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h3 className="text-lg font-bold text-gray-900">{metrics.averageModuleSize}h</h3>
          <p className="text-sm text-gray-600">平均模块大小</p>
          <p className="text-xs text-gray-500 mt-1">工时估算</p>
        </div>
      </div>

      {/* 模块状态分布 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h3 className="text-lg font-bold text-gray-900 mb-4">模块状态分布</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">已完成</span>
              <div className="flex items-center gap-2">
                <div className="w-20 bg-gray-200 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: `${(metrics.completedModules / metrics.totalModules) * 100}%` }} />
                </div>
                <span className="text-sm font-medium">{metrics.completedModules}</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">进行中</span>
              <div className="flex items-center gap-2">
                <div className="w-20 bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{ width: `${(metrics.inProgressModules / metrics.totalModules) * 100}%` }} />
                </div>
                <span className="text-sm font-medium">{metrics.inProgressModules}</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">计划中</span>
              <div className="flex items-center gap-2">
                <div className="w-20 bg-gray-200 rounded-full h-2">
                  <div className="bg-gray-400 h-2 rounded-full" style={{ width: `${(metrics.plannedModules / metrics.totalModules) * 100}%` }} />
                </div>
                <span className="text-sm font-medium">{metrics.plannedModules}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h3 className="text-lg font-bold text-gray-900 mb-4">最近活动</h3>
          <div className="space-y-3">
            {recentActivity.map((activity, index) => (
              <div key={index} className="flex items-start gap-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                  <p className="text-xs text-gray-600">{activity.details}</p>
                  <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// 主仪表板组件
export const MainDashboard: React.FC = () => {
  const [activeView, setActiveView] = useState<MainView>('project-tree');
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const renderMainContent = () => {
    switch (activeView) {
      case 'project-tree':
        return <EnhancedProjectTree />;
      case 'cloudflare':
        return <CloudflareDashboard />;
      case 'documentation':
        return <DocumentationView />;
      case 'analytics':
        return <AnalyticsView />;
      case 'ai-assistant':
        return <AIAssistant />;
      default:
        return <EnhancedProjectTree />;
    }
  };

  const currentView = navigationItems.find(item => item.id === activeView);

  return (
    <div className="flex h-screen bg-gray-100">
      {/* 侧边栏 */}
      <div className={`${sidebarOpen ? 'w-64' : 'w-16'} bg-white border-r border-gray-200 transition-all duration-300 flex flex-col`}>
        {/* 侧边栏头部 */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            {sidebarOpen && (
              <h1 className="text-lg font-bold text-gray-900">Personal Dashboard</h1>
            )}
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-1 text-gray-500 hover:text-gray-700"
            >
              {sidebarOpen ? <X size={20} /> : <Menu size={20} />}
            </button>
          </div>
        </div>

        {/* 导航菜单 */}
        <nav className="flex-1 p-4">
          <div className="space-y-2">
            {navigationItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveView(item.id)}
                className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeView === item.id
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                {item.icon}
                {sidebarOpen && (
                  <div>
                    <div className="font-medium">{item.name}</div>
                    <div className="text-xs opacity-75">{item.description}</div>
                  </div>
                )}
              </button>
            ))}
          </div>
        </nav>

        {/* 侧边栏底部 */}
        {sidebarOpen && (
          <div className="p-4 border-t border-gray-200">
            <div className="text-xs text-gray-500">
              <p>Phase 2: 核心功能实现</p>
              <p>v1.1.0 - 动态调整功能</p>
            </div>
          </div>
        )}
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* 顶部标题栏 */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold text-gray-900">{currentView?.name}</h2>
              <p className="text-sm text-gray-600">{currentView?.description}</p>
            </div>
            <div className="flex items-center gap-2">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Phase 2: 核心功能
              </span>
            </div>
          </div>
        </div>

        {/* 主内容 */}
        <div className="flex-1 overflow-hidden">
          {renderMainContent()}
        </div>
      </div>
    </div>
  );
};
