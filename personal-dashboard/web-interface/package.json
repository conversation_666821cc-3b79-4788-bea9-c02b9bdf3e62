{"name": "personal-dashboard-web", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "deploy": "npm run build && wrangler pages deploy dist"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.2", "zustand": "^4.4.1", "clsx": "^2.0.0", "lucide-react": "^0.263.1", "date-fns": "^2.30.0", "@headlessui/react": "^1.7.15", "zod": "^3.21.4"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "typescript": "^5.1.6", "vite": "^4.4.5", "vitest": "^0.34.1", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^5.17.0", "wrangler": "^3.5.1"}}