# PowerShell版本的恢复点创建脚本
# 用法: .\create-restore-point.ps1 [-Type "manual"] [-Description "描述"]

param(
    [string]$Type = "manual",
    [string]$Description = "手动创建的恢复点"
)

# 配置
$ProjectName = "personal-dashboard"
$BackupDir = ".\disaster-recovery\restore-points"
$Timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$RestorePointId = "restore-$Type-$Timestamp"
$RestorePointDir = Join-Path $BackupDir $RestorePointId

Write-Host "🚀 开始创建恢复点: $RestorePointId" -ForegroundColor Green
Write-Host "📝 描述: $Description" -ForegroundColor Yellow

# 创建恢复点目录
New-Item -ItemType Directory -Path $RestorePointDir -Force | Out-Null

try {
    # 1. 备份代码状态
    Write-Host "📦 备份代码状态..." -ForegroundColor Cyan
    
    if (Get-Command git -ErrorAction SilentlyContinue) {
        git rev-parse HEAD | Out-File -FilePath "$RestorePointDir\git-commit.txt" -Encoding UTF8
        git status --porcelain | Out-File -FilePath "$RestorePointDir\git-status.txt" -Encoding UTF8
        git diff | Out-File -FilePath "$RestorePointDir\git-diff.patch" -Encoding UTF8
        
        # 检查是否有未提交的更改
        $gitStatus = git status --porcelain
        if ($gitStatus) {
            Write-Host "⚠️  发现未提交的更改，创建stash..." -ForegroundColor Yellow
            git stash push -m "restore-point-$RestorePointId" | Out-File -FilePath "$RestorePointDir\git-stash.txt" -Encoding UTF8
        }
    } else {
        Write-Host "⚠️  Git未安装，跳过代码状态备份" -ForegroundColor Yellow
    }

    # 2. 备份配置文件
    Write-Host "⚙️  备份配置文件..." -ForegroundColor Cyan
    $ConfigDir = Join-Path $RestorePointDir "configs"
    New-Item -ItemType Directory -Path $ConfigDir -Force | Out-Null
    
    if (Test-Path "platform-root") {
        Copy-Item -Path "platform-root" -Destination $ConfigDir -Recurse -Force
    }
    if (Test-Path "project-tree") {
        Copy-Item -Path "project-tree" -Destination $ConfigDir -Recurse -Force
    }
    if (Test-Path "web-interface\package*.json") {
        Copy-Item -Path "web-interface\package*.json" -Destination $ConfigDir -Force
    }
    if (Test-Path "package*.json") {
        Copy-Item -Path "package*.json" -Destination $ConfigDir -Force
    }

    # 3. 备份环境变量
    Write-Host "🔐 备份环境变量..." -ForegroundColor Cyan
    if (Test-Path ".env") {
        # Windows下简单复制，实际使用中应该加密
        Copy-Item -Path ".env" -Destination "$RestorePointDir\env.backup" -Force
        Write-Host "⚠️  环境变量已备份但未加密，请注意安全" -ForegroundColor Yellow
    }

    # 4. 导出数据库
    Write-Host "🗄️  导出数据库..." -ForegroundColor Cyan
    if (Get-Command wrangler -ErrorAction SilentlyContinue) {
        try {
            wrangler d1 export dashboard_db --output "$RestorePointDir\database.sql" 2>$null
        } catch {
            Write-Host "⚠️  数据库导出失败或不可用" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️  Wrangler未安装，跳过数据库导出" -ForegroundColor Yellow
    }

    # 5. 备份依赖信息
    Write-Host "📚 备份依赖信息..." -ForegroundColor Cyan
    if (Test-Path "package-lock.json") {
        Copy-Item -Path "package-lock.json" -Destination "$RestorePointDir\package-lock.json" -Force
    }
    if (Test-Path "web-interface\package-lock.json") {
        Copy-Item -Path "web-interface\package-lock.json" -Destination "$RestorePointDir\web-package-lock.json" -Force
    }

    # 6. 备份构建产物
    Write-Host "🏗️  备份构建产物..." -ForegroundColor Cyan
    if (Test-Path "dist") {
        Compress-Archive -Path "dist" -DestinationPath "$RestorePointDir\dist.zip" -Force
    }
    if (Test-Path "web-interface\dist") {
        Compress-Archive -Path "web-interface\dist" -DestinationPath "$RestorePointDir\web-dist.zip" -Force
    }

    # 7. 创建恢复点元数据
    Write-Host "📋 创建元数据..." -ForegroundColor Cyan
    $metadata = @{
        id = $RestorePointId
        type = $Type
        description = $Description
        created_at = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")
        project = $ProjectName
        git_commit = if (Test-Path "$RestorePointDir\git-commit.txt") { Get-Content "$RestorePointDir\git-commit.txt" -Raw } else { "N/A" }
        creator = $env:USERNAME
        hostname = $env:COMPUTERNAME
        node_version = if (Get-Command node -ErrorAction SilentlyContinue) { node --version } else { "N/A" }
        npm_version = if (Get-Command npm -ErrorAction SilentlyContinue) { npm --version } else { "N/A" }
        files = @(
            "git-commit.txt",
            "git-status.txt", 
            "git-diff.patch",
            "configs/",
            "database.sql",
            "package-lock.json",
            "dist.zip"
        )
    }
    
    $metadata | ConvertTo-Json -Depth 10 | Out-File -FilePath "$RestorePointDir\metadata.json" -Encoding UTF8

    # 8. 创建恢复脚本
    Write-Host "🔧 创建恢复脚本..." -ForegroundColor Cyan
    $restoreScript = @"
# PowerShell恢复脚本
# 自动生成于 $(Get-Date)

`$RestorePointDir = Split-Path -Parent `$MyInvocation.MyCommand.Path
`$ProjectRoot = Split-Path -Parent (Split-Path -Parent (Split-Path -Parent `$RestorePointDir))

Write-Host "🔄 开始恢复到恢复点: $(Split-Path -Leaf `$RestorePointDir)" -ForegroundColor Green
Set-Location `$ProjectRoot

# 1. 恢复Git状态
if (Test-Path "`$RestorePointDir\git-commit.txt") {
    `$Commit = Get-Content "`$RestorePointDir\git-commit.txt" -Raw
    Write-Host "📦 恢复到Git提交: `$Commit" -ForegroundColor Cyan
    git checkout `$Commit.Trim()
}

# 2. 恢复配置文件
Write-Host "⚙️  恢复配置文件..." -ForegroundColor Cyan
if (Test-Path "`$RestorePointDir\configs\platform-root") {
    Copy-Item -Path "`$RestorePointDir\configs\platform-root" -Destination "." -Recurse -Force
}
if (Test-Path "`$RestorePointDir\configs\project-tree") {
    Copy-Item -Path "`$RestorePointDir\configs\project-tree" -Destination "." -Recurse -Force
}

# 3. 恢复依赖
if (Test-Path "`$RestorePointDir\package-lock.json") {
    Write-Host "📚 恢复依赖..." -ForegroundColor Cyan
    Copy-Item -Path "`$RestorePointDir\package-lock.json" -Destination "." -Force
    npm ci
}

# 4. 恢复数据库
if ((Test-Path "`$RestorePointDir\database.sql") -and (Get-Command wrangler -ErrorAction SilentlyContinue)) {
    Write-Host "🗄️  恢复数据库..." -ForegroundColor Cyan
    wrangler d1 execute dashboard_db --file="`$RestorePointDir\database.sql"
}

Write-Host "✅ 恢复完成！" -ForegroundColor Green
Write-Host "⚠️  请手动验证系统状态" -ForegroundColor Yellow
"@

    $restoreScript | Out-File -FilePath "$RestorePointDir\restore.ps1" -Encoding UTF8

    # 9. 更新恢复点索引
    Write-Host "📇 更新恢复点索引..." -ForegroundColor Cyan
    $indexFile = Join-Path $BackupDir "restore-points-index.json"
    
    $index = @()
    if (Test-Path $indexFile) {
        try {
            $index = Get-Content $indexFile -Raw | ConvertFrom-Json
        } catch {
            Write-Host "⚠️  索引文件格式错误，重新创建" -ForegroundColor Yellow
            $index = @()
        }
    }
    
    $newEntry = @{
        id = $RestorePointId
        type = $Type
        description = $Description
        created_at = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")
        path = $RestorePointDir
        compressed = $false
    }
    
    $index += $newEntry
    
    # 保持最近50个恢复点
    $index = $index | Sort-Object created_at -Descending | Select-Object -First 50
    
    $index | ConvertTo-Json -Depth 10 | Out-File -FilePath $indexFile -Encoding UTF8

    # 10. 清理旧的恢复点 (保留最近10个)
    Write-Host "🧹 清理旧恢复点..." -ForegroundColor Cyan
    $oldPoints = Get-ChildItem -Path $BackupDir -Directory -Name "restore-*" | 
                 Sort-Object -Descending | 
                 Select-Object -Skip 10
    
    foreach ($oldPoint in $oldPoints) {
        $oldPath = Join-Path $BackupDir $oldPoint
        Remove-Item -Path $oldPath -Recurse -Force -ErrorAction SilentlyContinue
    }

    Write-Host ""
    Write-Host "✅ 恢复点创建完成！" -ForegroundColor Green
    Write-Host "📍 恢复点ID: $RestorePointId" -ForegroundColor White
    Write-Host "📂 位置: $RestorePointDir" -ForegroundColor White
    Write-Host "🔧 恢复命令: $RestorePointDir\restore.ps1" -ForegroundColor White
    Write-Host ""
    Write-Host "📋 恢复点包含:" -ForegroundColor Cyan
    Write-Host "   - Git状态和未提交更改" -ForegroundColor White
    Write-Host "   - 配置文件" -ForegroundColor White
    Write-Host "   - 数据库导出" -ForegroundColor White
    Write-Host "   - 依赖锁定文件" -ForegroundColor White
    Write-Host "   - 构建产物" -ForegroundColor White
    Write-Host ""
    Write-Host "⚠️  重要提醒:" -ForegroundColor Yellow
    Write-Host "   - 恢复前请备份当前状态" -ForegroundColor White
    Write-Host "   - 恢复后请验证系统功能" -ForegroundColor White
    Write-Host "   - 敏感数据需要手动加密" -ForegroundColor White

} catch {
    Write-Host "Error creating restore point: $($_.Exception.Message)" -ForegroundColor Red

    # Clean up failed restore point
    if (Test-Path $RestorePointDir) {
        Remove-Item -Path $RestorePointDir -Recurse -Force -ErrorAction SilentlyContinue
    }

    exit 1
}
