#!/bin/bash

# 列出所有可用的恢复点
# 用法: ./list-restore-points.sh [选项]

set -e

# 配置
BACKUP_DIR="./disaster-recovery/restore-points"
RESTORE_POINTS_INDEX="${BACKUP_DIR}/restore-points-index.json"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -v, --verbose  显示详细信息"
    echo "  -t, --type     按类型筛选 (manual, auto, milestone)"
    echo "  -n, --number   显示最近N个恢复点 (默认: 10)"
    echo ""
    echo "示例:"
    echo "  $0                    # 显示最近10个恢复点"
    echo "  $0 -n 20             # 显示最近20个恢复点"
    echo "  $0 -t milestone      # 只显示里程碑恢复点"
    echo "  $0 -v                # 显示详细信息"
}

# 解析命令行参数
VERBOSE=false
TYPE_FILTER=""
NUMBER=10

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -t|--type)
            TYPE_FILTER="$2"
            shift 2
            ;;
        -n|--number)
            NUMBER="$2"
            shift 2
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查备份目录是否存在
if [ ! -d "${BACKUP_DIR}" ]; then
    echo -e "${RED}❌ 备份目录不存在: ${BACKUP_DIR}${NC}"
    exit 1
fi

echo -e "${CYAN}📋 Personal Dashboard 恢复点列表${NC}"
echo -e "${CYAN}================================${NC}"
echo ""

# 如果索引文件存在，使用索引
if [ -f "${RESTORE_POINTS_INDEX}" ]; then
    echo -e "${GREEN}📇 从索引文件读取恢复点信息...${NC}"
    
    # 使用Python处理JSON索引
    python3 -c "
import json
import sys
from datetime import datetime

try:
    with open('${RESTORE_POINTS_INDEX}', 'r') as f:
        index = json.load(f)
    
    # 按类型筛选
    if '${TYPE_FILTER}':
        index = [item for item in index if item.get('type') == '${TYPE_FILTER}']
    
    # 限制数量
    index = index[:${NUMBER}]
    
    if not index:
        print('${YELLOW}⚠️  没有找到匹配的恢复点${NC}')
        sys.exit(0)
    
    print(f'${BLUE}找到 {len(index)} 个恢复点:${NC}')
    print()
    
    for i, item in enumerate(index, 1):
        # 解析时间
        try:
            created_time = datetime.fromisoformat(item['created_at'].replace('Z', '+00:00'))
            time_str = created_time.strftime('%Y-%m-%d %H:%M:%S UTC')
        except:
            time_str = item.get('created_at', 'Unknown')
        
        # 类型颜色
        type_color = {
            'manual': '${YELLOW}',
            'auto': '${GREEN}',
            'milestone': '${PURPLE}',
            'emergency': '${RED}'
        }.get(item.get('type', 'unknown'), '${NC}')
        
        print(f'{CYAN}{i:2d}.${NC} {type_color}{item.get(\"type\", \"unknown\").upper()}${NC} - {item.get(\"id\", \"unknown\")}')
        print(f'     📅 创建时间: {time_str}')
        print(f'     📝 描述: {item.get(\"description\", \"无描述\")}')
        
        if '${VERBOSE}' == 'true':
            print(f'     📂 路径: {item.get(\"path\", \"unknown\")}')
            print(f'     🗜️  压缩: {\"是\" if item.get(\"compressed\", False) else \"否\"}')
        
        print(f'     🔧 恢复命令: {item.get(\"path\", \"unknown\")}/restore.sh')
        print()

except FileNotFoundError:
    print('${RED}❌ 索引文件不存在${NC}')
    sys.exit(1)
except json.JSONDecodeError:
    print('${RED}❌ 索引文件格式错误${NC}')
    sys.exit(1)
except Exception as e:
    print(f'${RED}❌ 处理索引文件时出错: {e}${NC}')
    sys.exit(1)
" 2>/dev/null

else
    # 如果没有索引文件，直接扫描目录
    echo -e "${YELLOW}⚠️  索引文件不存在，扫描目录...${NC}"
    
    # 查找所有恢复点目录
    RESTORE_POINTS=($(find "${BACKUP_DIR}" -maxdepth 1 -name "restore-*" -type d | sort -r))
    
    if [ ${#RESTORE_POINTS[@]} -eq 0 ]; then
        echo -e "${YELLOW}⚠️  没有找到任何恢复点${NC}"
        exit 0
    fi
    
    # 应用类型筛选
    if [ -n "${TYPE_FILTER}" ]; then
        FILTERED_POINTS=()
        for point in "${RESTORE_POINTS[@]}"; do
            if [[ "$(basename "$point")" == *"-${TYPE_FILTER}-"* ]]; then
                FILTERED_POINTS+=("$point")
            fi
        done
        RESTORE_POINTS=("${FILTERED_POINTS[@]}")
    fi
    
    # 限制数量
    if [ ${#RESTORE_POINTS[@]} -gt ${NUMBER} ]; then
        RESTORE_POINTS=("${RESTORE_POINTS[@]:0:${NUMBER}}")
    fi
    
    echo -e "${BLUE}找到 ${#RESTORE_POINTS[@]} 个恢复点:${NC}"
    echo ""
    
    for i in "${!RESTORE_POINTS[@]}"; do
        POINT="${RESTORE_POINTS[$i]}"
        POINT_NAME=$(basename "$POINT")
        
        # 提取类型
        if [[ "$POINT_NAME" =~ restore-([^-]+)- ]]; then
            TYPE="${BASH_REMATCH[1]}"
        else
            TYPE="unknown"
        fi
        
        # 类型颜色
        case "$TYPE" in
            "manual") TYPE_COLOR="${YELLOW}" ;;
            "auto") TYPE_COLOR="${GREEN}" ;;
            "milestone") TYPE_COLOR="${PURPLE}" ;;
            "emergency") TYPE_COLOR="${RED}" ;;
            *) TYPE_COLOR="${NC}" ;;
        esac
        
        # 获取创建时间
        if [ -f "${POINT}/metadata.json" ]; then
            CREATED_AT=$(python3 -c "
import json
try:
    with open('${POINT}/metadata.json', 'r') as f:
        data = json.load(f)
    print(data.get('created_at', 'Unknown'))
except:
    print('Unknown')
" 2>/dev/null)
            DESCRIPTION=$(python3 -c "
import json
try:
    with open('${POINT}/metadata.json', 'r') as f:
        data = json.load(f)
    print(data.get('description', '无描述'))
except:
    print('无描述')
" 2>/dev/null)
        else
            CREATED_AT=$(stat -c %y "$POINT" 2>/dev/null | cut -d'.' -f1 || echo "Unknown")
            DESCRIPTION="无描述"
        fi
        
        echo -e "${CYAN}$((i+1)).${NC} ${TYPE_COLOR}${TYPE^^}${NC} - ${POINT_NAME}"
        echo -e "     📅 创建时间: ${CREATED_AT}"
        echo -e "     📝 描述: ${DESCRIPTION}"
        
        if [ "$VERBOSE" = true ]; then
            echo -e "     📂 路径: ${POINT}"
            if [ -f "${POINT}.tar.gz" ]; then
                echo -e "     🗜️  压缩: 是"
            else
                echo -e "     🗜️  压缩: 否"
            fi
        fi
        
        if [ -f "${POINT}/restore.sh" ]; then
            echo -e "     🔧 恢复命令: ${POINT}/restore.sh"
        else
            echo -e "     ${RED}❌ 恢复脚本缺失${NC}"
        fi
        echo ""
    done
fi

echo -e "${CYAN}================================${NC}"
echo -e "${GREEN}💡 使用提示:${NC}"
echo -e "   • 运行恢复脚本前请备份当前状态"
echo -e "   • 恢复后请验证系统功能"
echo -e "   • 使用 -v 选项查看详细信息"
echo -e "   • 使用 -t 选项按类型筛选"
echo ""
