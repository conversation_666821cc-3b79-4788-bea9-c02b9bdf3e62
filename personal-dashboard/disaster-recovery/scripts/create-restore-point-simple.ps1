# Simple PowerShell restore point creation script
param(
    [string]$Type = "manual",
    [string]$Description = "Manual restore point"
)

# Configuration
$ProjectName = "personal-dashboard"
$BackupDir = ".\disaster-recovery\restore-points"
$Timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$RestorePointId = "restore-$Type-$Timestamp"
$RestorePointDir = Join-Path $BackupDir $RestorePointId

Write-Host "Creating restore point: $RestorePointId" -ForegroundColor Green
Write-Host "Description: $Description" -ForegroundColor Yellow

# Create restore point directory
New-Item -ItemType Directory -Path $RestorePointDir -Force | Out-Null

try {
    # 1. Backup code status
    Write-Host "Backing up code status..." -ForegroundColor Cyan
    
    if (Get-Command git -ErrorAction SilentlyContinue) {
        git rev-parse HEAD | Out-File -FilePath "$RestorePointDir\git-commit.txt" -Encoding UTF8
        git status --porcelain | Out-File -FilePath "$RestorePointDir\git-status.txt" -Encoding UTF8
        git diff | Out-File -FilePath "$RestorePointDir\git-diff.patch" -Encoding UTF8
    }

    # 2. Backup configuration files
    Write-Host "Backing up configuration files..." -ForegroundColor Cyan
    $ConfigDir = Join-Path $RestorePointDir "configs"
    New-Item -ItemType Directory -Path $ConfigDir -Force | Out-Null
    
    if (Test-Path "platform-root") {
        Copy-Item -Path "platform-root" -Destination $ConfigDir -Recurse -Force
    }
    if (Test-Path "project-tree") {
        Copy-Item -Path "project-tree" -Destination $ConfigDir -Recurse -Force
    }
    if (Test-Path "package*.json") {
        Copy-Item -Path "package*.json" -Destination $ConfigDir -Force
    }

    # 3. Backup dependencies
    Write-Host "Backing up dependencies..." -ForegroundColor Cyan
    if (Test-Path "package-lock.json") {
        Copy-Item -Path "package-lock.json" -Destination "$RestorePointDir\package-lock.json" -Force
    }

    # 4. Create metadata
    Write-Host "Creating metadata..." -ForegroundColor Cyan
    $metadata = @{
        id = $RestorePointId
        type = $Type
        description = $Description
        created_at = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")
        project = $ProjectName
        creator = $env:USERNAME
        hostname = $env:COMPUTERNAME
    }
    
    $metadata | ConvertTo-Json -Depth 10 | Out-File -FilePath "$RestorePointDir\metadata.json" -Encoding UTF8

    # 5. Create restore script
    Write-Host "Creating restore script..." -ForegroundColor Cyan
    $restoreScript = @"
# PowerShell restore script
`$RestorePointDir = Split-Path -Parent `$MyInvocation.MyCommand.Path
`$ProjectRoot = Split-Path -Parent (Split-Path -Parent (Split-Path -Parent `$RestorePointDir))

Write-Host "Restoring to restore point: $(Split-Path -Leaf `$RestorePointDir)" -ForegroundColor Green
Set-Location `$ProjectRoot

# Restore Git status
if (Test-Path "`$RestorePointDir\git-commit.txt") {
    `$Commit = Get-Content "`$RestorePointDir\git-commit.txt" -Raw
    Write-Host "Restoring to Git commit: `$Commit" -ForegroundColor Cyan
    git checkout `$Commit.Trim()
}

# Restore configuration files
Write-Host "Restoring configuration files..." -ForegroundColor Cyan
if (Test-Path "`$RestorePointDir\configs\platform-root") {
    Copy-Item -Path "`$RestorePointDir\configs\platform-root" -Destination "." -Recurse -Force
}
if (Test-Path "`$RestorePointDir\configs\project-tree") {
    Copy-Item -Path "`$RestorePointDir\configs\project-tree" -Destination "." -Recurse -Force
}

# Restore dependencies
if (Test-Path "`$RestorePointDir\package-lock.json") {
    Write-Host "Restoring dependencies..." -ForegroundColor Cyan
    Copy-Item -Path "`$RestorePointDir\package-lock.json" -Destination "." -Force
    npm ci
}

Write-Host "Restore completed!" -ForegroundColor Green
Write-Host "Please verify system status manually" -ForegroundColor Yellow
"@

    $restoreScript | Out-File -FilePath "$RestorePointDir\restore.ps1" -Encoding UTF8

    Write-Host ""
    Write-Host "Restore point created successfully!" -ForegroundColor Green
    Write-Host "Restore Point ID: $RestorePointId" -ForegroundColor White
    Write-Host "Location: $RestorePointDir" -ForegroundColor White
    Write-Host "Restore Command: $RestorePointDir\restore.ps1" -ForegroundColor White
    Write-Host ""
    Write-Host "Restore point contains:" -ForegroundColor Cyan
    Write-Host "   - Git status and uncommitted changes" -ForegroundColor White
    Write-Host "   - Configuration files" -ForegroundColor White
    Write-Host "   - Dependency lock files" -ForegroundColor White
    Write-Host ""

} catch {
    Write-Host "Error creating restore point: $($_.Exception.Message)" -ForegroundColor Red
    
    # Clean up failed restore point
    if (Test-Path $RestorePointDir) {
        Remove-Item -Path $RestorePointDir -Recurse -Force -ErrorAction SilentlyContinue
    }
    
    exit 1
}
