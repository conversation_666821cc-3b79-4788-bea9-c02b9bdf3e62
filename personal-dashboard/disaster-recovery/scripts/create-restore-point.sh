#!/bin/bash

# 创建项目恢复点脚本
# 用法: ./create-restore-point.sh [类型] [描述]

set -e  # 遇到错误立即退出

# 配置
PROJECT_NAME="personal-dashboard"
BACKUP_DIR="./disaster-recovery/restore-points"
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
TYPE=${1:-"manual"}
DESCRIPTION=${2:-"手动创建的恢复点"}

# 生成恢复点ID
RESTORE_POINT_ID="restore-${TYPE}-${TIMESTAMP}"
RESTORE_POINT_DIR="${BACKUP_DIR}/${RESTORE_POINT_ID}"

echo "🚀 开始创建恢复点: ${RESTORE_POINT_ID}"
echo "📝 描述: ${DESCRIPTION}"

# 创建恢复点目录
mkdir -p "${RESTORE_POINT_DIR}"

# 设置权限
chmod 755 "${RESTORE_POINT_DIR}"

# 1. 备份代码状态
echo "📦 备份代码状态..."
git rev-parse HEAD > "${RESTORE_POINT_DIR}/git-commit.txt"
git status --porcelain > "${RESTORE_POINT_DIR}/git-status.txt"
git diff > "${RESTORE_POINT_DIR}/git-diff.patch"

# 如果有未提交的更改，创建stash
if [ -s "${RESTORE_POINT_DIR}/git-status.txt" ]; then
    echo "⚠️  发现未提交的更改，创建stash..."
    git stash push -m "restore-point-${RESTORE_POINT_ID}" > "${RESTORE_POINT_DIR}/git-stash.txt"
fi

# 2. 备份配置文件
echo "⚙️  备份配置文件..."
mkdir -p "${RESTORE_POINT_DIR}/configs"
cp -r platform-root/ "${RESTORE_POINT_DIR}/configs/" 2>/dev/null || true
cp -r project-tree/ "${RESTORE_POINT_DIR}/configs/" 2>/dev/null || true
cp -r web-interface/package*.json "${RESTORE_POINT_DIR}/configs/" 2>/dev/null || true
cp package*.json "${RESTORE_POINT_DIR}/configs/" 2>/dev/null || true

# 3. 备份环境变量 (加密)
echo "🔐 备份环境变量..."
if [ -f ".env" ]; then
    gpg --symmetric --cipher-algo AES256 --batch --yes --passphrase-file <(echo "backup-key-${PROJECT_NAME}") \
        --output "${RESTORE_POINT_DIR}/env.gpg" .env
fi

# 4. 导出数据库 (如果可用)
echo "🗄️  导出数据库..."
if command -v wrangler &> /dev/null; then
    wrangler d1 export dashboard_db --output "${RESTORE_POINT_DIR}/database.sql" 2>/dev/null || echo "⚠️  数据库导出失败或不可用"
fi

# 5. 导出KV数据 (如果可用)
echo "🔑 导出KV数据..."
if command -v wrangler &> /dev/null && [ -n "$USER_KV_ID" ]; then
    wrangler kv:bulk get --namespace-id "$USER_KV_ID" > "${RESTORE_POINT_DIR}/kv-user-data.json" 2>/dev/null || echo "⚠️  KV数据导出失败或不可用"
fi

# 6. 备份依赖信息
echo "📚 备份依赖信息..."
if [ -f "package-lock.json" ]; then
    cp package-lock.json "${RESTORE_POINT_DIR}/package-lock.json"
fi
if [ -f "web-interface/package-lock.json" ]; then
    cp web-interface/package-lock.json "${RESTORE_POINT_DIR}/web-package-lock.json"
fi

# 7. 备份构建产物 (如果存在)
echo "🏗️  备份构建产物..."
if [ -d "dist" ]; then
    tar -czf "${RESTORE_POINT_DIR}/dist.tar.gz" dist/
fi
if [ -d "web-interface/dist" ]; then
    tar -czf "${RESTORE_POINT_DIR}/web-dist.tar.gz" web-interface/dist/
fi

# 8. 创建恢复点元数据
echo "📋 创建元数据..."
cat > "${RESTORE_POINT_DIR}/metadata.json" << EOF
{
  "id": "${RESTORE_POINT_ID}",
  "type": "${TYPE}",
  "description": "${DESCRIPTION}",
  "created_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "project": "${PROJECT_NAME}",
  "git_commit": "$(cat ${RESTORE_POINT_DIR}/git-commit.txt)",
  "creator": "$(whoami)",
  "hostname": "$(hostname)",
  "node_version": "$(node --version 2>/dev/null || echo 'N/A')",
  "npm_version": "$(npm --version 2>/dev/null || echo 'N/A')",
  "files": [
    "git-commit.txt",
    "git-status.txt", 
    "git-diff.patch",
    "configs/",
    "database.sql",
    "kv-user-data.json",
    "package-lock.json",
    "dist.tar.gz"
  ]
}
EOF

# 9. 创建恢复脚本
echo "🔧 创建恢复脚本..."
cat > "${RESTORE_POINT_DIR}/restore.sh" << 'EOF'
#!/bin/bash
# 自动生成的恢复脚本

set -e
RESTORE_POINT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${RESTORE_POINT_DIR}/../../../" && pwd)"

echo "🔄 开始恢复到恢复点: $(basename ${RESTORE_POINT_DIR})"
cd "${PROJECT_ROOT}"

# 1. 恢复Git状态
if [ -f "${RESTORE_POINT_DIR}/git-commit.txt" ]; then
    COMMIT=$(cat "${RESTORE_POINT_DIR}/git-commit.txt")
    echo "📦 恢复到Git提交: ${COMMIT}"
    git checkout "${COMMIT}"
fi

# 2. 恢复配置文件
echo "⚙️  恢复配置文件..."
cp -r "${RESTORE_POINT_DIR}/configs/platform-root/" . 2>/dev/null || true
cp -r "${RESTORE_POINT_DIR}/configs/project-tree/" . 2>/dev/null || true

# 3. 恢复依赖
if [ -f "${RESTORE_POINT_DIR}/package-lock.json" ]; then
    echo "📚 恢复依赖..."
    cp "${RESTORE_POINT_DIR}/package-lock.json" .
    npm ci
fi

# 4. 恢复数据库
if [ -f "${RESTORE_POINT_DIR}/database.sql" ] && command -v wrangler &> /dev/null; then
    echo "🗄️  恢复数据库..."
    wrangler d1 execute dashboard_db --file="${RESTORE_POINT_DIR}/database.sql"
fi

echo "✅ 恢复完成！"
echo "⚠️  请手动验证系统状态"
EOF

chmod +x "${RESTORE_POINT_DIR}/restore.sh"

# 10. 计算校验和
echo "🔍 计算校验和..."
find "${RESTORE_POINT_DIR}" -type f -exec sha256sum {} \; > "${RESTORE_POINT_DIR}/checksums.txt"

# 11. 压缩恢复点 (可选)
if [ "${COMPRESS:-false}" = "true" ]; then
    echo "🗜️  压缩恢复点..."
    tar -czf "${RESTORE_POINT_DIR}.tar.gz" -C "${BACKUP_DIR}" "$(basename ${RESTORE_POINT_DIR})"
    rm -rf "${RESTORE_POINT_DIR}"
    echo "📦 压缩文件: ${RESTORE_POINT_DIR}.tar.gz"
fi

# 12. 更新恢复点索引
echo "📇 更新恢复点索引..."
RESTORE_POINTS_INDEX="${BACKUP_DIR}/restore-points-index.json"

# 创建或更新索引文件
if [ ! -f "${RESTORE_POINTS_INDEX}" ]; then
    echo "[]" > "${RESTORE_POINTS_INDEX}"
fi

# 添加新恢复点到索引
python3 -c "
import json
import sys
from datetime import datetime

index_file = '${RESTORE_POINTS_INDEX}'
with open(index_file, 'r') as f:
    index = json.load(f)

new_entry = {
    'id': '${RESTORE_POINT_ID}',
    'type': '${TYPE}',
    'description': '${DESCRIPTION}',
    'created_at': '$(date -u +%Y-%m-%dT%H:%M:%SZ)',
    'path': '${RESTORE_POINT_DIR}',
    'compressed': ${COMPRESS:-false}
}

index.append(new_entry)

# 保持最近50个恢复点
index = sorted(index, key=lambda x: x['created_at'], reverse=True)[:50]

with open(index_file, 'w') as f:
    json.dump(index, f, indent=2)
" 2>/dev/null || echo "⚠️  索引更新失败，但恢复点已创建"

# 13. 清理旧的恢复点 (保留最近10个)
echo "🧹 清理旧恢复点..."
ls -t "${BACKUP_DIR}"/restore-* 2>/dev/null | tail -n +11 | xargs rm -rf 2>/dev/null || true

echo ""
echo "✅ 恢复点创建完成！"
echo "📍 恢复点ID: ${RESTORE_POINT_ID}"
echo "📂 位置: ${RESTORE_POINT_DIR}"
echo "🔧 恢复命令: ${RESTORE_POINT_DIR}/restore.sh"
echo ""
echo "📋 恢复点包含:"
echo "   - Git状态和未提交更改"
echo "   - 配置文件"
echo "   - 数据库导出"
echo "   - 依赖锁定文件"
echo "   - 构建产物"
echo ""
echo "⚠️  重要提醒:"
echo "   - 恢复前请备份当前状态"
echo "   - 恢复后请验证系统功能"
echo "   - 敏感数据已加密存储"
