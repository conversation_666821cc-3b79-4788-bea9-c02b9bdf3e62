﻿# PowerShell restore script
$RestorePointDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent (Split-Path -Parent (Split-Path -Parent $RestorePointDir))

Write-Host "Restoring to restore point: $RestorePointDir" -ForegroundColor Green
Set-Location $ProjectRoot

# Restore Git status
if (Test-Path "$RestorePointDir\git-commit.txt") {
    $Commit = Get-Content "$RestorePointDir\git-commit.txt" -Raw
    Write-Host "Restoring to Git commit: $Commit" -ForegroundColor Cyan
    git checkout $Commit.Trim()
}

# Restore configuration files
Write-Host "Restoring configuration files..." -ForegroundColor Cyan
if (Test-Path "$RestorePointDir\configs\platform-root") {
    Copy-Item -Path "$RestorePointDir\configs\platform-root" -Destination "." -Recurse -Force
}
if (Test-Path "$RestorePointDir\configs\project-tree") {
    Copy-Item -Path "$RestorePointDir\configs\project-tree" -Destination "." -Recurse -Force
}

# Restore dependencies
if (Test-Path "$RestorePointDir\package-lock.json") {
    Write-Host "Restoring dependencies..." -ForegroundColor Cyan
    Copy-Item -Path "$RestorePointDir\package-lock.json" -Destination "." -Force
    npm ci
}

Write-Host "Restore completed!" -ForegroundColor Green
Write-Host "Please verify system status manually" -ForegroundColor Yellow
