# 🧠 思维模型功能使用指南

## 🎯 功能概述

**思维模型页面**是dev-daily v2的创新功能，集成了多种经典的思维分析方法，为用户提供多维度的问题分析和决策支持。

### ✨ 核心特色
- **多模型并行分析**: 一次输入，同时获得6种思维模型的分析结果
- **AI驱动**: 基于OpenAI GPT模型的智能分析
- **可视化展示**: 每种模型都有专门设计的可视化界面
- **结果导出**: 支持分析结果的导出和保存

## 🛠️ 支持的思维模型

### 1. 🎭 六顶思考帽 (Six Thinking Hats)
**创始人**: <PERSON> de <PERSON>o  
**用途**: 多角度思考分析

#### 六个思考角度
- **🤍 白帽**: 客观事实和数据分析
- **❤️ 红帽**: 情感和直觉反应
- **🖤 黑帽**: 风险和问题识别
- **💛 黄帽**: 积极面和机会发现
- **💚 绿帽**: 创意和替代方案
- **💙 蓝帽**: 思考过程管理和总结

#### 适用场景
- 产品功能评估
- 团队决策讨论
- 项目风险评估
- 创新方案设计

### 2. 🎯 SWOT分析
**用途**: 内外部环境综合分析

#### 四个维度
- **💪 优势 (Strengths)**: 内部优势和竞争力
- **⚠️ 劣势 (Weaknesses)**: 内部不足和改进点
- **🚀 机会 (Opportunities)**: 外部机会和发展空间
- **⚡ 威胁 (Threats)**: 外部威胁和挑战

#### 适用场景
- 项目可行性分析
- 竞争策略制定
- 资源配置决策
- 市场进入评估

### 3. ❓ 5W1H分析
**用途**: 全面问题分析框架

#### 六个关键问题
- **❓ What**: 具体是什么问题或目标
- **🤔 Why**: 为什么要解决这个问题
- **⏰ When**: 时间安排和执行节点
- **📍 Where**: 执行地点或影响范围
- **👤 Who**: 相关人员和责任分工
- **⚙️ How**: 具体实施方法和步骤

#### 适用场景
- 项目规划设计
- 问题根因分析
- 流程梳理优化
- 任务分解执行

### 4. 🌍 PEST分析
**用途**: 外部环境因素分析

#### 四个环境因素
- **🏛️ 政治 (Political)**: 政策法规影响
- **💰 经济 (Economic)**: 经济环境因素
- **👥 社会 (Social)**: 社会文化趋势
- **🔬 技术 (Technological)**: 技术发展趋势

#### 适用场景
- 长期战略规划
- 市场环境评估
- 投资决策分析
- 风险预警系统

### 5. 📊 决策矩阵
**用途**: 多标准决策分析

#### 分析要素
- **评估标准**: 决策的关键指标
- **可选方案**: 不同的选择选项
- **权重分析**: 各标准的重要程度
- **综合评分**: 量化决策结果

#### 适用场景
- 技术方案选择
- 供应商评选
- 投资项目比较
- 人员招聘决策

### 6. 🔍 根因分析
**用途**: 问题根本原因探索

#### 分析层次
- **问题描述**: 明确问题现象
- **表面症状**: 可观察的问题表现
- **直接原因**: 导致问题的直接因素
- **根本原因**: 问题的深层次原因
- **解决方案**: 针对性的改进措施

#### 适用场景
- 质量问题分析
- 流程故障诊断
- 绩效问题改进
- 系统优化设计

## 🚀 使用方法

### 步骤1: 配置AI服务
1. 进入**设置页面**
2. 在**AI设置**中配置OpenAI API密钥
3. 选择合适的AI模型（推荐GPT-3.5 Turbo或GPT-4）
4. 测试API连接确保正常

### 步骤2: 输入分析主题
1. 进入**思维模型页面**
2. 在左侧输入框中描述要分析的问题或主题
3. 主题可以是：
   - 产品功能决策
   - 项目风险评估
   - 技术方案选择
   - 团队管理问题
   - 业务发展策略

### 步骤3: 开始分析
1. 点击**开始分析**按钮
2. AI将并行使用6种思维模型进行分析
3. 分析过程通常需要30-60秒
4. 系统会显示分析进度

### 步骤4: 查看结果
1. 分析完成后，左侧模型列表会显示✅标记
2. 点击不同的思维模型查看对应结果
3. 每种模型都有专门的可视化展示
4. 可以在不同模型间切换对比

### 步骤5: 导出结果
1. 点击**导出分析结果**按钮
2. 系统会生成JSON格式的完整分析报告
3. 包含所有模型的分析结果和元数据
4. 可用于后续参考或团队分享

## 💡 使用技巧

### 主题描述技巧
- **具体明确**: 避免过于宽泛的描述
- **包含背景**: 提供必要的上下文信息
- **明确目标**: 说明希望解决的具体问题
- **适当长度**: 建议50-200字的描述

### 示例主题
```
✅ 好的主题描述：
"我们正在考虑为dev-daily项目添加实时协作功能，允许多个用户同时编辑项目树。这个功能需要投入3个月开发时间，但可能提升用户粘性。请分析这个功能的可行性。"

❌ 不好的主题描述：
"项目功能"
```

### 结果解读技巧
- **对比分析**: 查看不同模型的结果差异
- **重点关注**: 注意多个模型都提到的关键点
- **平衡考虑**: 综合积极和消极的分析结果
- **行动导向**: 重点关注可执行的建议

## 🎯 应用场景示例

### 场景1: 产品功能决策
**主题**: "是否应该在下个版本中添加暗色主题功能？"

**分析价值**:
- 六顶思考帽：多角度评估用户需求和实现难度
- SWOT分析：评估功能的优势劣势和市场机会
- 5W1H分析：明确实施计划和资源需求
- 决策矩阵：量化评估投入产出比

### 场景2: 技术架构选择
**主题**: "微服务架构 vs 单体架构，哪个更适合我们的项目？"

**分析价值**:
- PEST分析：评估技术趋势和环境因素
- SWOT分析：对比两种架构的优劣势
- 决策矩阵：基于性能、维护性、扩展性等标准评分
- 根因分析：深入分析架构选择的根本考虑因素

### 场景3: 团队管理问题
**主题**: "团队开发效率低下，经常延期交付，如何改进？"

**分析价值**:
- 根因分析：找出效率低下的根本原因
- 5W1H分析：制定具体的改进计划
- 六顶思考帽：从多个角度思考解决方案
- SWOT分析：评估团队现状和改进机会

## 📊 功能优势

### 1. 🎯 多维度分析
- 一次输入获得6种不同视角的分析
- 避免单一思维模式的局限性
- 提供更全面的决策依据

### 2. 🤖 AI驱动智能
- 基于先进的GPT模型
- 结合项目上下文的个性化分析
- 持续学习和优化分析质量

### 3. 🎨 可视化展示
- 每种模型都有专门的界面设计
- 直观的颜色编码和图标系统
- 便于理解和记忆的信息组织

### 4. 💾 结果管理
- 支持分析结果的导出保存
- 便于团队分享和后续参考
- 建立决策过程的可追溯记录

### 5. ⚡ 高效便捷
- 简单的操作流程
- 快速的分析响应
- 无需学习复杂的分析方法

## 🔮 未来扩展

### 计划中的新模型
- **鱼骨图分析**: 因果关系可视化
- **力场分析**: 推动和阻碍因素分析
- **价值链分析**: 业务流程价值分析
- **波特五力**: 竞争环境分析
- **平衡计分卡**: 绩效管理分析

### 功能增强
- **模板库**: 预设的分析主题模板
- **协作功能**: 团队共同分析和讨论
- **历史记录**: 分析历史的管理和搜索
- **智能推荐**: 基于主题推荐合适的模型

---

**思维模型功能让dev-daily v2成为真正的智能决策支持工具！** 🌟

通过多种经典思维方法的结合，为用户提供全面、深入、可操作的分析建议，大大提升决策质量和效率。
