# Dev-Daily V2 设置指南

## 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/justpm2099/dev-daily-v2.git
cd dev-daily-v2
```

### 2. 安装依赖
```bash
cd dev-daily-v2
npm install
```

### 3. 配置环境变量
```bash
# 复制环境变量示例文件
cp .env.example .env

# 编辑 .env 文件，填入您的API密钥
# VITE_OPENAI_API_KEY=sk-proj-your-actual-api-key-here
```

### 4. 启动开发服务器
```bash
npm run dev
```

## API密钥配置

### OpenAI API密钥
1. 访问 [OpenAI Platform](https://platform.openai.com/api-keys)
2. 创建新的API密钥
3. 将密钥添加到 `.env` 文件中的 `VITE_OPENAI_API_KEY`

### Cloudflare配置
1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 获取您的Account ID和API Token
3. 将配置添加到 `.env` 文件中

## 部署到Cloudflare Pages

### 使用Wrangler CLI
```bash
# 安装Wrangler
npm install -g wrangler

# 登录Cloudflare
wrangler login

# 部署项目
npm run build
wrangler pages deploy dist --project-name=dev-daily-v2
```

## 项目结构

```
dev-daily-v2/
├── src/                    # 源代码
│   ├── components/         # React组件
│   ├── services/          # 服务层
│   └── utils/             # 工具函数
├── docs/                  # 文档
├── scripts/               # 脚本文件
└── dist/                  # 构建输出
```

## 功能特性

- 📊 项目管理和进度跟踪
- 🤖 AI助手集成（OpenAI）
- 🧠 思维模型分析工具
- ☁️ Cloudflare部署支持
- 📱 响应式设计

## 注意事项

⚠️ **重要**: 请勿将API密钥提交到版本控制系统中！
- 使用 `.env` 文件存储敏感信息
- `.env` 文件已被添加到 `.gitignore` 中
- 使用 `.env.example` 作为配置模板
